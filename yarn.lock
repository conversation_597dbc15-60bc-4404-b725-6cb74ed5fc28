# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@algolia/autocomplete-core@npm:1.17.9":
  version: 1.17.9
  resolution: "@algolia/autocomplete-core@npm:1.17.9"
  dependencies:
    "@algolia/autocomplete-plugin-algolia-insights": 1.17.9
    "@algolia/autocomplete-shared": 1.17.9
  checksum: dde242b1a2d8485e6c7bc94d00e25d707aa66dcd276ee1dde13213f1620bf6a1d289a61c657e40c707ca726a8aa009ab5e8229f92ae5cf22266de490b0634d20
  languageName: node
  linkType: hard

"@algolia/autocomplete-plugin-algolia-insights@npm:1.17.9":
  version: 1.17.9
  resolution: "@algolia/autocomplete-plugin-algolia-insights@npm:1.17.9"
  dependencies:
    "@algolia/autocomplete-shared": 1.17.9
  peerDependencies:
    search-insights: ">= 1 < 3"
  checksum: 32761d44a407d7c5ecfae98bb78b45a1ca85c59f44167ea36057315fb357c49684e9126bb7a67a513a27bda60a9661cecd6215f2daa903288860201b0b18c745
  languageName: node
  linkType: hard

"@algolia/autocomplete-preset-algolia@npm:1.17.9":
  version: 1.17.9
  resolution: "@algolia/autocomplete-preset-algolia@npm:1.17.9"
  dependencies:
    "@algolia/autocomplete-shared": 1.17.9
  peerDependencies:
    "@algolia/client-search": ">= 4.9.1 < 6"
    algoliasearch: ">= 4.9.1 < 6"
  checksum: 0dac2aae02121d37466b4ce1ca533420b25cd70e218a9e645e6194bd84a6012a0e94c22125437adb89599ecf14e4488882f91da382c6c9a8d9447e929b317522
  languageName: node
  linkType: hard

"@algolia/autocomplete-shared@npm:1.17.9":
  version: 1.17.9
  resolution: "@algolia/autocomplete-shared@npm:1.17.9"
  peerDependencies:
    "@algolia/client-search": ">= 4.9.1 < 6"
    algoliasearch: ">= 4.9.1 < 6"
  checksum: f16223f5995db0deb014a066e3587ec2da76e62b861aa21411be92cb255b7023507803283803d8c960b396a2c6b690951337c32fef34f68c59ecfb3822dee577
  languageName: node
  linkType: hard

"@algolia/client-abtesting@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/client-abtesting@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: 24b3f520f32d4d9e68dc9de666aeacadf1661ccf71d0bde4b8f234b350f20388defa3fa9c39d92da7c74663acd13d9d78eed557f8fb514b72a3fb5185ede7a62
  languageName: node
  linkType: hard

"@algolia/client-analytics@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/client-analytics@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: ee12c71508c17abdb15d6043c8323b96ca5ff9d6d10b2a4f0529a02a1f788d19cd3157f63ede4cdc37e141204f5a9ed5fb3f002eb1487f4dd28bc758fdafe362
  languageName: node
  linkType: hard

"@algolia/client-common@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/client-common@npm:5.20.1"
  checksum: 0e427f7dbfd526e6b12314f5f1a51e576dac723d03d1ccb86d1cc5c7ce8a10daed196beaae2a4ecf7a54db0172e0b5c7d7b613e90e8b907fbc433c3f092df040
  languageName: node
  linkType: hard

"@algolia/client-insights@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/client-insights@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: b94f8e7d81b0e15975516194b0fc3d288cb9a692a5706d94a408d354492b2b8c712e42e9d5e8ac6fecdf3595b38a5e92bc6920501503d834c6a9954edb044c8e
  languageName: node
  linkType: hard

"@algolia/client-personalization@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/client-personalization@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: d10c3b56e51d6dce7b939c1de510b00f68b10eae2dd9a670c93ff703f4f83d500c760db1a3d226feddbad3aaa769b35b33c3fc2d5a62d0a7ec3afed98c64bb01
  languageName: node
  linkType: hard

"@algolia/client-query-suggestions@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/client-query-suggestions@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: 4c0cd6e2e783773103d87e78a7fc19d9a2c555112bb482b13493416a4de6ff198a8112b3caad29ac9b7408088c23c34eff52bb3bc645bbcaa9ab1b3de8219321
  languageName: node
  linkType: hard

"@algolia/client-search@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/client-search@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: b95bd16771052449d8d4ed5d23df6b9f83b9c91e73c689cd56dd99166f9e6d728516fd5ea1f1b1004cebe45208adf513388ac798365caa3edee877b68cf3497c
  languageName: node
  linkType: hard

"@algolia/ingestion@npm:1.20.1":
  version: 1.20.1
  resolution: "@algolia/ingestion@npm:1.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: fa92996039a25470967c64c164b837b6b4f4953d333dfe9a775fbf78893479436e6064a9642a51b003f311db14c73572583ed537cec5749256439877e21cfb05
  languageName: node
  linkType: hard

"@algolia/monitoring@npm:1.20.1":
  version: 1.20.1
  resolution: "@algolia/monitoring@npm:1.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: e1092722d8a3476b24ce4d0effe817e832363e556a2c6027cab8d6c3d918ae5d31546d6f4625041284b1e505c8400c048d34074bca0f2fc92b0383fb4942da8c
  languageName: node
  linkType: hard

"@algolia/recommend@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/recommend@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: 97d0aa50ce1e7ecf1878542052199caa069519e15473bf40a85f90fb3349777fdf7b2cf7c2bdfe5d41a10209201489ed6b385bfecaf4fc117511b3e899211fce
  languageName: node
  linkType: hard

"@algolia/requester-browser-xhr@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/requester-browser-xhr@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
  checksum: 0a7fbe3316df4cc1f7304aaee03335a3ace59839c1adedaa415aee963fd9714b1c56b72284b723ba2c5b202380251f28cea3a9e0a154b96e2815d0ac6a12eab1
  languageName: node
  linkType: hard

"@algolia/requester-fetch@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/requester-fetch@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
  checksum: 28b4c73e38c661c67c0bdabe42649c1cb8ea5c4cc039b80977cffac71df68bdb8b2efa76818067847a2538beaa458c7a4c4875010efa2dbaec021a1a8a0ecdcc
  languageName: node
  linkType: hard

"@algolia/requester-node-http@npm:5.20.1":
  version: 5.20.1
  resolution: "@algolia/requester-node-http@npm:5.20.1"
  dependencies:
    "@algolia/client-common": 5.20.1
  checksum: db3b308c315e92dd1a7aa3215daf2e90d76dc374d3a11d25eb59b679bbf9588e3cd0514fc0145e586f46d75ad806d6a27cfe4f0496d7ff3a39bb8efa0ce64b8c
  languageName: node
  linkType: hard

"@alloc/quick-lru@npm:^5.2.0":
  version: 5.2.0
  resolution: "@alloc/quick-lru@npm:5.2.0"
  checksum: bdc35758b552bcf045733ac047fb7f9a07c4678b944c641adfbd41f798b4b91fffd0fdc0df2578d9b0afc7b4d636aa6e110ead5d6281a2adc1ab90efd7f057f8
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: d3ad7b89d973df059c4e8e6d7c972cbeb1bb2f18f002a3bd04ae0707da214cb06cc06929b65aa2313b9347463df2914772298bae8b1d7973f246bb3f2ab3e8f0
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.26.2":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": ^7.25.9
    js-tokens: ^4.0.0
    picocolors: ^1.0.0
  checksum: db13f5c42d54b76c1480916485e6900748bbcb0014a8aca87f50a091f70ff4e0d0a6db63cade75eb41fcc3d2b6ba0a7f89e343def4f96f00269b41b8ab8dd7b8
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.26.5, @babel/compat-data@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/compat-data@npm:7.26.8"
  checksum: 1bb04c6860c8c9555b933cb9c3caf5ef1dac331a37a351efb67956fc679f695d487aea76e792dd43823702c1300f7906f2a298e50b4a8d7ec199ada9c340c365
  languageName: node
  linkType: hard

"@babel/core@npm:^7.21.3":
  version: 7.26.8
  resolution: "@babel/core@npm:7.26.8"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.26.2
    "@babel/generator": ^7.26.8
    "@babel/helper-compilation-targets": ^7.26.5
    "@babel/helper-module-transforms": ^7.26.0
    "@babel/helpers": ^7.26.7
    "@babel/parser": ^7.26.8
    "@babel/template": ^7.26.8
    "@babel/traverse": ^7.26.8
    "@babel/types": ^7.26.8
    "@types/gensync": ^1.0.0
    convert-source-map: ^2.0.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: 9d83fb7ad33467fc5ed841d24158d01b7c486ad399d7988232ab9edc6d9f92cd4d60b598ca717aeeb136feb48f7e289c247663c6a28e85dee92a39b2e97cc2e1
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/generator@npm:7.26.8"
  dependencies:
    "@babel/parser": ^7.26.8
    "@babel/types": ^7.26.8
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
    jsesc: ^3.0.2
  checksum: 15ef65699a556f1c75edba52109e65a597a3e16da2faf117d617e67b667983d5e3cd11399a1d6ff9ff1b0029f8e7c9513975884704b6c2d13bba3d780456823d
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-annotate-as-pure@npm:7.25.9"
  dependencies:
    "@babel/types": ^7.25.9
  checksum: 41edda10df1ae106a9b4fe617bf7c6df77db992992afd46192534f5cff29f9e49a303231733782dd65c5f9409714a529f215325569f14282046e9d3b7a1ffb6c
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.25.9, @babel/helper-compilation-targets@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/helper-compilation-targets@npm:7.26.5"
  dependencies:
    "@babel/compat-data": ^7.26.5
    "@babel/helper-validator-option": ^7.25.9
    browserslist: ^4.24.0
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: 6bc0107613bf1d4d21913606e8e517194e5099a24db2a8374568e56ef4626e8140f9b8f8a4aabc35479f5904459a0aead2a91ee0dc63aae110ccbc2bc4b4fda1
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-create-class-features-plugin@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-member-expression-to-functions": ^7.25.9
    "@babel/helper-optimise-call-expression": ^7.25.9
    "@babel/helper-replace-supers": ^7.25.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.25.9
    "@babel/traverse": ^7.25.9
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 91dd5f203ed04568c70b052e2f26dfaac7c146447196c00b8ecbb6d3d2f3b517abadb985d3321a19d143adaed6fe17f7f79f8f50e0c20e9d8ad83e1027b42424
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.25.9":
  version: 7.26.3
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.26.3"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    regexpu-core: ^6.2.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 50a27d8ce6da5c2fa0c62c132c4d27cfeb36e3233ff1e5220d643de3dafe49423b507382f0b72a696fce7486014b134c1e742f55438590f9405d26765b009af0
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.3":
  version: 0.6.3
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.3"
  dependencies:
    "@babel/helper-compilation-targets": ^7.22.6
    "@babel/helper-plugin-utils": ^7.22.5
    debug: ^4.1.1
    lodash.debounce: ^4.0.8
    resolve: ^1.14.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 710e6d8a5391736b9f53f09d0494575c2e03de199ad8d1349bc8e514cb85251ea1f1842c2ff44830849d482052ddb42ae931101002a87a263b12f649c2e57c01
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-member-expression-to-functions@npm:7.25.9"
  dependencies:
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: 8e2f1979b6d596ac2a8cbf17f2cf709180fefc274ac3331408b48203fe19134ed87800774ef18838d0275c3965130bae22980d90caed756b7493631d4b2cf961
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-module-imports@npm:7.25.9"
  dependencies:
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: 1b411ce4ca825422ef7065dffae7d8acef52023e51ad096351e3e2c05837e9bf9fca2af9ca7f28dc26d596a588863d0fedd40711a88e350b736c619a80e704e6
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.25.9, @babel/helper-module-transforms@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/helper-module-transforms@npm:7.26.0"
  dependencies:
    "@babel/helper-module-imports": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 942eee3adf2b387443c247a2c190c17c4fd45ba92a23087abab4c804f40541790d51ad5277e4b5b1ed8d5ba5b62de73857446b7742f835c18ebd350384e63917
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-optimise-call-expression@npm:7.25.9"
  dependencies:
    "@babel/types": ^7.25.9
  checksum: f09d0ad60c0715b9a60c31841b3246b47d67650c512ce85bbe24a3124f1a4d66377df793af393273bc6e1015b0a9c799626c48e53747581c1582b99167cc65dc
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.25.9, @babel/helper-plugin-utils@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/helper-plugin-utils@npm:7.26.5"
  checksum: 4771fbb1711c624c62d12deabc2ed7435a6e6994b6ce09d5ede1bc1bf19be59c3775461a1e693bdd596af865685e87bb2abc778f62ceadc1b2095a8e2aa74180
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-remap-async-to-generator@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-wrap-function": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: ea37ad9f8f7bcc27c109963b8ebb9d22bac7a5db2a51de199cb560e251d5593fe721e46aab2ca7d3e7a24b0aa4aff0eaf9c7307af9c2fd3a1d84268579073052
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.25.9":
  version: 7.26.5
  resolution: "@babel/helper-replace-supers@npm:7.26.5"
  dependencies:
    "@babel/helper-member-expression-to-functions": ^7.25.9
    "@babel/helper-optimise-call-expression": ^7.25.9
    "@babel/traverse": ^7.26.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c5ab31b29c7cc09e30278f8860ecdb873ce6c84b5c08bc5239c369c7c4fe9f0a63cda61b55b7bbd20edb4e5dc32e73087cc3c57d85264834bd191551d1499185
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.25.9"
  dependencies:
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: fdbb5248932198bc26daa6abf0d2ac42cab9c2dbb75b7e9f40d425c8f28f09620b886d40e7f9e4e08ffc7aaa2cefe6fc2c44be7c20e81f7526634702fb615bdc
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 6435ee0849e101681c1849868278b5aee82686ba2c1e27280e5e8aca6233af6810d39f8e4e693d2f2a44a3728a6ccfd66f72d71826a94105b86b731697cdfa99
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 5b85918cb1a92a7f3f508ea02699e8d2422fe17ea8e82acd445006c0ef7520fbf48e3dbcdaf7b0a1d571fc3a2715a29719e5226636cb6042e15fe6ed2a590944
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-option@npm:7.25.9"
  checksum: 9491b2755948ebbdd68f87da907283698e663b5af2d2b1b02a2765761974b1120d5d8d49e9175b167f16f72748ffceec8c9cf62acfbee73f4904507b246e2b3d
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-wrap-function@npm:7.25.9"
  dependencies:
    "@babel/template": ^7.25.9
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: 8ec1701e60ae004415800c4a7a188f5564c73b4e4f3fdf58dd3f34a3feaa9753173f39bbd6d02e7ecc974f48155efc7940e62584435b3092c07728ee46a604ea
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.26.7":
  version: 7.26.7
  resolution: "@babel/helpers@npm:7.26.7"
  dependencies:
    "@babel/template": ^7.25.9
    "@babel/types": ^7.26.7
  checksum: 1c93604c7fd6dbd7aa6f3eb2f9fa56369f9ad02bac8b3afb902de6cd4264beb443cc8589bede3790ca28d7477d4c07801fe6f4943f9833ac5956b72708bbd7ac
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/parser@npm:7.26.8"
  dependencies:
    "@babel/types": ^7.26.8
  bin:
    parser: ./bin/babel-parser.js
  checksum: 2ede62d2451eaf37f524f2048ca41994466c81bda1f5acec36fbd8931fe77bf365e2b2060972735165e40aec305e04af76dd4d8fa895bc08a250215b32356577
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: b33d37dacf98a9c74f53959999adc37a258057668b62dba557e6865689433c53764673109eaba9102bf73b2ac4db162f0d9b89a6cca6f1b71d12f5908ec11da9
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: d3e14ab1cb9cb50246d20cab9539f2fbd1e7ef1ded73980c8ad7c0561b4d5e0b144d362225f0976d47898e04cbd40f2000e208b0913bd788346cf7791b96af91
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a9d1ee3fd100d3eb6799a2f2bbd785296f356c531d75c9369f71541811fa324270258a374db103ce159156d006da2f33370330558d0133e6f7584152c34997ca
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.25.9
    "@babel/plugin-transform-optional-chaining": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 5b298b28e156f64de51cdb03a2c5b80c7f978815ef1026f3ae8b9fc48d28bf0a83817d8fbecb61ef8fb94a7201f62cca5103cc6e7b9e8f28e38f766d7905b378
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c684593952ab1b40dfa4e64e98a07e7227c6db175c21bd0e6d71d2ad5d240fef4e4a984d56f05a494876542a022244fe1c1098f4116109fd90d06615e8a269b1
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d97745d098b835d55033ff3a7fb2b895b9c5295b08a5759e4f20df325aa385a3e0bc9bd5ad8f2ec554a44d4e6525acfc257b8c5848a1345cb40f26a30e277e91
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b58f2306df4a690ca90b763d832ec05202c50af787158ff8b50cdf3354359710bce2e1eb2b5135fcabf284756ac8eadf09ca74764aa7e76d12a5cac5f6b21e67
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c122aa577166c80ee67f75aebebeef4150a132c4d3109d25d7fc058bf802946f883e330f20b78c1d3e3a5ada631c8780c263d2d01b5dbaecc69efefeedd42916
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-syntax-jsx@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bb609d1ffb50b58f0c1bac8810d0e46a4f6c922aa171c458f3a19d66ee545d36e782d3bffbbc1fed0dc65a558bdce1caf5279316583c0fff5a2c1658982a8563
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-syntax-typescript@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0e9821e8ba7d660c36c919654e4144a70546942ae184e85b8102f2322451eae102cbfadbcadd52ce077a2b44b400ee52394c616feab7b5b9f791b910e933fd33
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c29f081224859483accf55fb4d091db2aac0dcd0d7954bac5ca889030cc498d3f771aa20eb2e9cd8310084ec394d85fa084b97faf09298b6bc9541182b3eb5bb
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.26.8"
  dependencies:
    "@babel/helper-plugin-utils": ^7.26.5
    "@babel/helper-remap-async-to-generator": ^7.25.9
    "@babel/traverse": ^7.26.8
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10424a1bbfbc7ffdb13cef1e832f76bb2d393a9fbfaa1eaa3091a8f6ec3e2ac0b66cf04fca9cb3fb4dbf3d1bd404d72dfce4a3742b4ef21f6271aca7076a65ef
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.25.9"
  dependencies:
    "@babel/helper-module-imports": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-remap-async-to-generator": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b3ad50fb93c171644d501864620ed23952a46648c4df10dc9c62cc9ad08031b66bd272cfdd708faeee07c23b6251b16f29ce0350473e4c79f0c32178d38ce3a6
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.26.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.26.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f2046c09bf8e588bfb1a6342d0eee733189102cf663ade27adb0130f3865123af5816b40a55ec8d8fa09271b54dfdaf977cd2f8e0b3dc97f18e690188d5a2174
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-block-scoping@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e869500cfb1995e06e64c9608543b56468639809febfcdd6fcf683bc0bf1be2431cacf2981a168a1a14f4766393e37bc9f7c96d25bc5b5f39a64a8a8ad0bf8e0
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-class-properties@npm:7.25.9"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a8d69e2c285486b63f49193cbcf7a15e1d3a5f632c1c07d7a97f65306df7f554b30270b7378dde143f8b557d1f8f6336c643377943dec8ec405e4cd11e90b9ea
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-transform-class-static-block@npm:7.26.0"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: d779d4d3a6f8d363f67fcbd928c15baa72be8d3b86c6d05e0300b50e66e2c4be9e99398b803d13064bc79d90ae36e37a505e3dc8af11904459804dec07660246
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-classes@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-compilation-targets": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-replace-supers": ^7.25.9
    "@babel/traverse": ^7.25.9
    globals: ^11.1.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d12584f72125314cc0fa8c77586ece2888d677788ac75f7393f5da574dfe4e45a556f7e3488fab29c8777ab3e5856d7a2d79f6df02834083aaa9d766440e3c68
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-computed-properties@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/template": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f77fa4bc0c1e0031068172df28852388db6b0f91c268d037905f459607cf1e8ebab00015f9f179f4ad96e11c5f381b635cd5dc4e147a48c7ac79d195ae7542de
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-destructuring@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 965f63077a904828f4adee91393f83644098533442b8217d5a135c23a759a4c252c714074c965676a60d2c33f610f579a4eeb59ffd783724393af61c0ca45fef
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8bdf1bb9e6e3a2cc8154ae88a3872faa6dc346d6901994505fb43ac85f858728781f1219f40b67f7bb0687c507450236cb7838ac68d457e65637f98500aa161b
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b553eebc328797ead6be5ba5bdaf2f1222cea8a5bd33fb4ed625975d4f9b510bfb0d688d97e314cd4b4a48b279bea7b3634ad68c1b41ee143c3082db0ae74037
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: f7233cf596be8c6843d31951afaf2464a62a610cb89c72c818c044765827fab78403ab8a7d3a6386f838c8df574668e2a48f6c206b1d7da965aff9c6886cb8e6
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aaca1ccda819be9b2b85af47ba08ddd2210ff2dbea222f26e4cd33f97ab020884bf81a66197e50872721e9daf36ceb5659502c82199884ea74d5d75ecda5c58b
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.26.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b369ffad07e02e259c43a09d309a5ca86cb9da6b43b1df6256463a810b172cedc4254742605eec0fc2418371c3f7430430f5abd36f21717281e79142308c13ba
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4dfe8df86c5b1d085d591290874bb2d78a9063090d71567ed657a418010ad333c3f48af2c974b865f53bbb718987a065f89828d43279a7751db1a56c9229078d
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-for-of@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 41b56e70256a29fc26ed7fb95ece062d7ec2f3b6ea8f0686349ffd004cd4816132085ee21165b89c502ee7161cb7cfb12510961638851357945dc7bc546475b7
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-function-name@npm:7.25.9"
  dependencies:
    "@babel/helper-compilation-targets": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a8d7c8d019a6eb57eab5ca1be3e3236f175557d55b1f3b11f8ad7999e3fbb1cf37905fd8cb3a349bffb4163a558e9f33b63f631597fdc97c858757deac1b2fd7
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-json-strings@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e2498d84761cfd05aaea53799933d55af309c9d6204e66b38778792d171e4d1311ad34f334259a3aa3407dd0446f6bd3e390a1fcb8ce2e42fe5aabed0e41bee1
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3cca75823a38aab599bc151b0fa4d816b5e1b62d6e49c156aa90436deb6e13649f5505973151a10418b64f3f9d1c3da53e38a186402e0ed7ad98e482e70c0c14
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8c6febb4ac53852314d28b5e2c23d5dbbff7bf1e57d61f9672e0d97531ef7778b3f0ad698dcf1179f5486e626c77127508916a65eb846a89e98a92f70ed3537b
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: db92041ae87b8f59f98b50359e0bb172480f6ba22e5e76b13bdfe07122cbf0daa9cd8ad2e78dcb47939938fed88ad57ab5989346f64b3a16953fc73dea3a9b1f
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-modules-amd@npm:7.25.9"
  dependencies:
    "@babel/helper-module-transforms": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: baad1f6fd0e0d38e9a9c1086a06abdc014c4c653fd452337cadfe23fb5bd8bf4368d1bc433a5ac8e6421bc0732ebb7c044cf3fb39c1b7ebe967d66e26c4e5cec
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.25.9, @babel/plugin-transform-modules-commonjs@npm:^7.26.3":
  version: 7.26.3
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.26.3"
  dependencies:
    "@babel/helper-module-transforms": ^7.26.0
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0ac9aa4e5fe9fe34b58ee174881631e5e1c89eee5b1ebfd1147934686be92fc5fbfdc11119f0b607b3743d36a1cbcb7c36f18e0dd4424d6d7b749b1b9a18808a
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.25.9"
  dependencies:
    "@babel/helper-module-transforms": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf446202f372ba92dc0db32b24b56225b6e3ad3b227e31074de8b86fdec01c273ae2536873e38dbe3ceb1cd0894209343adeaa37df208e3fa88c0c7dffec7924
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-modules-umd@npm:7.25.9"
  dependencies:
    "@babel/helper-module-transforms": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 946db66be5f04ab9ee56c424b00257276ec094aa2f148508927e6085239f76b00304fa1e33026d29eccdbe312efea15ca3d92e74a12689d7f0cdd9a7ba1a6c54
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 434346ba05cf74e3f4704b3bdd439287b95cd2a8676afcdc607810b8c38b6f4798cd69c1419726b2e4c7204e62e4a04d31b0360e91ca57a930521c9211e07789
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-new-target@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f8113539919aafce52f07b2bd182c771a476fe1d5d96d813460b33a16f173f038929369c595572cadc1f7bd8cb816ce89439d056e007770ddd7b7a0878e7895f
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.26.6":
  version: 7.26.6
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.26.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.26.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 752837d532b85c41f6bb868e83809605f513bc9a3b8e88ac3d43757c9bf839af4f246874c1c6d6902bb2844d355efccae602c3856098911f8abdd603672f8379
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0528ef041ed88e8c3f51624ee87b8182a7f246fe4013f0572788e0727d20795b558f2b82e3989b5dd416cbd339500f0d88857de41b6d3b6fdacb1d5344bcc5b1
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.25.9"
  dependencies:
    "@babel/helper-compilation-targets": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/plugin-transform-parameters": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a8ff73e1c46a03056b3a2236bafd6b3a4b83da93afe7ee24a50d0a8088150bf85bc5e5977daa04e66ff5fb7613d02d63ad49b91ebb64cf3f3022598d722e3a7a
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-object-super@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-replace-supers": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1817b5d8b80e451ae1ad9080cca884f4f16df75880a158947df76a2ed8ab404d567a7dce71dd8051ef95f90fbe3513154086a32aba55cc76027f6cbabfbd7f98
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b46a8d1e91829f3db5c252583eb00d05a779b4660abeea5500fda0f8ffa3584fd18299443c22f7fddf0ed9dfdb73c782c43b445dc468d4f89803f2356963b406
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f1642a7094456067e82b176e1e9fd426fda7ed9df54cb6d10109fc512b622bf4b3c83acc5875125732b8622565107fdbe2d60fe3ec8685e1d1c22c38c1b57782
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-parameters@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d7ba2a7d05edbc85aed741289b0ff3d6289a1c25d82ac4be32c565f88a66391f46631aad59ceeed40824037f7eeaa7a0de1998db491f50e65a565cd964f78786
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-private-methods@npm:7.25.9"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6e3671b352c267847c53a170a1937210fa8151764d70d25005e711ef9b21969aaf422acc14f9f7fb86bc0e4ec43e7aefcc0ad9196ae02d262ec10f509f126a58
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-create-class-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9ce3e983fea9b9ba677c192aa065c0b42ebdc7774be4c02135df09029ad92a55c35b004650c75952cb64d650872ed18f13ab64422c6fc891d06333762caa8a0a
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-property-literals@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 436046ab07d54a9b44a384eeffec701d4e959a37a7547dda72e069e751ca7ff753d1782a8339e354b97c78a868b49ea97bf41bf5a44c6d7a3c0a05ad40eeb49c
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-constant-elements@npm:^7.21.3":
  version: 7.25.9
  resolution: "@babel/plugin-transform-react-constant-elements@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ed59464c96cd4014f636852b4de398d2ffc22ffe3177a6c2a6058447a72839bb66a346a1db525ab60dcc5dd48ec59113a8325f785593689900358a15136e05c3
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-react-display-name@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cd7020494e6f31c287834e8929e6a718d5b0ace21232fa30feb48622c2312045504c34b347dcff9e88145c349882b296a7d6b6cc3d3447d8c85502f16471747c
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.25.9"
  dependencies:
    "@babel/plugin-transform-react-jsx": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 537d38369537f1eb56041c4b770bc0733fde1801a7f5ffef40a1217ea448f33ee2fa8e6098a58a82fd00e432c1b9426a66849496da419020c9eca3b1b1a23779
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-react-jsx@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-module-imports": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/plugin-syntax-jsx": ^7.25.9
    "@babel/types": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5c6523c3963e3c6cf4c3cc2768a3766318af05b8f6c17aff52a4010e2c170e87b2fcdc94e9c9223ae12158664df4852ce81b9c8d042c15ea8fd83d6375f9f30f
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.25.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9995c0fc7c25d3aaaa0ce84233de02eab2564ea111d0813ec5baa538eb21520402879cc787ad1ad4c2061b99cebc3beb09910e64c9592e8ccb42ae62d9e4fd9a
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-regenerator@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    regenerator-transform: ^0.15.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1c09e8087b476c5967282c9790fb8710e065eda77c60f6cb5da541edd59ded9d003d96f8ef640928faab4a0b35bf997673499a194973da4f0c97f0935807a482
  languageName: node
  linkType: hard

"@babel/plugin-transform-regexp-modifiers@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/plugin-transform-regexp-modifiers@npm:7.26.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 726deca486bbd4b176f8a966eb0f4aabc19d9def3b8dabb8b3a656778eca0df1fda3f3c92b213aa5a184232fdafd5b7bd73b4e24ca4345c498ef6baff2bda4e1
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-reserved-words@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8beda04481b25767acbd1f6b9ef7b3a9c12fbd9dcb24df45a6ad120e1dc4b247c073db60ac742f9093657d6d8c050501fc0606af042f81a3bb6a3ff862cddc47
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f774995d58d4e3a992b732cf3a9b8823552d471040e280264dd15e0735433d51b468fef04d75853d061309389c66bda10ce1b298297ce83999220eb0ad62741d
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-spread@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2403a5d49171b7714d5e5ecb1f598c61575a4dbe5e33e5a5f08c0ea990b75e693ca1ea983b6a96b2e3e5e7da48c8238333f525e47498c53b577c5d094d964c06
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7454b00844dbe924030dd15e2b3615b36e196500c4c47e98dabc6b37a054c5b1038ecd437e910aabf0e43bf56b973cb148d3437d50f6e2332d8309568e3e979b
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/plugin-transform-template-literals@npm:7.26.8"
  dependencies:
    "@babel/helper-plugin-utils": ^7.26.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 65874c8844ce906507cd5b9c78950d6173f8339b6416a2a9e763021db5a7045315a6f0e58976ec4af5e960c003ef322576c105130a644addb8f94d1a0821a972
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.26.7":
  version: 7.26.7
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.26.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.26.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1fcc48bde1426527d9905d561884e1ecaf3c03eb5abb507d33f71591f8da0c384e92097feaf91cc30692e04fb7f5e6ff1cb172acc5de7675d93fdb42db850d6a
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.25.9":
  version: 7.26.8
  resolution: "@babel/plugin-transform-typescript@npm:7.26.8"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.25.9
    "@babel/helper-create-class-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.26.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.25.9
    "@babel/plugin-syntax-typescript": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3d8866f2c5cb70d27bfb724bf205f073b59d04fe7e535c63439968579dc79b69055681088b522dab49695bdf1365b00e22aee11e3f3253381e554d89a8aa9dd6
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: be067e07488d804e3e82d7771f23666539d2ae5af03bf6eb8480406adf3dabd776e60c1fd5c6078dc5714b73cd80bbaca70e71d4f5d154c5c57200581602ca2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 201f6f46c1beb399e79aa208b94c5d54412047511795ce1e790edcd189cef73752e6a099fdfc01b3ad12205f139ae344143b62f21f44bbe02338a95e8506a911
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e8baae867526e179467c6ef5280d70390fa7388f8763a19a27c21302dd59b121032568be080749514b097097ceb9af716bf4b90638f1b3cf689aa837ba20150f
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.25.9"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.25.9
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 4445ef20de687cb4dcc95169742a8d9013d680aa5eee9186d8e25875bbfa7ee5e2de26a91177ccf70b1db518e36886abcd44750d28db5d7a9539f0efa6839f4b
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.20.2":
  version: 7.26.8
  resolution: "@babel/preset-env@npm:7.26.8"
  dependencies:
    "@babel/compat-data": ^7.26.8
    "@babel/helper-compilation-targets": ^7.26.5
    "@babel/helper-plugin-utils": ^7.26.5
    "@babel/helper-validator-option": ^7.25.9
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": ^7.25.9
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": ^7.25.9
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.25.9
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.25.9
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": ^7.25.9
    "@babel/plugin-proposal-private-property-in-object": 7.21.0-placeholder-for-preset-env.2
    "@babel/plugin-syntax-import-assertions": ^7.26.0
    "@babel/plugin-syntax-import-attributes": ^7.26.0
    "@babel/plugin-syntax-unicode-sets-regex": ^7.18.6
    "@babel/plugin-transform-arrow-functions": ^7.25.9
    "@babel/plugin-transform-async-generator-functions": ^7.26.8
    "@babel/plugin-transform-async-to-generator": ^7.25.9
    "@babel/plugin-transform-block-scoped-functions": ^7.26.5
    "@babel/plugin-transform-block-scoping": ^7.25.9
    "@babel/plugin-transform-class-properties": ^7.25.9
    "@babel/plugin-transform-class-static-block": ^7.26.0
    "@babel/plugin-transform-classes": ^7.25.9
    "@babel/plugin-transform-computed-properties": ^7.25.9
    "@babel/plugin-transform-destructuring": ^7.25.9
    "@babel/plugin-transform-dotall-regex": ^7.25.9
    "@babel/plugin-transform-duplicate-keys": ^7.25.9
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": ^7.25.9
    "@babel/plugin-transform-dynamic-import": ^7.25.9
    "@babel/plugin-transform-exponentiation-operator": ^7.26.3
    "@babel/plugin-transform-export-namespace-from": ^7.25.9
    "@babel/plugin-transform-for-of": ^7.25.9
    "@babel/plugin-transform-function-name": ^7.25.9
    "@babel/plugin-transform-json-strings": ^7.25.9
    "@babel/plugin-transform-literals": ^7.25.9
    "@babel/plugin-transform-logical-assignment-operators": ^7.25.9
    "@babel/plugin-transform-member-expression-literals": ^7.25.9
    "@babel/plugin-transform-modules-amd": ^7.25.9
    "@babel/plugin-transform-modules-commonjs": ^7.26.3
    "@babel/plugin-transform-modules-systemjs": ^7.25.9
    "@babel/plugin-transform-modules-umd": ^7.25.9
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.25.9
    "@babel/plugin-transform-new-target": ^7.25.9
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.26.6
    "@babel/plugin-transform-numeric-separator": ^7.25.9
    "@babel/plugin-transform-object-rest-spread": ^7.25.9
    "@babel/plugin-transform-object-super": ^7.25.9
    "@babel/plugin-transform-optional-catch-binding": ^7.25.9
    "@babel/plugin-transform-optional-chaining": ^7.25.9
    "@babel/plugin-transform-parameters": ^7.25.9
    "@babel/plugin-transform-private-methods": ^7.25.9
    "@babel/plugin-transform-private-property-in-object": ^7.25.9
    "@babel/plugin-transform-property-literals": ^7.25.9
    "@babel/plugin-transform-regenerator": ^7.25.9
    "@babel/plugin-transform-regexp-modifiers": ^7.26.0
    "@babel/plugin-transform-reserved-words": ^7.25.9
    "@babel/plugin-transform-shorthand-properties": ^7.25.9
    "@babel/plugin-transform-spread": ^7.25.9
    "@babel/plugin-transform-sticky-regex": ^7.25.9
    "@babel/plugin-transform-template-literals": ^7.26.8
    "@babel/plugin-transform-typeof-symbol": ^7.26.7
    "@babel/plugin-transform-unicode-escapes": ^7.25.9
    "@babel/plugin-transform-unicode-property-regex": ^7.25.9
    "@babel/plugin-transform-unicode-regex": ^7.25.9
    "@babel/plugin-transform-unicode-sets-regex": ^7.25.9
    "@babel/preset-modules": 0.1.6-no-external-plugins
    babel-plugin-polyfill-corejs2: ^0.4.10
    babel-plugin-polyfill-corejs3: ^0.11.0
    babel-plugin-polyfill-regenerator: ^0.6.1
    core-js-compat: ^3.40.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 409066e5ab77321b0ba7a231509aa75e92ad6ec718b3b1c07dbba7028a223877a65f6472d167942cb30ffac29401b37fa20b6dc724c7e9deba30145714b50680
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 4855e799bc50f2449fb5210f78ea9e8fd46cf4f242243f1e2ed838e2bd702e25e73e822e7f8447722a5f4baa5e67a8f7a0e403f3e7ce04540ff743a9c411c375
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.18.6":
  version: 7.26.3
  resolution: "@babel/preset-react@npm:7.26.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-validator-option": ^7.25.9
    "@babel/plugin-transform-react-display-name": ^7.25.9
    "@babel/plugin-transform-react-jsx": ^7.25.9
    "@babel/plugin-transform-react-jsx-development": ^7.25.9
    "@babel/plugin-transform-react-pure-annotations": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9c76f145026715c8e4a1f6c44f208918e700227d8d8a8068f4ae10d87031d23eb8b483e508cd4452d65066f731b7a8169527e66e83ffe165595e8db7899dd859
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.21.0":
  version: 7.26.0
  resolution: "@babel/preset-typescript@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
    "@babel/helper-validator-option": ^7.25.9
    "@babel/plugin-syntax-jsx": ^7.25.9
    "@babel/plugin-transform-modules-commonjs": ^7.25.9
    "@babel/plugin-transform-typescript": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6d8641fa6efd0e10eec5e8f92cd164b916a06d57131cfa5216c281404289c87d2b4995140a1c1d9c3bad171ff6ef2226be5f0585e09577ffff349706e991ec71
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.23.2, @babel/runtime@npm:^7.8.4":
  version: 7.26.7
  resolution: "@babel/runtime@npm:7.26.7"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: a1664a08f3f4854b895b540cca2f5f5c6c1993b5fb788c9615d70fc201e16bb254df8e0550c83eaf2749a14d87775e11a7c9ded6161203e9da7a4a323d546925
  languageName: node
  linkType: hard

"@babel/template@npm:^7.25.9, @babel/template@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/template@npm:7.26.8"
  dependencies:
    "@babel/code-frame": ^7.26.2
    "@babel/parser": ^7.26.8
    "@babel/types": ^7.26.8
  checksum: dfa79b33d49b89b2466a660bf299a545dd5fd6680fbf9828d2deca9bd826eb861041a9f5a25a4a0dddf6e4905e6fafac18a6885bf2aeecac6f39407a221e630f
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.25.9, @babel/traverse@npm:^7.26.5, @babel/traverse@npm:^7.26.8":
  version: 7.26.8
  resolution: "@babel/traverse@npm:7.26.8"
  dependencies:
    "@babel/code-frame": ^7.26.2
    "@babel/generator": ^7.26.8
    "@babel/parser": ^7.26.8
    "@babel/template": ^7.26.8
    "@babel/types": ^7.26.8
    debug: ^4.3.1
    globals: ^11.1.0
  checksum: f8b2f4d9945932ac6b0a359c322628327514a3e1d356555923dc143f3376d3e01f8f7a56cccb717223fa7420426e077809701175b717d946c622d826a6df7c60
  languageName: node
  linkType: hard

"@babel/types@npm:^7.21.3, @babel/types@npm:^7.25.9, @babel/types@npm:^7.26.7, @babel/types@npm:^7.26.8, @babel/types@npm:^7.4.4":
  version: 7.26.8
  resolution: "@babel/types@npm:7.26.8"
  dependencies:
    "@babel/helper-string-parser": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
  checksum: 8f0f3bac37cc93d4658df460dc24156c6f1466abca63ef111c9f03128df6c247c672ed89e779ababb41250627c78d8bfcfba616eecb01b6e4ddcfd8ded718996
  languageName: node
  linkType: hard

"@citation-js/core@npm:^0.7.14":
  version: 0.7.18
  resolution: "@citation-js/core@npm:0.7.18"
  dependencies:
    "@citation-js/date": ^0.5.0
    "@citation-js/name": ^0.4.2
    fetch-ponyfill: ^7.1.0
    sync-fetch: ^0.4.1
  checksum: 94a4c42612eb425e16b08ac3720fe3e798263830960ccb81be99a557c9c5b9d1b6515cfdf4303287a3533d658845f457bf4cbae7f558983477b729cf052f0699
  languageName: node
  linkType: hard

"@citation-js/date@npm:^0.5.0, @citation-js/date@npm:^0.5.1":
  version: 0.5.1
  resolution: "@citation-js/date@npm:0.5.1"
  checksum: d3f71fd8cc933d7b51f3aec3a529729a13617680b310aba72c1adb3c0b9756c418219d30a85e9a495b1f8202fd6c751ade117b64e5a95ac95a3c94014448e56f
  languageName: node
  linkType: hard

"@citation-js/name@npm:^0.4.2":
  version: 0.4.2
  resolution: "@citation-js/name@npm:0.4.2"
  checksum: 35556499a6a8a77189ee29b2ec15ee06366d0d0d79b5a896dfb617b94c1f4cf8423a0db022c294ee144a5a0f382fec6cce7b8064b71aaf001574095064b1586b
  languageName: node
  linkType: hard

"@citation-js/plugin-bibjson@npm:^0.7.14":
  version: 0.7.18
  resolution: "@citation-js/plugin-bibjson@npm:0.7.18"
  dependencies:
    "@citation-js/date": ^0.5.0
    "@citation-js/name": ^0.4.2
  peerDependencies:
    "@citation-js/core": ^0.7.0
  checksum: 94de821f0290272963831ffaf98b57cca1313e423eefd72e4b4f24080b41bbb52d9308d9ad033cbbb97820f467bc45347ff12b028659c2b5e90c414c32e7c6dc
  languageName: node
  linkType: hard

"@citation-js/plugin-bibtex@npm:^0.7.14":
  version: 0.7.18
  resolution: "@citation-js/plugin-bibtex@npm:0.7.18"
  dependencies:
    "@citation-js/date": ^0.5.0
    "@citation-js/name": ^0.4.2
    moo: ^0.5.1
  peerDependencies:
    "@citation-js/core": ^0.7.0
  checksum: f0dc77ef3f6ca1cc37ef7a07bcc914fc6c136a8687e55063cf44d7ce82ad3f06dd5b53aa4b73e28d2800fa1d509ee87d045695a3a6814f5d18477d63ba4efb5b
  languageName: node
  linkType: hard

"@citation-js/plugin-csl@npm:^0.7.14":
  version: 0.7.18
  resolution: "@citation-js/plugin-csl@npm:0.7.18"
  dependencies:
    "@citation-js/date": ^0.5.0
    citeproc: ^2.4.6
  peerDependencies:
    "@citation-js/core": ^0.7.0
  checksum: eec193b5bb066ceef9b4b8843a6b197e14f4df4ce0bc06b894d24fd7936ab7370b23a7af1d9414db5b9752af941636aa026526a8517da3855cf45a5a04479d3a
  languageName: node
  linkType: hard

"@contentlayer2/cli@npm:0.5.4":
  version: 0.5.4
  resolution: "@contentlayer2/cli@npm:0.5.4"
  dependencies:
    "@contentlayer2/core": 0.5.4
    "@contentlayer2/utils": 0.5.4
    clipanion: ^3.2.1
    typanion: ^3.12.1
  checksum: e2ff32f3afc4b7d924f12128420084afb1abede3d063cd3495adc98a871842c0ea0801a955346013d86d1ead2340f31e9444c6739acef68f35bfbfa02344f856
  languageName: node
  linkType: hard

"@contentlayer2/cli@npm:0.5.5":
  version: 0.5.5
  resolution: "@contentlayer2/cli@npm:0.5.5"
  dependencies:
    "@contentlayer2/core": 0.5.5
    "@contentlayer2/utils": 0.5.5
    clipanion: ^3.2.1
    typanion: ^3.12.1
  checksum: 7330c28fd7825934b8763904006c59746a7231fdb6edc018070bedb3a0f578df2d6a83eb68d2124c001d7d7889ebce1cffb13dc123d3189c7437acc46a8f50a7
  languageName: node
  linkType: hard

"@contentlayer2/client@npm:0.5.4":
  version: 0.5.4
  resolution: "@contentlayer2/client@npm:0.5.4"
  dependencies:
    "@contentlayer2/core": 0.5.4
  checksum: 69c2b45382c2d235eeb492166bdf0b123b997f4c4a0985085406780127c87012e00617a0a771ac65b5674273374926a7df14a279e49cdc136b573f5e67037118
  languageName: node
  linkType: hard

"@contentlayer2/client@npm:0.5.5":
  version: 0.5.5
  resolution: "@contentlayer2/client@npm:0.5.5"
  dependencies:
    "@contentlayer2/core": 0.5.5
  checksum: cba6c9c65b4f50386f08579f8ba02b348e349417f2d65cc747663d7d3de88a4ec85cd51eac5a3c3d1b0b2e951b77d405cef19fbd658a3fc5ec9892c7077fb519
  languageName: node
  linkType: hard

"@contentlayer2/core@npm:0.5.4":
  version: 0.5.4
  resolution: "@contentlayer2/core@npm:0.5.4"
  dependencies:
    "@contentlayer2/utils": 0.5.4
    camel-case: ^4.1.2
    comment-json: ^4.2.3
    esbuild: ">=0.17"
    gray-matter: ^4.0.3
    mdx-bundler: ^10.0.2
    rehype-stringify: ^10.0.0
    remark-frontmatter: ^5.0.0
    remark-parse: ^11.0.0
    remark-rehype: ^11.1.0
    source-map-support: ^0.5.21
    type-fest: ^4.10.0
    unified: ^11.0.4
  peerDependencies:
    esbuild: ">=0.17"
    markdown-wasm: 1.x
  peerDependenciesMeta:
    esbuild:
      optional: true
    markdown-wasm:
      optional: true
  checksum: cb564a65a12aedb6490c792e457d3d2719c570cc94c34b3666300e3fc9304ace760f392eba382fd4a05948c4a09eb8b54fe729fd11f2d5756ad4ee514a529527
  languageName: node
  linkType: hard

"@contentlayer2/core@npm:0.5.5":
  version: 0.5.5
  resolution: "@contentlayer2/core@npm:0.5.5"
  dependencies:
    "@contentlayer2/utils": 0.5.5
    camel-case: ^4.1.2
    comment-json: ^4.2.3
    esbuild: ">=0.17"
    gray-matter: ^4.0.3
    mdx-bundler: ^10.1.1
    rehype-stringify: ^10.0.0
    remark-frontmatter: ^5.0.0
    remark-parse: ^11.0.0
    remark-rehype: ^11.1.0
    source-map-support: ^0.5.21
    type-fest: ^4.10.0
    unified: ^11.0.4
  peerDependencies:
    esbuild: ">=0.17"
    markdown-wasm: 1.x
  peerDependenciesMeta:
    esbuild:
      optional: true
    markdown-wasm:
      optional: true
  checksum: 9cc91034506c5ab57e01f0139e76a4341e6dc21df7ab5b070660c06a9a6f89a77392690e8a02c3735efb8394ff734e693d0941392f7569d97de8ee549c6aa81d
  languageName: node
  linkType: hard

"@contentlayer2/source-files@npm:0.5.4":
  version: 0.5.4
  resolution: "@contentlayer2/source-files@npm:0.5.4"
  dependencies:
    "@contentlayer2/core": 0.5.4
    "@contentlayer2/utils": 0.5.4
    chokidar: ^3.5.3
    fast-glob: ^3.2.12
    gray-matter: ^4.0.3
    imagescript: ^1.2.16
    micromatch: ^4.0.5
    ts-pattern: ^5.0.6
    unified: ^11.0.4
    yaml: ^2.3.1
    zod: ^3.22.4
  checksum: 747d32b77605e9c335f22b449c5f74ec3836be92872c8e61ca026676c6dca818da2ac2274f53a451d7b099600897925b01dcf65dc8ce794c5e50754e7336dea2
  languageName: node
  linkType: hard

"@contentlayer2/source-files@npm:0.5.5":
  version: 0.5.5
  resolution: "@contentlayer2/source-files@npm:0.5.5"
  dependencies:
    "@contentlayer2/core": 0.5.5
    "@contentlayer2/utils": 0.5.5
    chokidar: ^3.5.3
    fast-glob: ^3.2.12
    gray-matter: ^4.0.3
    imagescript: ^1.2.16
    micromatch: ^4.0.5
    ts-pattern: ^5.0.6
    unified: ^11.0.4
    yaml: ^2.3.1
    zod: ^3.22.4
  checksum: f967d67598c2c1042b7624eecea11c97535a69b3cc0bc4f0122b6cac994061c22a229baaa3b715bd07770dba363ec7555f3b26a53e27542189a7d9b862507300
  languageName: node
  linkType: hard

"@contentlayer2/source-remote-files@npm:0.5.4":
  version: 0.5.4
  resolution: "@contentlayer2/source-remote-files@npm:0.5.4"
  dependencies:
    "@contentlayer2/core": 0.5.4
    "@contentlayer2/source-files": 0.5.4
    "@contentlayer2/utils": 0.5.4
  checksum: 902242928e1b8dc84e9d52cb14a42cc73628c72990c562865a8f9879057000c39acffca4b5147139e02db4942ed165783c9b3c4be7e69f062875184b7756e6ff
  languageName: node
  linkType: hard

"@contentlayer2/source-remote-files@npm:0.5.5":
  version: 0.5.5
  resolution: "@contentlayer2/source-remote-files@npm:0.5.5"
  dependencies:
    "@contentlayer2/core": 0.5.5
    "@contentlayer2/source-files": 0.5.5
    "@contentlayer2/utils": 0.5.5
  checksum: ef5851098bc6333db219975a4aac2c94e66642c8d228c1d83520a23d7e0f2891ef9205e428b68112f45d41a3a1ca1bc01bbfca402704bbed4cd5e96aca6401ca
  languageName: node
  linkType: hard

"@contentlayer2/utils@npm:0.5.4":
  version: 0.5.4
  resolution: "@contentlayer2/utils@npm:0.5.4"
  dependencies:
    "@effect-ts/core": ^0.60.5
    "@effect-ts/otel": ^0.15.1
    "@effect-ts/otel-sdk-trace-node": ^0.15.1
    "@js-temporal/polyfill": ^0.4.4
    "@opentelemetry/api": ^1.9.0
    "@opentelemetry/core": ^1.30.1
    "@opentelemetry/exporter-trace-otlp-grpc": ^0.57.1
    "@opentelemetry/resources": ^1.30.1
    "@opentelemetry/sdk-trace-base": ^1.30.1
    "@opentelemetry/sdk-trace-node": ^1.30.1
    "@opentelemetry/semantic-conventions": ^1.28.0
    chokidar: ^3.5.3
    hash-wasm: ^4.11.0
    inflection: ^3.0.0
    memfs: ^4.8.2
    oo-ascii-tree: ^1.94.0
    ts-pattern: ^5.0.6
    type-fest: ^4.10.0
  peerDependenciesMeta:
    "@effect-ts/core":
      optional: true
    "@effect-ts/otel":
      optional: true
    "@effect-ts/otel-node":
      optional: true
  checksum: bb647b7bf9e9c65a0b3d422d81e6aec84ee256b699829b4d8ed217b7ec6ed4391a8da0f5d39996b7a877b7a0139403609d8d05c57afee84949c59c46fb3b8abe
  languageName: node
  linkType: hard

"@contentlayer2/utils@npm:0.5.5":
  version: 0.5.5
  resolution: "@contentlayer2/utils@npm:0.5.5"
  dependencies:
    "@effect-ts/core": ^0.60.5
    "@effect-ts/otel": ^0.15.1
    "@effect-ts/otel-sdk-trace-node": ^0.15.1
    "@js-temporal/polyfill": ^0.4.4
    "@opentelemetry/api": ^1.9.0
    "@opentelemetry/core": ^1.30.1
    "@opentelemetry/exporter-trace-otlp-grpc": ^0.57.1
    "@opentelemetry/resources": ^1.30.1
    "@opentelemetry/sdk-trace-base": ^1.30.1
    "@opentelemetry/sdk-trace-node": ^1.30.1
    "@opentelemetry/semantic-conventions": ^1.28.0
    chokidar: ^3.5.3
    hash-wasm: ^4.11.0
    inflection: ^3.0.0
    memfs: ^4.8.2
    oo-ascii-tree: ^1.94.0
    ts-pattern: ^5.0.6
    type-fest: ^4.10.0
  peerDependenciesMeta:
    "@effect-ts/core":
      optional: true
    "@effect-ts/otel":
      optional: true
    "@effect-ts/otel-node":
      optional: true
  checksum: a5ff9b9360da71be70385651fe1c7bbf1163bcf80833199b6a9d94d3ea4d0958c842d145dc6d4f78e37b93c82a259e91965ce212f0632086f5876a71ba36d1a6
  languageName: node
  linkType: hard

"@discoveryjs/json-ext@npm:0.5.7":
  version: 0.5.7
  resolution: "@discoveryjs/json-ext@npm:0.5.7"
  checksum: 2176d301cc258ea5c2324402997cf8134ebb212469c0d397591636cea8d3c02f2b3cf9fd58dcb748c7a0dade77ebdc1b10284fa63e608c033a1db52fddc69918
  languageName: node
  linkType: hard

"@docsearch/css@npm:3.8.3":
  version: 3.8.3
  resolution: "@docsearch/css@npm:3.8.3"
  checksum: 79281cb78cf7b236fead4930f6832220bb4a03acccd825c7b43fe48653a19fd1d6b12739b469d4c73130f8ed68868e434531f8a36d33bc73a7683818c77a9bda
  languageName: node
  linkType: hard

"@docsearch/react@npm:^3.6.2":
  version: 3.8.3
  resolution: "@docsearch/react@npm:3.8.3"
  dependencies:
    "@algolia/autocomplete-core": 1.17.9
    "@algolia/autocomplete-preset-algolia": 1.17.9
    "@docsearch/css": 3.8.3
    algoliasearch: ^5.14.2
  peerDependencies:
    "@types/react": ">= 16.8.0 < 19.0.0"
    react: ">= 16.8.0 < 19.0.0"
    react-dom: ">= 16.8.0 < 19.0.0"
    search-insights: ">= 1 < 3"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    react:
      optional: true
    react-dom:
      optional: true
    search-insights:
      optional: true
  checksum: 2a2266c7e3e506e26f8cc4ee19abb238ba8289737e0e32542a3d1ded88376b322493373df53311626e3bd6b8bd65fb27581a408de20097c5654a6c56e80ecb28
  languageName: node
  linkType: hard

"@effect-ts/core@npm:^0.60.5":
  version: 0.60.5
  resolution: "@effect-ts/core@npm:0.60.5"
  dependencies:
    "@effect-ts/system": ^0.57.5
  checksum: c1236ce2f261a4547379061c80eb55ca476d3638289b2d2b54fb491aabe2ad9e325e1774c948f34572d9bceb07c0e74f1be2d69b060673c0b1df44f149dae164
  languageName: node
  linkType: hard

"@effect-ts/otel-sdk-trace-node@npm:^0.15.1":
  version: 0.15.1
  resolution: "@effect-ts/otel-sdk-trace-node@npm:0.15.1"
  dependencies:
    "@effect-ts/otel": ^0.15.1
  peerDependencies:
    "@effect-ts/core": ^0.60.2
    "@opentelemetry/api": ^1.4.0
    "@opentelemetry/core": ^1.13.0
    "@opentelemetry/sdk-trace-base": ^1.13.0
    "@opentelemetry/sdk-trace-node": ^1.13.0
  checksum: 70a5ba0ced3d15a4d66bb49f914822d1e1e9f4028da2cb64fa9d27d2596ab3ed59b2d889d0fedd87383749861f8dcf72b993a543150f4a5c8b24faa93350dc39
  languageName: node
  linkType: hard

"@effect-ts/otel@npm:^0.15.1":
  version: 0.15.1
  resolution: "@effect-ts/otel@npm:0.15.1"
  peerDependencies:
    "@effect-ts/core": ^0.60.2
    "@opentelemetry/api": ^1.4.0
    "@opentelemetry/core": ^1.13.0
    "@opentelemetry/sdk-trace-base": ^1.13.0
  checksum: ee48c0b0a82026776f96f7b915a0f56d0df885d7cc16f8160d07508aade4f9a99766a3f31f85cb111044370fc4051a62ee6c29552fbcedee466b0f1f98b14d80
  languageName: node
  linkType: hard

"@effect-ts/system@npm:^0.57.5":
  version: 0.57.5
  resolution: "@effect-ts/system@npm:0.57.5"
  checksum: f24c304e076d82bcbf375c977fb678353bb7b37b22ba36e77f47db31532377e1d670391b03fb2c29b518ed91f4c996f34d45fab501921d7b11c05e6de69fa2ec
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.2.0":
  version: 1.3.1
  resolution: "@emnapi/runtime@npm:1.3.1"
  dependencies:
    tslib: ^2.4.0
  checksum: 9a16ae7905a9c0e8956cf1854ef74e5087fbf36739abdba7aa6b308485aafdc993da07c19d7af104cd5f8e425121120852851bb3a0f78e2160e420a36d47f42f
  languageName: node
  linkType: hard

"@esbuild-plugins/node-resolve@npm:^0.2.2":
  version: 0.2.2
  resolution: "@esbuild-plugins/node-resolve@npm:0.2.2"
  dependencies:
    "@types/resolve": ^1.17.1
    debug: ^4.3.1
    escape-string-regexp: ^4.0.0
    resolve: ^1.19.0
  peerDependencies:
    esbuild: "*"
  checksum: 4cb460a9462d9685e365370bdcbc46f865d11a3235586985097ab488136cab80a34c107af52db2a11f1c7b969ec1ffe434b812ddc7ffe54734a55648638999fe
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/aix-ppc64@npm:0.25.0"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/aix-ppc64@npm:0.25.2"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/android-arm64@npm:0.25.0"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/android-arm64@npm:0.25.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/android-arm@npm:0.25.0"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/android-arm@npm:0.25.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/android-x64@npm:0.25.0"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/android-x64@npm:0.25.2"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/darwin-arm64@npm:0.25.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/darwin-arm64@npm:0.25.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/darwin-x64@npm:0.25.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/darwin-x64@npm:0.25.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/freebsd-arm64@npm:0.25.0"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/freebsd-arm64@npm:0.25.2"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/freebsd-x64@npm:0.25.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/freebsd-x64@npm:0.25.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-arm64@npm:0.25.0"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-arm64@npm:0.25.2"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-arm@npm:0.25.0"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-arm@npm:0.25.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-ia32@npm:0.25.0"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-ia32@npm:0.25.2"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-loong64@npm:0.25.0"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-loong64@npm:0.25.2"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-mips64el@npm:0.25.0"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-mips64el@npm:0.25.2"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-ppc64@npm:0.25.0"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-ppc64@npm:0.25.2"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-riscv64@npm:0.25.0"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-riscv64@npm:0.25.2"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-s390x@npm:0.25.0"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-s390x@npm:0.25.2"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/linux-x64@npm:0.25.0"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/linux-x64@npm:0.25.2"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/netbsd-arm64@npm:0.25.0"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/netbsd-arm64@npm:0.25.2"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/netbsd-x64@npm:0.25.0"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/netbsd-x64@npm:0.25.2"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/openbsd-arm64@npm:0.25.0"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/openbsd-arm64@npm:0.25.2"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/openbsd-x64@npm:0.25.0"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/openbsd-x64@npm:0.25.2"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/sunos-x64@npm:0.25.0"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/sunos-x64@npm:0.25.2"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/win32-arm64@npm:0.25.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/win32-arm64@npm:0.25.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/win32-ia32@npm:0.25.0"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/win32-ia32@npm:0.25.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.0":
  version: 0.25.0
  resolution: "@esbuild/win32-x64@npm:0.25.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.2":
  version: 0.25.2
  resolution: "@esbuild/win32-x64@npm:0.25.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.1
  resolution: "@eslint-community/eslint-utils@npm:4.4.1"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: a7ffc838eb6a9ef594cda348458ccf38f34439ac77dc090fa1c120024bcd4eb911dfd74d5ef44d42063e7949fa7c5123ce714a015c4abb917d4124be1bd32bfe
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 0d628680e204bc316d545b4993d3658427ca404ae646ce541fcc65306b8c712c340e5e573e30fb9f85f4855c0c5f6dca9868931f2fcced06417fbe1a0c6cd2d6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.19.0":
  version: 0.19.2
  resolution: "@eslint/config-array@npm:0.19.2"
  dependencies:
    "@eslint/object-schema": ^2.1.6
    debug: ^4.3.1
    minimatch: ^3.1.2
  checksum: 1c707e04fc2951079b32d2cb1c939ce25e863cd1329c1bd363a285b2a5caaaf88b97ddbf354cc46d1334097dc749f79b0fae33151dc2dfb9a60ba14288c65b39
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.10.0":
  version: 0.10.0
  resolution: "@eslint/core@npm:0.10.0"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: 851fa099b3fef00e7ff8ece14523aff0822d3e1b47b047ab0a0d898e80c1362a22aa8b7778727231c383246932ecb63de79b4448ec1e500901c578580b087573
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.11.0":
  version: 0.11.0
  resolution: "@eslint/core@npm:0.11.0"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: 9038b006bdb6a1a5b942e45d217598aaaec86cc97f8e891964e5220bc5514015981152cc999ea4196ee66d1f6ca5b3f8e8de404d5d8890d50142aee9e15495d1
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.2.0":
  version: 3.2.0
  resolution: "@eslint/eslintrc@npm:3.2.0"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^10.0.1
    globals: ^14.0.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: c898e4d12f4c9a79a61ee3c91e38eea5627a04e021cb749191e8537445858bfe32f810eca0cb2dc9902b8ad8b65ca07ef7221dc4bad52afe60cbbf50ec56c236
  languageName: node
  linkType: hard

"@eslint/js@npm:9.20.0, @eslint/js@npm:^9.16.0":
  version: 9.20.0
  resolution: "@eslint/js@npm:9.20.0"
  checksum: e49dcbcea1a7892222988ba410b3f1e2b756177558f3f11fa3627682c3aca7585f8124c128711035e176daf56f82b4af47dc5655ca7a825057451607e42e5d13
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: e32e565319f6544d36d3fa69a3e163120722d12d666d1a4525c9a6f02e9b54c29d9b1f03139e25d7e759e08dda8da433590bc23c09db8d511162157ef1b86a4c
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.2.5":
  version: 0.2.5
  resolution: "@eslint/plugin-kit@npm:0.2.5"
  dependencies:
    "@eslint/core": ^0.10.0
    levn: ^0.4.1
  checksum: 423db33e67ff16f6db71bf8bfc8d5b0c2c4fe6f2209131e5886a573bf994bfc72ab4f825068d6521f186247731d4c9d48eb42a5e5ce389c6faa275752c0e9459
  languageName: node
  linkType: hard

"@fal-works/esbuild-plugin-global-externals@npm:^2.1.2":
  version: 2.1.2
  resolution: "@fal-works/esbuild-plugin-global-externals@npm:2.1.2"
  checksum: c59715902b9062aa7ff38973f298b509499fd146dbf564dc338b3f9e896da5bffb4ca676c27587fde79b3586003e24d65960acb62f009bca43dca34c76f8cbf7
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.6.0":
  version: 1.6.9
  resolution: "@floating-ui/core@npm:1.6.9"
  dependencies:
    "@floating-ui/utils": ^0.2.9
  checksum: 21cbcac72a40172399570dedf0eb96e4f24b0d829980160e8d14edf08c2955ac6feffb7b94e1530c78fb7944635e52669c9257ad08570e0295efead3b5a9af91
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.0.0":
  version: 1.6.13
  resolution: "@floating-ui/dom@npm:1.6.13"
  dependencies:
    "@floating-ui/core": ^1.6.0
    "@floating-ui/utils": ^0.2.9
  checksum: eabab9d860d3b5beab1c2d6936287efc4d9ab352de99062380589ef62870d59e8730397489c34a96657e128498001b5672330c4a9da0159fe8b2401ac59fe314
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.1.2":
  version: 2.1.2
  resolution: "@floating-ui/react-dom@npm:2.1.2"
  dependencies:
    "@floating-ui/dom": ^1.0.0
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 25bb031686e23062ed4222a8946e76b3f9021d40a48437bd747233c4964a766204b8a55f34fa8b259839af96e60db7c6e3714d81f1de06914294f90e86ffbc48
  languageName: node
  linkType: hard

"@floating-ui/react@npm:^0.26.16":
  version: 0.26.28
  resolution: "@floating-ui/react@npm:0.26.28"
  dependencies:
    "@floating-ui/react-dom": ^2.1.2
    "@floating-ui/utils": ^0.2.8
    tabbable: ^6.0.0
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 1bfcccdb1f388ceb0075dc3e46934f4f04ef10bff2f971e1bf79067391c8729b366025caca0a42f5ca80854820a621a9edecbacdc046c33eb428f508fd6ce1f3
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.8, @floating-ui/utils@npm:^0.2.9":
  version: 0.2.9
  resolution: "@floating-ui/utils@npm:0.2.9"
  checksum: d518b80cec5a323e54a069a1dd99a20f8221a4853ed98ac16c75275a0cc22f75de4f8ac5b121b4f8990bd45da7ad1fb015b9a1e4bac27bb1cd62444af84e9784
  languageName: node
  linkType: hard

"@giscus/react@npm:^3.0.0":
  version: 3.1.0
  resolution: "@giscus/react@npm:3.1.0"
  dependencies:
    giscus: ^1.6.0
  peerDependencies:
    react: ^16 || ^17 || ^18 || ^19
    react-dom: ^16 || ^17 || ^18 || ^19
  checksum: 7dbbf62ba94b7fd229d5f5f93c4452d2d349c544b434411573152d8d17e3b15f50cd6c7fc06ebbb8ae778650753e4a3432d8151be1c065adfa36602d2b3e5ee1
  languageName: node
  linkType: hard

"@grpc/grpc-js@npm:^1.7.1":
  version: 1.12.6
  resolution: "@grpc/grpc-js@npm:1.12.6"
  dependencies:
    "@grpc/proto-loader": ^0.7.13
    "@js-sdsl/ordered-map": ^4.4.2
  checksum: eec210e6895be95f0784eec7704128d75655cee528ae6ead7e4bbc2c2d18d7a1e3938c2d2a8256a2dbbd4a75b95909c6b2e29ff21419e3609e93dfdb6213afe3
  languageName: node
  linkType: hard

"@grpc/proto-loader@npm:^0.7.13":
  version: 0.7.13
  resolution: "@grpc/proto-loader@npm:0.7.13"
  dependencies:
    lodash.camelcase: ^4.3.0
    long: ^5.0.0
    protobufjs: ^7.2.5
    yargs: ^17.7.2
  bin:
    proto-loader-gen-types: build/bin/proto-loader-gen-types.js
  checksum: 399c1b8a4627f93dc31660d9636ea6bf58be5675cc7581e3df56a249369e5be02c6cd0d642c5332b0d5673bc8621619bc06fb045aa3e8f57383737b5d35930dc
  languageName: node
  linkType: hard

"@headlessui/react@npm:2.2.0":
  version: 2.2.0
  resolution: "@headlessui/react@npm:2.2.0"
  dependencies:
    "@floating-ui/react": ^0.26.16
    "@react-aria/focus": ^3.17.1
    "@react-aria/interactions": ^3.21.3
    "@tanstack/react-virtual": ^3.8.1
  peerDependencies:
    react: ^18 || ^19 || ^19.0.0-rc
    react-dom: ^18 || ^19 || ^19.0.0-rc
  checksum: d64b23108e3f0ad4a28753aba5bc3c08ad771d2b9f5a2f3a7a8b4dec5b96fbbcce39fe9404a050af2c1ceafdc29837f5c3dc51ca03ea58c7ee2e30cf8b9b8d16
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 611e0545146f55ddfdd5c20239cfb7911f9d0e28258787c4fc1a1f6214250830c9367aaaeace0096ed90b6739bee1e9c52ad5ba8adaf74ab8b449119303babfe
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": ^0.19.1
    "@humanwhocodes/retry": ^0.3.0
  checksum: f9cb52bb235f8b9c6fcff43a7e500669a38f8d6ce26593404a9b56365a1644e0ed60c720dc65ff6a696b1f85f3563ab055bb554ec8674f2559085ba840e47710
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 7e5517bb51dbea3e02ab6cacef59a8f4b0ca023fc4b0b8cbc40de0ad29f46edd50b897c6e7fba79366a0217e3f48e2da8975056f6c35cfe19d9cc48f1d03c1dd
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.1":
  version: 0.4.1
  resolution: "@humanwhocodes/retry@npm:0.4.1"
  checksum: f11167c28e8266faba470fd273cbaafe2827523492bc18c5623015adb7ed66f46b2e542e3d756fed9ca614300249267814220c2f5f03a59e07fdfa64fc14ad52
  languageName: node
  linkType: hard

"@img/sharp-darwin-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-darwin-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-darwin-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-darwin-arm64":
      optional: true
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-darwin-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-darwin-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-darwin-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-darwin-x64":
      optional: true
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-darwin-arm64@npm:1.0.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-darwin-x64@npm:1.0.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-arm64@npm:1.0.4"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm@npm:1.0.5":
  version: 1.0.5
  resolution: "@img/sharp-libvips-linux-arm@npm:1.0.5"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-s390x@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-s390x@npm:1.0.4"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-x64@npm:1.0.4"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linuxmusl-arm64@npm:1.0.4"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linuxmusl-x64@npm:1.0.4"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linux-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-arm@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-arm@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-arm": 1.0.5
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm":
      optional: true
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-s390x@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-s390x@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-s390x": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-s390x":
      optional: true
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linuxmusl-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linuxmusl-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linuxmusl-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linuxmusl-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-wasm32@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-wasm32@npm:0.33.5"
  dependencies:
    "@emnapi/runtime": ^1.2.0
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@img/sharp-win32-ia32@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-win32-ia32@npm:0.33.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@img/sharp-win32-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-win32-x64@npm:0.33.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: c0687b5227461717aa537fe71a42e356bcd1c43293b3353796a148bf3b0d6f59109def46c22f05b60e29a46f19b2e4676d027959a7c53a6c92b9d5b0d87d0420
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 05df4f2538b3b0f998ea4c1cd34574d0feba216fa5d4ccaef0187d12abf82eafe6021cec8b49f9bb4d90f2ba4582ccc581e72986a5fcf4176ae0cfeb04cf52ec
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 9d3c40d225e139987b50c48988f8717a54a8c994d8a948ee42e1412e08988761d0754d7d10b803061cc3aebf35f92a5dbbab493bd0e1a9ef9e89a2130e83ba34
  languageName: node
  linkType: hard

"@js-sdsl/ordered-map@npm:^4.4.2":
  version: 4.4.2
  resolution: "@js-sdsl/ordered-map@npm:4.4.2"
  checksum: a927ae4ff8565ecb75355cc6886a4f8fadbf2af1268143c96c0cce3ba01261d241c3f4ba77f21f3f017a00f91dfe9e0673e95f830255945c80a0e96c6d30508a
  languageName: node
  linkType: hard

"@js-temporal/polyfill@npm:^0.4.4":
  version: 0.4.4
  resolution: "@js-temporal/polyfill@npm:0.4.4"
  dependencies:
    jsbi: ^4.3.0
    tslib: ^2.4.1
  checksum: 034c00fdc1aa1a1d96f786ebe568f9f85309bcdcdf1d3fc7f7f670b43a64cafb648739e2363af950685a6d7569fe46c88ee8e28054c7d9b47199015d94a3b8a6
  languageName: node
  linkType: hard

"@jsonjoy.com/base64@npm:^1.1.1":
  version: 1.1.2
  resolution: "@jsonjoy.com/base64@npm:1.1.2"
  peerDependencies:
    tslib: 2
  checksum: 00dbf9cbc6ecb3af0e58288a305cc4ee3dfca9efa24443d98061756e8f6de4d6d2d3764bdfde07f2b03e6ce56db27c8a59b490bd134bf3d8122b4c6b394c7010
  languageName: node
  linkType: hard

"@jsonjoy.com/json-pack@npm:^1.0.3":
  version: 1.1.1
  resolution: "@jsonjoy.com/json-pack@npm:1.1.1"
  dependencies:
    "@jsonjoy.com/base64": ^1.1.1
    "@jsonjoy.com/util": ^1.1.2
    hyperdyperid: ^1.2.0
    thingies: ^1.20.0
  peerDependencies:
    tslib: 2
  checksum: bf1065b60c65bc0f3b3c1d496b8c65152ec23ecaefee8a5ff26dc7bc197aff541f15d1e1330bbd8b33b1c25d740406fc3c5f6d9da261ec6f1a753ac08fb0eb85
  languageName: node
  linkType: hard

"@jsonjoy.com/util@npm:^1.1.2, @jsonjoy.com/util@npm:^1.3.0":
  version: 1.5.0
  resolution: "@jsonjoy.com/util@npm:1.5.0"
  peerDependencies:
    tslib: 2
  checksum: 62892928e1223798e3d910be8dde4fdceaddf2ebdd4bdc0c50495b8ee33503317adf7b5118cd8f5a63045e3f232d70e95fb0279828caf1ec392ffeeb7ea129b8
  languageName: node
  linkType: hard

"@lit-labs/ssr-dom-shim@npm:^1.2.0":
  version: 1.3.0
  resolution: "@lit-labs/ssr-dom-shim@npm:1.3.0"
  checksum: c2003e8bb6d39c15b359450da0d5ea8970f7e684127c10abb26066afd8818785a6e43374fa52d25ac93e9714db670ca8a9b2befc9f3426d6e52eef4a592a79d4
  languageName: node
  linkType: hard

"@lit/reactive-element@npm:^2.0.4":
  version: 2.0.4
  resolution: "@lit/reactive-element@npm:2.0.4"
  dependencies:
    "@lit-labs/ssr-dom-shim": ^1.2.0
  checksum: 368d788d9eefdde74e77721e38c78de222dc5ec87d543e0638d0d28f7a8cf530c3d7b49aa8606efeec3f3485abbb22a43b58c2f20c1e6e7f0de266d4c6d125c4
  languageName: node
  linkType: hard

"@mailchimp/mailchimp_marketing@npm:^3.0.80":
  version: 3.0.80
  resolution: "@mailchimp/mailchimp_marketing@npm:3.0.80"
  dependencies:
    dotenv: ^8.2.0
    superagent: 3.8.1
  checksum: a43f69334766bf02fccf4f6076fa38b7e6a53d9d99485547e1e11d23f9188349ac2831a329f15bb8e31be46cca5e22275a120d0d108b80212ed92ad12df709e4
  languageName: node
  linkType: hard

"@mdx-js/esbuild@npm:^3.0.0":
  version: 3.1.0
  resolution: "@mdx-js/esbuild@npm:3.1.0"
  dependencies:
    "@mdx-js/mdx": ^3.0.0
    "@types/unist": ^3.0.0
    source-map: ^0.7.0
    vfile: ^6.0.0
    vfile-message: ^4.0.0
  peerDependencies:
    esbuild: ">=0.14.0"
  checksum: 7f1e37ee140c5aa6addb4cb050032271906af8f85b74a5c664062136b8fe4398df255ba9689f6a3c5785ed0fd2e47198cd53375afba8ebb777f8b7ff8f5a9f57
  languageName: node
  linkType: hard

"@mdx-js/mdx@npm:^3.0.0":
  version: 3.1.0
  resolution: "@mdx-js/mdx@npm:3.1.0"
  dependencies:
    "@types/estree": ^1.0.0
    "@types/estree-jsx": ^1.0.0
    "@types/hast": ^3.0.0
    "@types/mdx": ^2.0.0
    collapse-white-space: ^2.0.0
    devlop: ^1.0.0
    estree-util-is-identifier-name: ^3.0.0
    estree-util-scope: ^1.0.0
    estree-walker: ^3.0.0
    hast-util-to-jsx-runtime: ^2.0.0
    markdown-extensions: ^2.0.0
    recma-build-jsx: ^1.0.0
    recma-jsx: ^1.0.0
    recma-stringify: ^1.0.0
    rehype-recma: ^1.0.0
    remark-mdx: ^3.0.0
    remark-parse: ^11.0.0
    remark-rehype: ^11.0.0
    source-map: ^0.7.0
    unified: ^11.0.0
    unist-util-position-from-estree: ^2.0.0
    unist-util-stringify-position: ^4.0.0
    unist-util-visit: ^5.0.0
    vfile: ^6.0.0
  checksum: 8a1aa72ddb23294ef28903fc7ad32439a8588106949d789477c2e858e6f068c7b979ae4b2296e820987f7c4d75d6781dafb6fe6a514828bb2ab2b81d89548064
  languageName: node
  linkType: hard

"@next/bundle-analyzer@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/bundle-analyzer@npm:15.2.4"
  dependencies:
    webpack-bundle-analyzer: 4.10.1
  checksum: bb99fb49b16dc1921f9e44a109b13926f483cdb72a6dd59c199518286fb0af67cf23901ecabfb5416fafa79d96024a885f4683ebd4054ad77d7fc50d469ad44d
  languageName: node
  linkType: hard

"@next/env@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/env@npm:15.2.4"
  checksum: 8c532d963408766406baeb3b7f018fcdfac6b953d829afbd9d7d649668c20546dd32400259d3b0d894a7a1947116e364cb4a62e9a642cf0cca7c0f1fcf9e7920
  languageName: node
  linkType: hard

"@next/eslint-plugin-next@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/eslint-plugin-next@npm:15.2.4"
  dependencies:
    fast-glob: 3.3.1
  checksum: 5cec3e2631bf959b91df5a1aa495b6f673a2894347441491c67459b26743f90d5e113c4caefe4fa46ef851628cf5543278dcaff21c3e75e9b73efcb9fab31cb7
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-darwin-arm64@npm:15.2.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-darwin-x64@npm:15.2.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-linux-arm64-gnu@npm:15.2.4"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-linux-arm64-musl@npm:15.2.4"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-linux-x64-gnu@npm:15.2.4"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-linux-x64-musl@npm:15.2.4"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-win32-arm64-msvc@npm:15.2.4"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:15.2.4":
  version: 15.2.4
  resolution: "@next/swc-win32-x64-msvc@npm:15.2.4"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@nolyfill/is-core-module@npm:1.0.39":
  version: 1.0.39
  resolution: "@nolyfill/is-core-module@npm:1.0.39"
  checksum: 0d6e098b871eca71d875651288e1f0fa770a63478b0b50479c99dc760c64175a56b5b04f58d5581bbcc6b552b8191ab415eada093d8df9597ab3423c8cac1815
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@opentelemetry/api-logs@npm:0.57.1":
  version: 0.57.1
  resolution: "@opentelemetry/api-logs@npm:0.57.1"
  dependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 93bdc18cda63a66444972aade7560da0825235631bb161d13a42ed388680c9ede731c9926e8c7f8f80f67482429bb312c2cba149036cba03aec438fc85ae1ed2
  languageName: node
  linkType: hard

"@opentelemetry/api@npm:^1.3.0, @opentelemetry/api@npm:^1.9.0":
  version: 1.9.0
  resolution: "@opentelemetry/api@npm:1.9.0"
  checksum: 9e88e59d53ced668f3daaecfd721071c5b85a67dd386f1c6f051d1be54375d850016c881f656ffbe9a03bedae85f7e89c2f2b635313f9c9b195ad033cdc31020
  languageName: node
  linkType: hard

"@opentelemetry/context-async-hooks@npm:1.30.1":
  version: 1.30.1
  resolution: "@opentelemetry/context-async-hooks@npm:1.30.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 9bc42d4be4bf988d30bb7b20215d6aafb141d21e0088ab3c23b177122cfafef20581c2b7c8ff577a0e0e0a18b65249db6d84817d2aa53c7de83583ee1a117897
  languageName: node
  linkType: hard

"@opentelemetry/core@npm:1.30.1, @opentelemetry/core@npm:^1.30.1":
  version: 1.30.1
  resolution: "@opentelemetry/core@npm:1.30.1"
  dependencies:
    "@opentelemetry/semantic-conventions": 1.28.0
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: fe71452fffc6b5efe4bd1f0ab18b0424e744825f9a837f5bf08be80fe6d2c90c443743cb3d220cffad27b7d83dc38e0a7861c91ec965be156394e752c95e583c
  languageName: node
  linkType: hard

"@opentelemetry/exporter-trace-otlp-grpc@npm:^0.57.1":
  version: 0.57.1
  resolution: "@opentelemetry/exporter-trace-otlp-grpc@npm:0.57.1"
  dependencies:
    "@grpc/grpc-js": ^1.7.1
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/otlp-grpc-exporter-base": 0.57.1
    "@opentelemetry/otlp-transformer": 0.57.1
    "@opentelemetry/resources": 1.30.1
    "@opentelemetry/sdk-trace-base": 1.30.1
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 0961270901ba3327a0c1e17f239f4416c69eca2ddd9dab7b5d50a9384e60b2d17f60426aa74c841f13d28cdbb8efe96cd018fd381bbe4a4d80b873ed83fba062
  languageName: node
  linkType: hard

"@opentelemetry/otlp-exporter-base@npm:0.57.1":
  version: 0.57.1
  resolution: "@opentelemetry/otlp-exporter-base@npm:0.57.1"
  dependencies:
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/otlp-transformer": 0.57.1
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 81073d1ae6803357dbaf49014e37992f5769c3d8c9c6fb0e5cabe575b9ffe520acb7647608ec3387e75df5d403bdd9ba321bb6c805009f82646326f7da67a33e
  languageName: node
  linkType: hard

"@opentelemetry/otlp-grpc-exporter-base@npm:0.57.1":
  version: 0.57.1
  resolution: "@opentelemetry/otlp-grpc-exporter-base@npm:0.57.1"
  dependencies:
    "@grpc/grpc-js": ^1.7.1
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/otlp-exporter-base": 0.57.1
    "@opentelemetry/otlp-transformer": 0.57.1
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 0c399858d9ae487775fc9491cd86b691f7d9eca152a9c9217e36eb5dd1b1a3f07082e78b97e59a9bc7bed4c36dbb62748201aa6513d80d4064fb6d1cbb0e5718
  languageName: node
  linkType: hard

"@opentelemetry/otlp-transformer@npm:0.57.1":
  version: 0.57.1
  resolution: "@opentelemetry/otlp-transformer@npm:0.57.1"
  dependencies:
    "@opentelemetry/api-logs": 0.57.1
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/resources": 1.30.1
    "@opentelemetry/sdk-logs": 0.57.1
    "@opentelemetry/sdk-metrics": 1.30.1
    "@opentelemetry/sdk-trace-base": 1.30.1
    protobufjs: ^7.3.0
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: b72cd1f32af6014b0cc2a0f525a59a307a06291df3da648dbff52b4b7ad9f8a98cc17bbb510fd013462d8376ea720d2b2e4875a3d85b064d698029eeb79491ed
  languageName: node
  linkType: hard

"@opentelemetry/propagator-b3@npm:1.30.1":
  version: 1.30.1
  resolution: "@opentelemetry/propagator-b3@npm:1.30.1"
  dependencies:
    "@opentelemetry/core": 1.30.1
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 19e7a7667aff64a2735278e3c2cbd207a2da2f9ae48effca0716deaf18ecf6fb1a539d52ed7fbc5f0eb66fa43a12c4078af75a212451d8cd1a8346b54f3b2515
  languageName: node
  linkType: hard

"@opentelemetry/propagator-jaeger@npm:1.30.1":
  version: 1.30.1
  resolution: "@opentelemetry/propagator-jaeger@npm:1.30.1"
  dependencies:
    "@opentelemetry/core": 1.30.1
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: ac8bab25e80fdbfd8344e415e35a4336fe2c0eb7c708a32b9e39b0d31228ce6ef802b9820b6501642ac0340e1b5387bd1ad42b9a81c3801ebd4436d18dad250f
  languageName: node
  linkType: hard

"@opentelemetry/resources@npm:1.30.1, @opentelemetry/resources@npm:^1.30.1":
  version: 1.30.1
  resolution: "@opentelemetry/resources@npm:1.30.1"
  dependencies:
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/semantic-conventions": 1.28.0
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: a930d52fcdb18bda6d27a5d867493a6aa560fcad9f266af0c5d24c26069253b463f5cb9961577c3ebb1de752ae37ef4e1344009273504e19604ea7495a4028bd
  languageName: node
  linkType: hard

"@opentelemetry/sdk-logs@npm:0.57.1":
  version: 0.57.1
  resolution: "@opentelemetry/sdk-logs@npm:0.57.1"
  dependencies:
    "@opentelemetry/api-logs": 0.57.1
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/resources": 1.30.1
  peerDependencies:
    "@opentelemetry/api": ">=1.4.0 <1.10.0"
  checksum: 742dd4e9b266f7eb7fc4e02b559e1873d7bb6c243879ad379ab1ae992d0691c5083df1428c191d1ac99671a2896e2905547c121a52df3c5339ac4f02743fdf03
  languageName: node
  linkType: hard

"@opentelemetry/sdk-metrics@npm:1.30.1":
  version: 1.30.1
  resolution: "@opentelemetry/sdk-metrics@npm:1.30.1"
  dependencies:
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/resources": 1.30.1
  peerDependencies:
    "@opentelemetry/api": ">=1.3.0 <1.10.0"
  checksum: b74d16b1a0550e840fea4e7585162d3f3f817aef5e6991e86c1d1808c1e18ec32f6e32982756622671505c3d224976e1f6c11fb4b49d39f281fd7848b57277a0
  languageName: node
  linkType: hard

"@opentelemetry/sdk-trace-base@npm:1.30.1, @opentelemetry/sdk-trace-base@npm:^1.30.1":
  version: 1.30.1
  resolution: "@opentelemetry/sdk-trace-base@npm:1.30.1"
  dependencies:
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/resources": 1.30.1
    "@opentelemetry/semantic-conventions": 1.28.0
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 212c19fb2595d5abc9eb6728946acacb4ca760761625217209d66ad9e3b83efa65fe0c68917a4ac5fb800070f241a4ebdc89e8d6e016134ae95cb8925cf22440
  languageName: node
  linkType: hard

"@opentelemetry/sdk-trace-node@npm:^1.30.1":
  version: 1.30.1
  resolution: "@opentelemetry/sdk-trace-node@npm:1.30.1"
  dependencies:
    "@opentelemetry/context-async-hooks": 1.30.1
    "@opentelemetry/core": 1.30.1
    "@opentelemetry/propagator-b3": 1.30.1
    "@opentelemetry/propagator-jaeger": 1.30.1
    "@opentelemetry/sdk-trace-base": 1.30.1
    semver: ^7.5.2
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: e9122c45b509c11130640d6f5bc621bd547771919dcb6fb0fee0d244c9b5a07d3f721dd338eddcadd1b1d6ef0111de79bbc7a862115fa23e7ed28bca39738c67
  languageName: node
  linkType: hard

"@opentelemetry/semantic-conventions@npm:1.28.0":
  version: 1.28.0
  resolution: "@opentelemetry/semantic-conventions@npm:1.28.0"
  checksum: 1d708afa654990236cdb6b5da84f7ab899b70bff9f753bc49d93616a5c7f7f339ba1eba6a9fbb57dee596995334f4e7effa57a4624741882ab5b3c419c3511e2
  languageName: node
  linkType: hard

"@opentelemetry/semantic-conventions@npm:^1.28.0":
  version: 1.29.0
  resolution: "@opentelemetry/semantic-conventions@npm:1.29.0"
  checksum: c2ae975a7f8201a15eab209a1f7de0a9daf14a3331e2c5aa6c75171af0c867054b4026adc5c67787ca9a19a8a46806008474a619d480632d3f4c79d336256c20
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.1.0":
  version: 0.1.1
  resolution: "@pkgr/core@npm:0.1.1"
  checksum: 6f25fd2e3008f259c77207ac9915b02f1628420403b2630c92a07ff963129238c9262afc9e84344c7a23b5cc1f3965e2cd17e3798219f5fd78a63d144d3cceba
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.24":
  version: 1.0.0-next.28
  resolution: "@polka/url@npm:1.0.0-next.28"
  checksum: 7402aaf1de781d0eb0870d50cbcd394f949aee11b38a267a5c3b4e3cfee117e920693e6e93ce24c87ae2d477a59634f39d9edde8e86471cae756839b07c79af7
  languageName: node
  linkType: hard

"@protobufjs/aspromise@npm:^1.1.1, @protobufjs/aspromise@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/aspromise@npm:1.1.2"
  checksum: 011fe7ef0826b0fd1a95935a033a3c0fd08483903e1aa8f8b4e0704e3233406abb9ee25350ec0c20bbecb2aad8da0dcea58b392bbd77d6690736f02c143865d2
  languageName: node
  linkType: hard

"@protobufjs/base64@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/base64@npm:1.1.2"
  checksum: 67173ac34de1e242c55da52c2f5bdc65505d82453893f9b51dc74af9fe4c065cf4a657a4538e91b0d4a1a1e0a0642215e31894c31650ff6e3831471061e1ee9e
  languageName: node
  linkType: hard

"@protobufjs/codegen@npm:^2.0.4":
  version: 2.0.4
  resolution: "@protobufjs/codegen@npm:2.0.4"
  checksum: 59240c850b1d3d0b56d8f8098dd04787dcaec5c5bd8de186fa548de86b86076e1c50e80144b90335e705a044edf5bc8b0998548474c2a10a98c7e004a1547e4b
  languageName: node
  linkType: hard

"@protobufjs/eventemitter@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/eventemitter@npm:1.1.0"
  checksum: 0369163a3d226851682f855f81413cbf166cd98f131edb94a0f67f79e75342d86e89df9d7a1df08ac28be2bc77e0a7f0200526bb6c2a407abbfee1f0262d5fd7
  languageName: node
  linkType: hard

"@protobufjs/fetch@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/fetch@npm:1.1.0"
  dependencies:
    "@protobufjs/aspromise": ^1.1.1
    "@protobufjs/inquire": ^1.1.0
  checksum: 3fce7e09eb3f1171dd55a192066450f65324fd5f7cc01a431df01bb00d0a895e6bfb5b0c5561ce157ee1d886349c90703d10a4e11a1a256418ff591b969b3477
  languageName: node
  linkType: hard

"@protobufjs/float@npm:^1.0.2":
  version: 1.0.2
  resolution: "@protobufjs/float@npm:1.0.2"
  checksum: 5781e1241270b8bd1591d324ca9e3a3128d2f768077a446187a049e36505e91bc4156ed5ac3159c3ce3d2ba3743dbc757b051b2d723eea9cd367bfd54ab29b2f
  languageName: node
  linkType: hard

"@protobufjs/inquire@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/inquire@npm:1.1.0"
  checksum: ca06f02eaf65ca36fb7498fc3492b7fc087bfcc85c702bac5b86fad34b692bdce4990e0ef444c1e2aea8c034227bd1f0484be02810d5d7e931c55445555646f4
  languageName: node
  linkType: hard

"@protobufjs/path@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/path@npm:1.1.2"
  checksum: 856eeb532b16a7aac071cacde5c5620df800db4c80cee6dbc56380524736205aae21e5ae47739114bf669ab5e8ba0e767a282ad894f3b5e124197cb9224445ee
  languageName: node
  linkType: hard

"@protobufjs/pool@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/pool@npm:1.1.0"
  checksum: d6a34fbbd24f729e2a10ee915b74e1d77d52214de626b921b2d77288bd8f2386808da2315080f2905761527cceffe7ec34c7647bd21a5ae41a25e8212ff79451
  languageName: node
  linkType: hard

"@protobufjs/utf8@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/utf8@npm:1.1.0"
  checksum: f9bf3163d13aaa3b6f5e6fbf37a116e094ea021c0e1f2a7ccd0e12a29e2ce08dafba4e8b36e13f8ed7397e1591610ce880ed1289af4d66cf4ace8a36a9557278
  languageName: node
  linkType: hard

"@radix-ui/react-compose-refs@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-compose-refs@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 1be82f9f7fab96cc10f167a2e4f976e0135a63d473334f664c06f02af13bc5ea1994cb0505f89ed190d756cb65d57506721c030908af07e49b9e3cfd36044f33
  languageName: node
  linkType: hard

"@radix-ui/react-portal@npm:^1.0.1":
  version: 1.1.4
  resolution: "@radix-ui/react-portal@npm:1.1.4"
  dependencies:
    "@radix-ui/react-primitive": 2.0.2
    "@radix-ui/react-use-layout-effect": 1.1.0
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 7797c53d071c762e234c92b5ca721f97ba300969fa9d630e808d436a896082b7c31553cdc0ed1cbfd46b76fe2ddbe5e3a0790f9ff2f6542923b896301a634bbd
  languageName: node
  linkType: hard

"@radix-ui/react-primitive@npm:2.0.2":
  version: 2.0.2
  resolution: "@radix-ui/react-primitive@npm:2.0.2"
  dependencies:
    "@radix-ui/react-slot": 1.1.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 3a6144ed164322b1135f6f774bfd23c7c4c5340db8a1022d05c9c7ad41ba187299a4894caae634e94a1d73b5f69305318a47b41e6dc7806fb5923fa05dd39d40
  languageName: node
  linkType: hard

"@radix-ui/react-slot@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-slot@npm:1.1.2"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: f341cd66b284061a5147a67f6d7b8a7337a4c076b97ce087bcb1358fa36e9714ef988cc7ea2c2ed3b6ec3f17adafd2460851b31ed8f4dd73ea22b0b11e22ab97
  languageName: node
  linkType: hard

"@radix-ui/react-use-layout-effect@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-use-layout-effect@npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 271ea0bf1cd74718895a68414a6e95537737f36e02ad08eeb61a82b229d6abda9cff3135a479e134e1f0ce2c3ff97bb85babbdce751985fb755a39b231d7ccf2
  languageName: node
  linkType: hard

"@reach/observe-rect@npm:^1.1.0":
  version: 1.2.0
  resolution: "@reach/observe-rect@npm:1.2.0"
  checksum: 7dd903eeaad0e22c6d973bd26265d91eadba56ab5134701ceb3e85214db75339fae94aa7e8b88a65e8daa64bc7cf1b915d4ffcdfd324466b561dc6adc3c6e070
  languageName: node
  linkType: hard

"@react-aria/focus@npm:^3.17.1":
  version: 3.19.1
  resolution: "@react-aria/focus@npm:3.19.1"
  dependencies:
    "@react-aria/interactions": ^3.23.0
    "@react-aria/utils": ^3.27.0
    "@react-types/shared": ^3.27.0
    "@swc/helpers": ^0.5.0
    clsx: ^2.0.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 1ad0714617aefbcd164f37fcbbd82b3edf7f1148983b67a7c08b7f6c55d05f03b141310d70dda8e5bbb866e0790d345c6fa6038fd4fdb6b4226b65aba664513e
  languageName: node
  linkType: hard

"@react-aria/interactions@npm:^3.21.3, @react-aria/interactions@npm:^3.23.0":
  version: 3.23.0
  resolution: "@react-aria/interactions@npm:3.23.0"
  dependencies:
    "@react-aria/ssr": ^3.9.7
    "@react-aria/utils": ^3.27.0
    "@react-types/shared": ^3.27.0
    "@swc/helpers": ^0.5.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 43d47bc2b5f1afa0b47cfba9514b6e0daee6d0d2507ae0f5dbb18f6b3f90e64a9de99fd4787eb663517ca84576de9ef7d731f490102848dcecf886babf3d2f50
  languageName: node
  linkType: hard

"@react-aria/ssr@npm:^3.9.7":
  version: 3.9.7
  resolution: "@react-aria/ssr@npm:3.9.7"
  dependencies:
    "@swc/helpers": ^0.5.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10ad277d8c4db6cf9b546f5800dd084451a4a8173a57b06c6597fd39375526a81f1fb398fe46558d372f8660d33c0a09a2580e0529351d76b2c8938482597b3f
  languageName: node
  linkType: hard

"@react-aria/utils@npm:^3.27.0":
  version: 3.27.0
  resolution: "@react-aria/utils@npm:3.27.0"
  dependencies:
    "@react-aria/ssr": ^3.9.7
    "@react-stately/utils": ^3.10.5
    "@react-types/shared": ^3.27.0
    "@swc/helpers": ^0.5.0
    clsx: ^2.0.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: c93031bd77378483ad507d424d42341a164e2232007cd44397bcd197d0363a4164b201238fdfb47692bdc95ec92bca6c3c311d617dad5b6c2f3a67c6e0a42981
  languageName: node
  linkType: hard

"@react-stately/utils@npm:^3.10.5":
  version: 3.10.5
  resolution: "@react-stately/utils@npm:3.10.5"
  dependencies:
    "@swc/helpers": ^0.5.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 4f4292ccf7bb86578a20b354cf9569f88d2d50ecb2e10ac6046fab3b9eb2175f734acf1b9bd87787e439220b912785a54551a724ab285f03e4f33b2942831f57
  languageName: node
  linkType: hard

"@react-types/shared@npm:^3.27.0":
  version: 3.27.0
  resolution: "@react-types/shared@npm:3.27.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 72de4ba6f7e168e6c94cacd3c100c280df9ead41bd9d93f0f8b3e5ad5e8d75d96738b4dde9fc5d1907733b54baca63cd474034691a5a0f22120e1a4657ca3ad0
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 17d04adf404e04c1e61391ed97bca5117d4c2767a76ae3e879390d6dec7b317fcae68afbf9e98badee075d0b64fa60f287729c4942021b4d19cd01db77385c01
  languageName: node
  linkType: hard

"@rushstack/eslint-patch@npm:^1.10.3":
  version: 1.10.5
  resolution: "@rushstack/eslint-patch@npm:1.10.5"
  checksum: c7df90efeb77e4311f70549c1b0c41455e3a4f0c0cf2696e560d9a535f129d63ab84c98d0a3de95ed2d369d5281b541af819f99002bfd38e185e59c355b58d69
  languageName: node
  linkType: hard

"@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3fc8e35d16f5abe0af5efe5851f27581225ac405d6a1ca44cda0df064cddfcc29a428c48c2e4bef6cebf627c9ac2f652a096030edb02cf5a120ce28d3c234710
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ff992893c6c4ac802713ba3a97c13be34e62e6d981c813af40daabcd676df68a72a61bd1e692bb1eda3587f1b1d700ea462222ae2153bb0f46886632d4f88d08
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0fb691b63a21bac00da3aa2dccec50d0d5a5b347ff408d60803b84410d8af168f2656e4ba1ee1f24dab0ae4e4af77901f2928752bb0434c1f6788133ec599ec8
  languageName: node
  linkType: hard

"@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1edda65ef4f4dd8f021143c8ec276a08f6baa6f733b8e8ee2e7775597bf6b97afb47fdeefd579d6ae6c959fe2e634f55cd61d99377631212228c8cfb351b8921
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 876cec891488992e6a9aebb8155e2bea4ec461b4718c51de36e988e00e271c6d9d01ef6be17b9effd44b2b3d7db0b41c161a5904a46ae6f38b26b387ad7f3709
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: be0e2d391164428327d9ec469a52cea7d93189c6b0e2c290999e048f597d777852f701c64dca44cd45b31ed14a7f859520326e2e4ad7c3a4545d0aa235bc7e9a
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85b434a57572f53bd2b9f0606f253e1fcf57b4a8c554ec3f2d43ed17f50d8cae200cb3aaf1ec9d626e1456e8b135dce530ae047eb0bed6d4bf98a752d6640459
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-svg-component@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-transform-svg-component@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 04e2023d75693eeb0890341c40e449881184663056c249be7e5c80168e4aabb0fadd255e8d5d2dbf54b8c2a6e700efba994377135bfa4060dc4a2e860116ef8c
  languageName: node
  linkType: hard

"@svgr/babel-preset@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-preset@npm:8.1.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute": 8.0.0
    "@svgr/babel-plugin-remove-jsx-attribute": 8.0.0
    "@svgr/babel-plugin-remove-jsx-empty-expression": 8.0.0
    "@svgr/babel-plugin-replace-jsx-attribute-value": 8.0.0
    "@svgr/babel-plugin-svg-dynamic-title": 8.0.0
    "@svgr/babel-plugin-svg-em-dimensions": 8.0.0
    "@svgr/babel-plugin-transform-react-native-svg": 8.1.0
    "@svgr/babel-plugin-transform-svg-component": 8.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a67930f080b8891e1e8e2595716b879c944d253112bae763dce59807ba23454d162216c8d66a0a0e3d4f38a649ecd6c387e545d1e1261dd69a68e9a3392ee08
  languageName: node
  linkType: hard

"@svgr/core@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/core@npm:8.1.0"
  dependencies:
    "@babel/core": ^7.21.3
    "@svgr/babel-preset": 8.1.0
    camelcase: ^6.2.0
    cosmiconfig: ^8.1.3
    snake-case: ^3.0.4
  checksum: da4a12865c7dc59829d58df8bd232d6c85b7115fda40da0d2f844a1a51886e2e945560596ecfc0345d37837ac457de86a931e8b8d8550e729e0c688c02250d8a
  languageName: node
  linkType: hard

"@svgr/hast-util-to-babel-ast@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/hast-util-to-babel-ast@npm:8.0.0"
  dependencies:
    "@babel/types": ^7.21.3
    entities: ^4.4.0
  checksum: 88401281a38bbc7527e65ff5437970414391a86158ef4b4046c89764c156d2d39ecd7cce77be8a51994c9fb3249170cb1eb8b9128b62faaa81743ef6ed3534ab
  languageName: node
  linkType: hard

"@svgr/plugin-jsx@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-jsx@npm:8.1.0"
  dependencies:
    "@babel/core": ^7.21.3
    "@svgr/babel-preset": 8.1.0
    "@svgr/hast-util-to-babel-ast": 8.0.0
    svg-parser: ^2.0.4
  peerDependencies:
    "@svgr/core": "*"
  checksum: 0418a9780753d3544912ee2dad5d2cf8d12e1ba74df8053651b3886aeda54d5f0f7d2dece0af5e0d838332c4f139a57f0dabaa3ca1afa4d1a765efce6a7656f2
  languageName: node
  linkType: hard

"@svgr/plugin-svgo@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-svgo@npm:8.1.0"
  dependencies:
    cosmiconfig: ^8.1.3
    deepmerge: ^4.3.1
    svgo: ^3.0.2
  peerDependencies:
    "@svgr/core": "*"
  checksum: 59d9d214cebaacca9ca71a561f463d8b7e5a68ca9443e4792a42d903acd52259b1790c0680bc6afecc3f00a255a6cbd7ea278a9f625bac443620ea58a590c2d0
  languageName: node
  linkType: hard

"@svgr/webpack@npm:^8.0.1":
  version: 8.1.0
  resolution: "@svgr/webpack@npm:8.1.0"
  dependencies:
    "@babel/core": ^7.21.3
    "@babel/plugin-transform-react-constant-elements": ^7.21.3
    "@babel/preset-env": ^7.20.2
    "@babel/preset-react": ^7.18.6
    "@babel/preset-typescript": ^7.21.0
    "@svgr/core": 8.1.0
    "@svgr/plugin-jsx": 8.1.0
    "@svgr/plugin-svgo": 8.1.0
  checksum: c6eec5b0cf2fb2ecd3a7a362d272eda35330b17c76802a3481f499b5d07ff8f87b31d2571043bff399b051a1767b1e2e499dbf186104d1c06d76f9f1535fac01
  languageName: node
  linkType: hard

"@swc/counter@npm:0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.15, @swc/helpers@npm:^0.5.0":
  version: 0.5.15
  resolution: "@swc/helpers@npm:0.5.15"
  dependencies:
    tslib: ^2.8.0
  checksum: 1a9e0dbb792b2d1e0c914d69c201dbc96af3a0e6e6e8cf5a7f7d6a5d7b0e8b762915cd4447acb6b040e2ecc1ed49822875a7239f99a2d63c96c3c3407fb6fccf
  languageName: node
  linkType: hard

"@tailwindcss/forms@npm:^0.5.9":
  version: 0.5.10
  resolution: "@tailwindcss/forms@npm:0.5.10"
  dependencies:
    mini-svg-data-uri: ^1.2.3
  peerDependencies:
    tailwindcss: ">=3.0.0 || >= 3.0.0-alpha.1 || >= 4.0.0-alpha.20 || >= 4.0.0-beta.1"
  checksum: 4526d02edccc4e44599d9588f83e4ac3e9435d137da5638653de2e74d5b612ade449a8c26d075be21692c1ac00a514aaffdb6723e526e3c8314c9a75a9f45979
  languageName: node
  linkType: hard

"@tailwindcss/node@npm:^4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/node@npm:4.0.5"
  dependencies:
    enhanced-resolve: ^5.18.0
    jiti: ^2.4.2
    tailwindcss: 4.0.5
  checksum: 5368c60ac12023ec3a9d7913105476a9a8a81d01fbf2d1c74b99abd5fcb3a6947f7e57c27874e5843d10ff439ecd6979177c346cc91039b48247c47543217803
  languageName: node
  linkType: hard

"@tailwindcss/oxide-android-arm64@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-android-arm64@npm:4.0.5"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-darwin-arm64@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-darwin-arm64@npm:4.0.5"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-darwin-x64@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-darwin-x64@npm:4.0.5"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-freebsd-x64@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-freebsd-x64@npm:4.0.5"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm-gnueabihf@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-linux-arm-gnueabihf@npm:4.0.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm64-gnu@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-linux-arm64-gnu@npm:4.0.5"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm64-musl@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-linux-arm64-musl@npm:4.0.5"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-x64-gnu@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-linux-x64-gnu@npm:4.0.5"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-x64-musl@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-linux-x64-musl@npm:4.0.5"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@tailwindcss/oxide-win32-arm64-msvc@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-win32-arm64-msvc@npm:4.0.5"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-win32-x64-msvc@npm:4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide-win32-x64-msvc@npm:4.0.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide@npm:^4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/oxide@npm:4.0.5"
  dependencies:
    "@tailwindcss/oxide-android-arm64": 4.0.5
    "@tailwindcss/oxide-darwin-arm64": 4.0.5
    "@tailwindcss/oxide-darwin-x64": 4.0.5
    "@tailwindcss/oxide-freebsd-x64": 4.0.5
    "@tailwindcss/oxide-linux-arm-gnueabihf": 4.0.5
    "@tailwindcss/oxide-linux-arm64-gnu": 4.0.5
    "@tailwindcss/oxide-linux-arm64-musl": 4.0.5
    "@tailwindcss/oxide-linux-x64-gnu": 4.0.5
    "@tailwindcss/oxide-linux-x64-musl": 4.0.5
    "@tailwindcss/oxide-win32-arm64-msvc": 4.0.5
    "@tailwindcss/oxide-win32-x64-msvc": 4.0.5
  dependenciesMeta:
    "@tailwindcss/oxide-android-arm64":
      optional: true
    "@tailwindcss/oxide-darwin-arm64":
      optional: true
    "@tailwindcss/oxide-darwin-x64":
      optional: true
    "@tailwindcss/oxide-freebsd-x64":
      optional: true
    "@tailwindcss/oxide-linux-arm-gnueabihf":
      optional: true
    "@tailwindcss/oxide-linux-arm64-gnu":
      optional: true
    "@tailwindcss/oxide-linux-arm64-musl":
      optional: true
    "@tailwindcss/oxide-linux-x64-gnu":
      optional: true
    "@tailwindcss/oxide-linux-x64-musl":
      optional: true
    "@tailwindcss/oxide-win32-arm64-msvc":
      optional: true
    "@tailwindcss/oxide-win32-x64-msvc":
      optional: true
  checksum: 839615eeffc6d6173471409cf7c89bef446bdc916648f4090a76793957fb583943b7e3ae2e4418b15f75120ec6e7bd9966d3e1f392ff09a74131cc8b61faa8eb
  languageName: node
  linkType: hard

"@tailwindcss/postcss@npm:^4.0.5":
  version: 4.0.5
  resolution: "@tailwindcss/postcss@npm:4.0.5"
  dependencies:
    "@alloc/quick-lru": ^5.2.0
    "@tailwindcss/node": ^4.0.5
    "@tailwindcss/oxide": ^4.0.5
    lightningcss: ^1.29.1
    postcss: ^8.4.41
    tailwindcss: 4.0.5
  checksum: 8e48eb24b19cdb3db031bc70884efd2df6332ea962e8fb298fb2d3963413e631792bd82c2582c08ddcaebf01150ca8bbc46550ce762066d2cbf0ad4d299287ec
  languageName: node
  linkType: hard

"@tailwindcss/typography@npm:^0.5.15":
  version: 0.5.16
  resolution: "@tailwindcss/typography@npm:0.5.16"
  dependencies:
    lodash.castarray: ^4.4.0
    lodash.isplainobject: ^4.0.6
    lodash.merge: ^4.6.2
    postcss-selector-parser: 6.0.10
  peerDependencies:
    tailwindcss: "*"
  checksum: 700b74aa4bda40db9f9c6bce5dbfbd38c48999c9999972260b8392c1f7b67a542bbf31f33d6a81497b4d34dfaebd6dde1077c0e1e4a39c8263c759c2c148b961
  languageName: node
  linkType: hard

"@tanstack/react-virtual@npm:^3.8.1":
  version: 3.13.0
  resolution: "@tanstack/react-virtual@npm:3.13.0"
  dependencies:
    "@tanstack/virtual-core": 3.13.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 0cc6fcc63d68af698d79b455fa6a44115ee3abfb41bd2a52fc96094cb4760743989e593871b3b872021f966c7ecc90eb45e85ccfc70446fff44ce8e6296cc76f
  languageName: node
  linkType: hard

"@tanstack/virtual-core@npm:3.13.0":
  version: 3.13.0
  resolution: "@tanstack/virtual-core@npm:3.13.0"
  checksum: 0cbead3350002bea1f8353e091d3d8d3d9ba7815f4a0eb359bc927b7b7f39c9530c1dfa15f8d75fe5f47621c8f55be57643133b8fe728af09f7ea0578016f78d
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 11226c39b52b391719a2a92e10183e4260d9651f86edced166da1d95f39a0a1eaa470e44d14ac685ccd6d3df7e2002433782872c0feeb260d61e80f21250e65c
  languageName: node
  linkType: hard

"@types/acorn@npm:^4.0.0":
  version: 4.0.6
  resolution: "@types/acorn@npm:4.0.6"
  dependencies:
    "@types/estree": "*"
  checksum: 60e1fd28af18d6cb54a93a7231c7c18774a9a8739c9b179e9e8750dca631e10cbef2d82b02830ea3f557b1d121e6406441e9e1250bd492dc81d4b3456e76e4d4
  languageName: node
  linkType: hard

"@types/debug@npm:^4.0.0":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "*"
  checksum: 47876a852de8240bfdaf7481357af2b88cb660d30c72e73789abf00c499d6bc7cd5e52f41c915d1b9cd8ec9fef5b05688d7b7aef17f7f272c2d04679508d1053
  languageName: node
  linkType: hard

"@types/estree-jsx@npm:^1.0.0":
  version: 1.0.5
  resolution: "@types/estree-jsx@npm:1.0.5"
  dependencies:
    "@types/estree": "*"
  checksum: a028ab0cd7b2950168a05c6a86026eb3a36a54a4adfae57f13911d7b49dffe573d9c2b28421b2d029b49b3d02fcd686611be2622dc3dad6d9791166c083f6008
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 8825d6e729e16445d9a1dd2fb1db2edc5ed400799064cd4d028150701031af012ba30d6d03fe9df40f4d7a437d0de6d2b256020152b7b09bde9f2e420afdffd9
  languageName: node
  linkType: hard

"@types/gensync@npm:^1.0.0":
  version: 1.0.4
  resolution: "@types/gensync@npm:1.0.4"
  checksum: 99c3aa0d3f1198973c7e51bea5947b815f3338ce89ce09a39ac8abb41cd844c5b95189da254ea45e50a395fe25fd215664d8ca76c5438814963597afb01f686e
  languageName: node
  linkType: hard

"@types/hast@npm:^2.0.0":
  version: 2.3.10
  resolution: "@types/hast@npm:2.3.10"
  dependencies:
    "@types/unist": ^2
  checksum: 41531b7fbf590b02452996fc63272479c20a07269e370bd6514982cbcd1819b4b84d3ea620f2410d1b9541a23d08ce2eeb0a592145d05e00e249c3d56700d460
  languageName: node
  linkType: hard

"@types/hast@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/hast@npm:3.0.4"
  dependencies:
    "@types/unist": "*"
  checksum: 7a973e8d16fcdf3936090fa2280f408fb2b6a4f13b42edeb5fbd614efe042b82eac68e298e556d50f6b4ad585a3a93c353e9c826feccdc77af59de8dd400d044
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: e60b153664572116dfea673c5bda7778dbff150498f44f998e34b5886d8afc47f16799280e4b6e241c0472aef1bc36add771c569c68fc5125fc2ae519a3eb9ac
  languageName: node
  linkType: hard

"@types/katex@npm:^0.16.0":
  version: 0.16.7
  resolution: "@types/katex@npm:0.16.7"
  checksum: 4fd15d93553be97c02c064e16be18d7ccbabf66ec72a9dc7fd5bfa47f0c7581da2f942f693c7cb59499de4c843c2189796e49c9647d336cbd52b777b6722a95a
  languageName: node
  linkType: hard

"@types/mdast@npm:^4.0.0":
  version: 4.0.4
  resolution: "@types/mdast@npm:4.0.4"
  dependencies:
    "@types/unist": "*"
  checksum: 20c4e9574cc409db662a35cba52b068b91eb696b3049e94321219d47d34c8ccc99a142be5c76c80a538b612457b03586bc2f6b727a3e9e7530f4c8568f6282ee
  languageName: node
  linkType: hard

"@types/mdx@npm:^2.0.0, @types/mdx@npm:^2.0.12":
  version: 2.0.13
  resolution: "@types/mdx@npm:2.0.13"
  checksum: 195137b548e75a85f0558bb1ca5088aff1c01ae0fc64454da06085b7513a043356d0bb51ed559d3cbc7ad724ccd8cef2a7d07d014b89a47a74dff8875ceb3b15
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 532d2ebb91937ccc4a89389715e5b47d4c66e708d15942fe6cc25add6dc37b2be058230a327dd50f43f89b8b6d5d52b74685a9e8f70516edfc9bdd6be910eff4
  languageName: node
  linkType: hard

"@types/node@npm:>=13.7.0":
  version: 22.13.1
  resolution: "@types/node@npm:22.13.1"
  dependencies:
    undici-types: ~6.20.0
  checksum: a0759e4bedc3fe892c3ddef5fa9cb5251f9c5b24defc1a389438ea3b5b727c481c1a9bc94bae4ecc7426c89ad293cd66633d163da1ab14d74d358cbec9e1ce31
  languageName: node
  linkType: hard

"@types/prismjs@npm:^1.0.0":
  version: 1.26.5
  resolution: "@types/prismjs@npm:1.26.5"
  checksum: d208b04ee9b6de6b2dc916439a81baa47e64ab3659a66d3d34bc3e42faccba9d4b26f590d76f97f7978d1dfaafa0861f81172b1e3c68696dd7a42d73aaaf5b7b
  languageName: node
  linkType: hard

"@types/react@npm:^19.0.8":
  version: 19.0.8
  resolution: "@types/react@npm:19.0.8"
  dependencies:
    csstype: ^3.0.2
  checksum: 80dd2e7fa4b3e0ea2d883c21317563f4af1c4d90a6250c8bcbc052079304dc3335369267026004ed5d7cac09c7b0026e02e71ae5cca3150643507e353219fe47
  languageName: node
  linkType: hard

"@types/resolve@npm:^1.17.1":
  version: 1.20.6
  resolution: "@types/resolve@npm:1.20.6"
  checksum: dc35f5517606b6687cd971c0281ac58bdee2c50c051b030f04647d3991688be2259c304ee97e5b5d4b9936072c36767eb5933b54611a407d6557972bb6fea4f6
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.2":
  version: 2.0.7
  resolution: "@types/trusted-types@npm:2.0.7"
  checksum: 8e4202766a65877efcf5d5a41b7dd458480b36195e580a3b1085ad21e948bc417d55d6f8af1fd2a7ad008015d4117d5fdfe432731157da3c68678487174e4ba3
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^3.0.0":
  version: 3.0.3
  resolution: "@types/unist@npm:3.0.3"
  checksum: 96e6453da9e075aaef1dc22482463898198acdc1eeb99b465e65e34303e2ec1e3b1ed4469a9118275ec284dc98019f63c3f5d49422f0e4ac707e5ab90fb3b71a
  languageName: node
  linkType: hard

"@types/unist@npm:^2, @types/unist@npm:^2.0.0":
  version: 2.0.11
  resolution: "@types/unist@npm:2.0.11"
  checksum: 6d436e832bc35c6dde9f056ac515ebf2b3384a1d7f63679d12358766f9b313368077402e9c1126a14d827f10370a5485e628bf61aa91117cf4fc882423191a4e
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0, @typescript-eslint/eslint-plugin@npm:^8.12.0":
  version: 8.23.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.23.0"
  dependencies:
    "@eslint-community/regexpp": ^4.10.0
    "@typescript-eslint/scope-manager": 8.23.0
    "@typescript-eslint/type-utils": 8.23.0
    "@typescript-eslint/utils": 8.23.0
    "@typescript-eslint/visitor-keys": 8.23.0
    graphemer: ^1.4.0
    ignore: ^5.3.1
    natural-compare: ^1.4.0
    ts-api-utils: ^2.0.1
  peerDependencies:
    "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: b7dd9cbba9ff5094ce312b5757569cd3a29cdfaf26026282973ecf2c1b80314b660a54b4bf4e0faff7f9a69a093a14245552262feb297f739dbcc0e3d1784122
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0, @typescript-eslint/parser@npm:^8.12.0":
  version: 8.23.0
  resolution: "@typescript-eslint/parser@npm:8.23.0"
  dependencies:
    "@typescript-eslint/scope-manager": 8.23.0
    "@typescript-eslint/types": 8.23.0
    "@typescript-eslint/typescript-estree": 8.23.0
    "@typescript-eslint/visitor-keys": 8.23.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 3a17e8c4f1c70d1153ad644e3148a022cefdb1fbc4dc6f085ed15b09d38f05056f4bcad9ff06255372b8d5309194a7697d581d0577873d67e3891230da4ac3df
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.23.0":
  version: 8.23.0
  resolution: "@typescript-eslint/scope-manager@npm:8.23.0"
  dependencies:
    "@typescript-eslint/types": 8.23.0
    "@typescript-eslint/visitor-keys": 8.23.0
  checksum: cb2772a1f4a973ebcd8130e90ef864a792e2f65170a97def5103f934b028420d5d1d6a689bdeda16dd07eb6c85c1e0e7ff4edddd4acccd63585d07bc5936af09
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.23.0":
  version: 8.23.0
  resolution: "@typescript-eslint/type-utils@npm:8.23.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 8.23.0
    "@typescript-eslint/utils": 8.23.0
    debug: ^4.3.4
    ts-api-utils: ^2.0.1
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: 523e333dd18da5587141a5f3c7d5eddee73a660379191366c1f7283ce68bf0c623a0603f50197892c27281bbe40f50283e57e552bcf03025132fbc4f70f2f705
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.23.0":
  version: 8.23.0
  resolution: "@typescript-eslint/types@npm:8.23.0"
  checksum: 6f3b0f57181b275d15be1134ca9b000da1314696cdb2641013dd92df5d5daac54af9832b8efe3374082404f154f0c5730fdb495bc8eadfd29aa62c1260b4cdc3
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.23.0":
  version: 8.23.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.23.0"
  dependencies:
    "@typescript-eslint/types": 8.23.0
    "@typescript-eslint/visitor-keys": 8.23.0
    debug: ^4.3.4
    fast-glob: ^3.3.2
    is-glob: ^4.0.3
    minimatch: ^9.0.4
    semver: ^7.6.0
    ts-api-utils: ^2.0.1
  peerDependencies:
    typescript: ">=4.8.4 <5.8.0"
  checksum: 16ccabac2560f85c5e0c92e87ff6dcad46b8d82d691b3cce552e529c2d64b90a54e2f0ec50a74bc34085b27b8f1982ae63e53793b01cdd45fd06a087f3d6eef0
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.23.0":
  version: 8.23.0
  resolution: "@typescript-eslint/utils@npm:8.23.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@typescript-eslint/scope-manager": 8.23.0
    "@typescript-eslint/types": 8.23.0
    "@typescript-eslint/typescript-estree": 8.23.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.8.0"
  checksum: fba721abac60a67f34b8f8867619dc6bf624d4031599b6090109a17d8bc16e139d8192650d8c8da52769fd002fe967ca5a4a9f1150abed7761ed02a4f594f323
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.23.0":
  version: 8.23.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.23.0"
  dependencies:
    "@typescript-eslint/types": 8.23.0
    eslint-visitor-keys: ^4.2.0
  checksum: 20196da5f22d01da9848146d28c6973ea5345aba02486cdb0fcfea6317135a05e9ebea1a8c35f4a0ef436f056f946e98a626ae6e729a0ea36c68e46c567ba85d
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.0.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 64ed518f49c2b31f5b50f8570a1e37bde3b62f2460042c50f132430b2d869c4a6586f13aa33a58a4722715b8158c68cae2827389d6752ac54da2893c83e480fc
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.0
  resolution: "abbrev@npm:3.0.0"
  checksum: 2500075b5ef85e97c095ab6ab2ea640dcf90bb388f46398f4d347b296f53399f984ec9462c74bee81df6bba56ef5fd9dbc2fb29076b1feb0023e0f52d43eb984
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.0.0, acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.0":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: ^8.11.0
  checksum: 4ff03f42323e7cf90f1683e08606b0f460e1e6ac263d2730e3df91c7665b6f64e696db6ea27ee4bed18c2599569be61f28a8399fa170c611161a348c402ca19c
  languageName: node
  linkType: hard

"acorn@npm:^8.0.0, acorn@npm:^8.0.4, acorn@npm:^8.11.0, acorn@npm:^8.14.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 8755074ba55fff94e84e81c72f1013c2d9c78e973c31231c8ae505a5f966859baf654bddd75046bffd73ce816b149298977fff5077a3033dedba0ae2aad152d4
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"algoliasearch@npm:^5.14.2":
  version: 5.20.1
  resolution: "algoliasearch@npm:5.20.1"
  dependencies:
    "@algolia/client-abtesting": 5.20.1
    "@algolia/client-analytics": 5.20.1
    "@algolia/client-common": 5.20.1
    "@algolia/client-insights": 5.20.1
    "@algolia/client-personalization": 5.20.1
    "@algolia/client-query-suggestions": 5.20.1
    "@algolia/client-search": 5.20.1
    "@algolia/ingestion": 1.20.1
    "@algolia/monitoring": 1.20.1
    "@algolia/recommend": 5.20.1
    "@algolia/requester-browser-xhr": 5.20.1
    "@algolia/requester-fetch": 5.20.1
    "@algolia/requester-node-http": 5.20.1
  checksum: 4448570276f4bcf806dec6a66cce4ae158b31f8c2f46e91f19fe393f184a6e43b1c58a4d121fb15d783e8fc4049a378a6edff771b66751cc4fbc736943e54f83
  languageName: node
  linkType: hard

"ansi-escapes@npm:^5.0.0":
  version: 5.0.0
  resolution: "ansi-escapes@npm:5.0.0"
  dependencies:
    type-fest: ^1.0.2
  checksum: d4b5eb8207df38367945f5dd2ef41e08c28edc192dc766ef18af6b53736682f49d8bfcfa4e4d6ecbc2e2f97c258fda084fb29a9e43b69170b71090f771afccac
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.0.0, ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"aria-query@npm:^5.3.2":
  version: 5.3.2
  resolution: "aria-query@npm:5.3.2"
  checksum: d971175c85c10df0f6d14adfe6f1292409196114ab3c62f238e208b53103686f46cc70695a4f775b73bc65f6a09b6a092fd963c4f3a5a7d690c8fc5094925717
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    is-array-buffer: ^3.0.5
  checksum: 0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8":
  version: 3.1.8
  resolution: "array-includes@npm:3.1.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.4
    is-string: ^1.0.7
  checksum: eb39ba5530f64e4d8acab39297c11c1c5be2a4ea188ab2b34aba5fb7224d918f77717a9d57a3e2900caaa8440e59431bdaf5c974d5212ef65d97f132e38e2d91
  languageName: node
  linkType: hard

"array-timsort@npm:^1.0.3":
  version: 1.0.3
  resolution: "array-timsort@npm:1.0.3"
  checksum: fd4b5b0911214bdc8b5699ed10d309685551b518b3819c611c967cff59b87aee01cf591a10e36a3f14dbff696984bd6682b845f6fdbf1217195e910f241a4f78
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-shim-unscopables: ^1.0.2
  checksum: 83ce4ad95bae07f136d316f5a7c3a5b911ac3296c3476abe60225bc4a17938bf37541972fcc37dd5adbc99cbb9c928c70bbbfc1c1ce549d41a415144030bb446
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlastindex@npm:1.2.5"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-shim-unscopables: ^1.0.2
  checksum: 2c81cff2a75deb95bf1ed89b6f5f2bfbfb882211e3b7cc59c3d6b87df774cd9d6b36949a8ae39ac476e092c1d4a4905f5ee11a86a456abb10f35f8211ae4e710
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1, array.prototype.flat@npm:^1.3.2":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-shim-unscopables: ^1.0.2
  checksum: 5d5a7829ab2bb271a8d30a1c91e6271cef0ec534593c0fe6d2fb9ebf8bb62c1e5326e2fddcbbcbbe5872ca04f5e6b54a1ecf092e0af704fb538da9b2bfd95b40
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2, array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-shim-unscopables: ^1.0.2
  checksum: 11b4de09b1cf008be6031bb507d997ad6f1892e57dc9153583de6ebca0f74ea403fffe0f203461d359de05048d609f3f480d9b46fed4099652d8b62cc972f284
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
    es-errors: ^1.3.0
    es-shim-unscopables: ^1.0.2
  checksum: e4142d6f556bcbb4f393c02e7dbaea9af8f620c040450c2be137c9cbbd1a17f216b9c688c5f2c08fbb038ab83f55993fa6efdd9a05881d84693c7bcb5422127a
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    is-array-buffer: ^3.0.4
  checksum: b1d1fd20be4e972a3779b1569226f6740170dca10f07aa4421d42cefeec61391e79c557cda8e771f5baefe47d878178cd4438f60916ce831813c08132bced765
  languageName: node
  linkType: hard

"ast-types-flow@npm:^0.0.8":
  version: 0.0.8
  resolution: "ast-types-flow@npm:0.0.8"
  checksum: 0a64706609a179233aac23817837abab614f3548c252a2d3d79ea1e10c74aa28a0846e11f466cf72771b6ed8713abc094dcf8c40c3ec4207da163efa525a94a8
  languageName: node
  linkType: hard

"astring@npm:^1.8.0":
  version: 1.9.0
  resolution: "astring@npm:1.9.0"
  bin:
    astring: bin/astring
  checksum: 69ffde3643f5280c6846231a995af878a94d3eab41d1a19a86b8c15f456453f63a7982cf5dd72d270b9f50dd26763a3e1e48377c961b7df16f550132b6dba805
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 9102e246d1ed9b37ac36f57f0a6ca55226876553251a31fc80677e71471f463a54c872dc78d5d7f80740c8ba624395cccbe8b60f7b690c4418f487d8e9fd1106
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"axe-core@npm:^4.10.0":
  version: 4.10.2
  resolution: "axe-core@npm:4.10.2"
  checksum: 2b9b1c93ea73ea9f206604e4e17bd771d2d835f077bde54517d73028b8865c69b209460e73d5b109968cbdb39ab3d28943efa5695189bd79e16421ce1706719e
  languageName: node
  linkType: hard

"axobject-query@npm:^4.1.0":
  version: 4.1.0
  resolution: "axobject-query@npm:4.1.0"
  checksum: 7d1e87bf0aa7ae7a76cd39ab627b7c48fda3dc40181303d9adce4ba1d5b5ce73b5e5403ee6626ec8e91090448c887294d6144e24b6741a976f5be9347e3ae1df
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.12
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.12"
  dependencies:
    "@babel/compat-data": ^7.22.6
    "@babel/helper-define-polyfill-provider": ^0.6.3
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 6e6e6a8b85fec80a310ded2f5c151385e4ac59118909dd6a952e1025e4a478eb79dda45a5a6322cc2e598fd696eb07d4e2fa52418b4101f3dc370bdf8c8939ba
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.11.0":
  version: 0.11.1
  resolution: "babel-plugin-polyfill-corejs3@npm:0.11.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.6.3
    core-js-compat: ^3.40.0
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: ee39440475ef377a1570ccbc06b1a1d274cbfbbe2e7c3d4c60f38781a47f00a28bd10d8e23430828b965820c41beb2c93c84596baf72583a2c9c3fdfa4397994
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.3
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.3"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.6.3
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: d12696e6b3f280eb78fac551619ca4389262db62c7352cd54bf679d830df8b35596eef2de77cf00db6648eada1c99d49c4f40636dbc9c335a1e5420cfef96750
  languageName: node
  linkType: hard

"bail@npm:^2.0.0":
  version: 2.0.2
  resolution: "bail@npm:2.0.2"
  checksum: aab4e8ccdc8d762bf3fdfce8e706601695620c0c2eda256dd85088dc0be3cfd7ff126f6e99c2bee1f24f5d418414aacf09d7f9702f16d6963df2fa488cda8824
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"bcp-47-match@npm:^2.0.0":
  version: 2.0.3
  resolution: "bcp-47-match@npm:2.0.3"
  checksum: b36d34a035f1329aeef1db4ebbb0d8d7d6db1e7db920711cdb8ec4948a276b5e75ff84bc007cb0ba71d40c8470d463e5f36486ba48427e72c15c5189ce9c6577
  languageName: node
  linkType: hard

"bcp-47-normalize@npm:^2.0.0":
  version: 2.3.0
  resolution: "bcp-47-normalize@npm:2.3.0"
  dependencies:
    bcp-47: ^2.0.0
    bcp-47-match: ^2.0.0
  checksum: 0537216ca50b2f96e1146fbebf6229cd6582ca06b6622d985c4ef940022c5a70ee7c72ee898f3746edf2691bfd8d892e3d21c2f062fede5dae678ce0ab7ca448
  languageName: node
  linkType: hard

"bcp-47@npm:^2.0.0":
  version: 2.1.0
  resolution: "bcp-47@npm:2.1.0"
  dependencies:
    is-alphabetical: ^2.0.0
    is-alphanumerical: ^2.0.0
    is-decimal: ^2.0.0
  checksum: 2ae12b551f0ef4da3684617d12941430091efa1114a89028f6ee05eba3df06e314cca2988cde43e7d66dc9d5799eac1201556b0c3a5df99efe514928144bab1e
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"body-scroll-lock@npm:^4.0.0-beta.0":
  version: 4.0.0-beta.0
  resolution: "body-scroll-lock@npm:4.0.0-beta.0"
  checksum: 61d40007fddf64ecc69e9e02ed9d96bb895f88d7da65cea7651081110225de48efa44ffc4acd376ed004788e242a9af12059fec728c096774b49365524ea6f46
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.24.3":
  version: 4.24.4
  resolution: "browserslist@npm:4.24.4"
  dependencies:
    caniuse-lite: ^1.0.30001688
    electron-to-chromium: ^1.5.73
    node-releases: ^2.0.19
    update-browserslist-db: ^1.1.1
  bin:
    browserslist: cli.js
  checksum: 64074bf6cf0a9ae3094d753270e3eae9cf925149db45d646f0bc67bacc2e46d7ded64a4e835b95f5fdcf0350f63a83c3755b32f80831f643a47f0886deb8a065
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer@npm:^5.7.1":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: e2cf8429e1c4c7b8cbd30834ac09bd61da46ce35f5c22a78e6c2f04497d6d25541b16881e30a019c6fd3154150650ccee27a308eff3e26229d788bbdeb08ab84
  languageName: node
  linkType: hard

"busboy@npm:1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: ^1.1.0
  checksum: 32801e2c0164e12106bf236291a00795c3c4e4b709ae02132883fe8478ba2ae23743b11c5735a0aae8afe65ac4b6ca4568b91f0d9fed1fdbc32ede824a73746e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.1
  resolution: "call-bind-apply-helpers@npm:1.0.1"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: 3c55343261bb387c58a4762d15ad9d42053659a62681ec5eb50690c6b52a4a666302a01d557133ce6533e8bd04530ee3b209f23dd06c9577a1925556f8fcccdf
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-define-property: ^1.0.0
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.2
  checksum: aa2899bce917a5392fd73bd32e71799c37c0b7ab454e0ed13af7f6727549091182aade8bbb7b55f304a5bc436d543241c14090fb8a3137e9875e23f444f4f5a9
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3":
  version: 1.0.3
  resolution: "call-bound@npm:1.0.3"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    get-intrinsic: ^1.2.6
  checksum: a93bbe0f2d0a2d6c144a4349ccd0593d5d0d5d9309b69101710644af8964286420062f2cc3114dca120b9bc8cc07507952d4b1b3ea7672e0d7f6f1675efedb32
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: ^3.1.2
    tslib: ^2.0.3
  checksum: bcbd25cd253b3cbc69be3f535750137dbf2beb70f093bdc575f73f800acc8443d34fd52ab8f0a2413c34f1e8203139ffc88428d8863e4dfe530cfb257a379ad6
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001579, caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001699
  resolution: "caniuse-lite@npm:1.0.30001699"
  checksum: 697172065537b0f33c428fe8561f4cba6796428dc8e3e56f78eee28404edfcbea70d48bb109ab6c6536de6da90e331058a2cb8ef3a15c58b2226c96ee558bc07
  languageName: node
  linkType: hard

"ccount@npm:^2.0.0":
  version: 2.0.1
  resolution: "ccount@npm:2.0.1"
  checksum: 48193dada54c9e260e0acf57fc16171a225305548f9ad20d5471e0f7a8c026aedd8747091dccb0d900cde7df4e4ddbd235df0d8de4a64c71b12f0d3303eeafd4
  languageName: node
  linkType: hard

"chalk@npm:5.3.0":
  version: 5.3.0
  resolution: "chalk@npm:5.3.0"
  checksum: 623922e077b7d1e9dedaea6f8b9e9352921f8ae3afe739132e0e00c275971bdd331268183b2628cf4ab1727c45ea1f28d7e24ac23ce1db1eb653c414ca8a5a80
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"character-entities-html4@npm:^2.0.0":
  version: 2.1.0
  resolution: "character-entities-html4@npm:2.1.0"
  checksum: 7034aa7c7fa90309667f6dd50499c8a760c3d3a6fb159adb4e0bada0107d194551cdbad0714302f62d06ce4ed68565c8c2e15fdef2e8f8764eb63fa92b34b11d
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^3.0.0":
  version: 3.0.0
  resolution: "character-entities-legacy@npm:3.0.0"
  checksum: 7582af055cb488b626d364b7d7a4e46b06abd526fb63c0e4eb35bcb9c9799cc4f76b39f34fdccef2d1174ac95e53e9ab355aae83227c1a2505877893fce77731
  languageName: node
  linkType: hard

"character-entities@npm:^2.0.0":
  version: 2.0.2
  resolution: "character-entities@npm:2.0.2"
  checksum: cf1643814023697f725e47328fcec17923b8f1799102a8a79c1514e894815651794a2bffd84bb1b3a4b124b050154e4529ed6e81f7c8068a734aecf07a6d3def
  languageName: node
  linkType: hard

"character-reference-invalid@npm:^2.0.0":
  version: 2.0.1
  resolution: "character-reference-invalid@npm:2.0.1"
  checksum: 98d3b1a52ae510b7329e6ee7f6210df14f1e318c5415975d4c9e7ee0ef4c07875d47c6e74230c64551f12f556b4a8ccc24d9f3691a2aa197019e72a95e9297ee
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.3":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d2f29f499705dcd4f6f3bbed79a9ce2388cf530460122eed3b9c48efeab7a4e28739c6551fd15bec9245c6b9eeca7a32baa64694d64d9b6faeb74ddb8c4a413d
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"citeproc@npm:^2.4.6, citeproc@npm:^2.4.63":
  version: 2.4.63
  resolution: "citeproc@npm:2.4.63"
  checksum: 15a22d857cabca6e28b79d52cff094949031145b3ff223ec17cb1b7f1be68b9ad63e8fcaa8621685f10c3c09daf72be58f9e9d20ef322e70e2b60dfbade27dc7
  languageName: node
  linkType: hard

"clean-css@npm:^5.0.0":
  version: 5.3.3
  resolution: "clean-css@npm:5.3.3"
  dependencies:
    source-map: ~0.6.0
  checksum: 941987c14860dd7d346d5cf121a82fd2caf8344160b1565c5387f7ccca4bbcaf885bace961be37c4f4713ce2d8c488dd89483c1add47bb779790edbfdcc79cbc
  languageName: node
  linkType: hard

"cli-cursor@npm:^4.0.0":
  version: 4.0.0
  resolution: "cli-cursor@npm:4.0.0"
  dependencies:
    restore-cursor: ^4.0.0
  checksum: ab3f3ea2076e2176a1da29f9d64f72ec3efad51c0960898b56c8a17671365c26e67b735920530eaf7328d61f8bd41c27f46b9cf6e4e10fe2fa44b5e8c0e392cc
  languageName: node
  linkType: hard

"cli-truncate@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-truncate@npm:3.1.0"
  dependencies:
    slice-ansi: ^5.0.0
    string-width: ^5.0.0
  checksum: c3243e41974445691c63f8b405df1d5a24049dc33d324fe448dc572e561a7b772ae982692900b1a5960901cc4fc7def25a629b9c69a4208ee89d12ab3332617a
  languageName: node
  linkType: hard

"client-only@npm:0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 0c16bf660dadb90610553c1d8946a7fdfb81d624adea073b8440b7d795d5b5b08beb3c950c6a2cf16279365a3265158a236876d92bce16423c485c322d7dfaf8
  languageName: node
  linkType: hard

"clipanion@npm:^3.2.1":
  version: 3.2.1
  resolution: "clipanion@npm:3.2.1"
  dependencies:
    typanion: ^3.8.0
  peerDependencies:
    typanion: "*"
  checksum: 448efd122ead3c802e61ba7a2002e2080c8cce01ce8a0a789d9b9e4f8fe70fd887dcf163ef8c778f5364a9e6f4b498b9f1853f709d7ed4291713e78bcfb88ee8
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.0
    wrap-ansi: ^7.0.0
  checksum: ce2e8f578a4813806788ac399b9e866297740eecd4ad1823c27fd344d78b22c5f8597d548adbcc46f0573e43e21e751f39446c5a5e804a12aace402b7a315d7f
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: acd3e1ab9d8a433ecb3cc2f6a05ab95fe50b4a3cfc5ba47abb6cbf3754585fcb87b84e90c822a1f256c4198e3b41c7f6c391577ffc8678ad587fc0976b24fd57
  languageName: node
  linkType: hard

"collapse-white-space@npm:^2.0.0":
  version: 2.1.0
  resolution: "collapse-white-space@npm:2.1.0"
  checksum: c8978b1f4e7d68bf846cfdba6c6689ce8910511df7d331eb6e6757e51ceffb52768d59a28db26186c91dcf9594955b59be9f8ccd473c485790f5d8b90dc6726f
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: c13fe7cff7885f603f49105827d621ce87f4571d78ba28ef4a3f1a104304748f620615e6bf065ecd2145d0d9dad83a3553f52bb25ede7239d18e9f81622f1cc5
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: ^2.0.1
    color-string: ^1.9.0
  checksum: 0579629c02c631b426780038da929cca8e8d80a40158b09811a0112a107c62e10e4aad719843b791b1e658ab4e800558f2e87ca4522c8b32349d497ecb6adeb4
  languageName: node
  linkType: hard

"colorette@npm:^2.0.20":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 0c016fea2b91b733eb9f4bcdb580018f52c0bc0979443dad930e5037a968237ac53d9beb98e218d2e9235834f8eebce7f8e080422d6194e957454255bde71d3d
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^2.0.0":
  version: 2.0.3
  resolution: "comma-separated-tokens@npm:2.0.3"
  checksum: e3bf9e0332a5c45f49b90e79bcdb4a7a85f28d6a6f0876a94f1bb9b2bfbdbbb9292aac50e1e742d8c0db1e62a0229a106f57917e2d067fca951d81737651700d
  languageName: node
  linkType: hard

"commander@npm:11.0.0":
  version: 11.0.0
  resolution: "commander@npm:11.0.0"
  checksum: 6621954e1e1d078b4991c1f5bbd9439ad37aa7768d6ab4842de1dbd4d222c8a27e1b8e62108b3a92988614af45031d5bb2a2aaa92951f4d0c934d1a1ac564bb4
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 0f82321821fc27b83bd409510bb9deeebcfa799ff0bf5d102128b500b7af22872c0c92cb6a0ebc5a4cf19c6b550fba9cedfa7329d18c6442a625f851377bacf0
  languageName: node
  linkType: hard

"comment-json@npm:^4.2.3":
  version: 4.2.5
  resolution: "comment-json@npm:4.2.5"
  dependencies:
    array-timsort: ^1.0.3
    core-util-is: ^1.0.3
    esprima: ^4.0.1
    has-own-prop: ^2.0.0
    repeat-string: ^1.6.1
  checksum: 6dfc7b83d079595f3fd397af1fe2c48537c0a6fe537f718c4a9e5fbdbb78bb8c2bf0aebc0cc64d14c4807eb030f3e5357235a5adcc366457ab4059256d57f59c
  languageName: node
  linkType: hard

"component-emitter@npm:^1.2.0":
  version: 1.3.1
  resolution: "component-emitter@npm:1.3.1"
  checksum: 94550aa462c7bd5a61c1bc480e28554aa306066930152d1b1844a0dd3845d4e5db7e261ddec62ae184913b3e59b55a2ad84093b9d3596a8f17c341514d6c483d
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"contentlayer2@npm:0.5.5":
  version: 0.5.5
  resolution: "contentlayer2@npm:0.5.5"
  dependencies:
    "@contentlayer2/cli": 0.5.5
    "@contentlayer2/client": 0.5.5
    "@contentlayer2/core": 0.5.5
    "@contentlayer2/source-files": 0.5.5
    "@contentlayer2/source-remote-files": 0.5.5
    "@contentlayer2/utils": 0.5.5
  bin:
    contentlayer2: ./bin/cli.cjs
  checksum: cac861fb9fdd1668ab61a56a0a21cb373d8dbe61f305e0d83eaed199521a2924e94bd488095500d631696205291ce09cfed3938c91c7f1bf6365f3a10c95fc4b
  languageName: node
  linkType: hard

"contentlayer2@npm:^0.5.3":
  version: 0.5.4
  resolution: "contentlayer2@npm:0.5.4"
  dependencies:
    "@contentlayer2/cli": 0.5.4
    "@contentlayer2/client": 0.5.4
    "@contentlayer2/core": 0.5.4
    "@contentlayer2/source-files": 0.5.4
    "@contentlayer2/source-remote-files": 0.5.4
    "@contentlayer2/utils": 0.5.4
  bin:
    contentlayer2: ./bin/cli.cjs
  checksum: 93c13edeed2b849896ef49cfa4355811c385f4dbfcfecc4adedd89bf2885c90fc9b1995babf43e9f55727e51559fc13d18d60249ed81cbf2d36b6596a6cc449f
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 63ae9933be5a2b8d4509daca5124e20c14d023c820258e484e32dc324d34c2754e71297c94a05784064ad27615037ef677e3f0c00469fb55f409d2bb21261035
  languageName: node
  linkType: hard

"cookiejar@npm:^2.1.0":
  version: 2.1.4
  resolution: "cookiejar@npm:2.1.4"
  checksum: c4442111963077dc0e5672359956d6556a195d31cbb35b528356ce5f184922b99ac48245ac05ed86cf993f7df157c56da10ab3efdadfed79778a0d9b1b092d5b
  languageName: node
  linkType: hard

"copyfiles@npm:^2.4.1":
  version: 2.4.1
  resolution: "copyfiles@npm:2.4.1"
  dependencies:
    glob: ^7.0.5
    minimatch: ^3.0.3
    mkdirp: ^1.0.4
    noms: 0.0.0
    through2: ^2.0.1
    untildify: ^4.0.0
    yargs: ^16.1.0
  bin:
    copyfiles: copyfiles
    copyup: copyfiles
  checksum: aea69873bb99cc5f553967660cbfb70e4eeda198f572a36fb0f748b36877ff2c90fd906c58b1d540adbad8afa8ee82820172f1c18e69736f7ab52792c12745a7
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.40.0":
  version: 3.40.0
  resolution: "core-js-compat@npm:3.40.0"
  dependencies:
    browserslist: ^4.24.3
  checksum: 7ad00607c481ab2ded13d72be9ca5db5bbf42e221a175e905fb425e1ef520864aea28736c7283f57e9552d570eb6204bed87fbc8b9eab0fcfd9a7830dacccd43
  languageName: node
  linkType: hard

"core-util-is@npm:^1.0.3, core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.1.3":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
    path-type: ^4.0.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dc339ebea427898c9e03bf01b56ba7afbac07fc7d2a2d5a15d6e9c14de98275a9565da949375aee1809591c152c0a3877bb86dbeaf74d5bd5aaa79955ad9e7a0
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: ^7.0.1
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: 26f2f3ea2ab32617f57effb70d329c2070d2f5630adc800985d8b30b56e8bf7f5f439dd3a0358b79cee6f930afc23cf8e23515f17ccfb30092c6b62c6b630a79
  languageName: node
  linkType: hard

"cross-fetch@npm:^4.0.0":
  version: 4.1.0
  resolution: "cross-fetch@npm:4.1.0"
  dependencies:
    node-fetch: ^2.7.0
  checksum: c02fa85d59f83e50dbd769ee472c9cc984060c403ee5ec8654659f61a525c1a655eef1c7a35e365c1a107b4e72d76e786718b673d1cb3c97f61d4644cb0a9f9d
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^6.1.0
    domhandler: ^5.0.2
    domutils: ^3.0.1
    nth-check: ^2.0.1
  checksum: 2772c049b188d3b8a8159907192e926e11824aea525b8282981f72ba3f349cf9ecd523fdf7734875ee2cb772246c22117fc062da105b6d59afe8dcd5c99c9bda
  languageName: node
  linkType: hard

"css-selector-parser@npm:^3.0.0":
  version: 3.0.5
  resolution: "css-selector-parser@npm:3.0.5"
  checksum: aefcc9841dcf02adee18f6225e03cd44a12aa6357694a19732d03b5d31a2d276b5c6a66a3c32b090438e8a7bfacdb6d0b2ae45b8b5d66eaf08efa941f768bd33
  languageName: node
  linkType: hard

"css-tree@npm:^2.3.1":
  version: 2.3.1
  resolution: "css-tree@npm:2.3.1"
  dependencies:
    mdn-data: 2.0.30
    source-map-js: ^1.0.1
  checksum: 493cc24b5c22b05ee5314b8a0d72d8a5869491c1458017ae5ed75aeb6c3596637dbe1b11dac2548974624adec9f7a1f3a6cf40593dc1f9185eb0e8279543fbc0
  languageName: node
  linkType: hard

"css-tree@npm:~2.2.0":
  version: 2.2.1
  resolution: "css-tree@npm:2.2.1"
  dependencies:
    mdn-data: 2.0.28
    source-map-js: ^1.0.1
  checksum: b94aa8cc2f09e6f66c91548411fcf74badcbad3e150345074715012d16333ce573596ff5dfca03c2a87edf1924716db765120f94247e919d72753628ba3aba27
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: b975e547e1e90b79625918f84e67db5d33d896e6de846c9b584094e529f0c63e2ab85ee33b9daffd05bff3a146a1916bec664e18bb76dd5f66cbff9fc13b2bbe
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"csso@npm:^5.0.5":
  version: 5.0.5
  resolution: "csso@npm:5.0.5"
  dependencies:
    css-tree: ~2.2.0
  checksum: 0ad858d36bf5012ed243e9ec69962a867509061986d2ee07cc040a4b26e4d062c00d4c07e5ba8d430706ceb02dd87edd30a52b5937fd45b1b6f2119c4993d59a
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"damerau-levenshtein@npm:^1.0.8":
  version: 1.0.8
  resolution: "damerau-levenshtein@npm:1.0.8"
  checksum: d240b7757544460ae0586a341a53110ab0a61126570ef2d8c731e3eab3f0cb6e488e2609e6a69b46727635de49be20b071688698744417ff1b6c1d7ccd03e0de
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 1e1cd509c3037ac0f8ba320da3d1f8bf1a9f09b0be09394b5e40781b8cc15ff9834967ba7c9f843a425b34f9fe14ce44cf055af6662c44263424c1eb8d65659b
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 3600c91ced1cfa935f19ef2abae11029e01738de8d229354d3b2a172bf0d7e4ed08ff8f53294b715569fdf72dfeaa96aa7652f479c0f60570878d88e7e8bddf6
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: 8dd492cd51d19970876626b5b5169fbb67ca31ec1d1d3238ee6a71820ca8b80cafb141c485999db1ee1ef02f2cc3b99424c5eda8d59e852d9ebb79ab290eb5ee
  languageName: node
  linkType: hard

"debounce@npm:^1.2.1":
  version: 1.2.1
  resolution: "debounce@npm:1.2.1"
  checksum: 682a89506d9e54fb109526f4da255c5546102fbb8e3ae75eef3b04effaf5d4853756aee97475cd4650641869794e44f410eeb20ace2b18ea592287ab2038519e
  languageName: node
  linkType: hard

"debug@npm:2":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.0, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.3.7":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: fb42df878dd0e22816fc56e1fdca9da73caa85212fbe40c868b1295a6878f9101ae684f4eeef516c13acfc700f5ea07f1136954f43d4cd2d477a811144136479
  languageName: node
  linkType: hard

"debug@npm:4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 3dbad3f94ea64f34431a9cbf0bafb61853eda57bff2880036153438f50fb5a84f27683ba0d8e5426bf41a8c6ff03879488120cf5b3a761e77953169c0600a708
  languageName: node
  linkType: hard

"debug@npm:^3.1.0, debug@npm:^3.2.6, debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"decode-named-character-reference@npm:^1.0.0":
  version: 1.0.2
  resolution: "decode-named-character-reference@npm:1.0.2"
  dependencies:
    character-entities: ^2.0.0
  checksum: f4c71d3b93105f20076052f9cb1523a22a9c796b8296cd35eef1ca54239c78d182c136a848b83ff8da2071e3ae2b1d300bf29d00650a6d6e675438cc31b11d78
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 2024c6a980a1b7128084170c4cf56b0fd58a63f2da1660dcfe977415f27b17dbe5888668b59d0b063753f3220719d5e400b7f113609489c90160bb9a5518d052
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"dequal@npm:^2.0.0":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 8679b850e1a3d0ebbc46ee780d5df7b478c23f335887464023a631d1b9af051ad4a6595a44220f9ff8ff95a8ddccf019b5ad778a976fd7bbf77383d36f412f90
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: daaaed925ffa7889bd91d56e9624e6c8033911bb60f3a50a74a87500680652969dbaab9526d1e200a4c94acf80fc862a22131841145a0a8482d60a99c24f4a3e
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.3":
  version: 2.0.3
  resolution: "detect-libc@npm:2.0.3"
  checksum: 2ba6a939ae55f189aea996ac67afceb650413c7a34726ee92c40fb0deb2400d57ef94631a8a3f052055eea7efb0f99a9b5e6ce923415daa3e68221f963cfc27d
  languageName: node
  linkType: hard

"devlop@npm:^1.0.0, devlop@npm:^1.1.0":
  version: 1.1.0
  resolution: "devlop@npm:1.1.0"
  dependencies:
    dequal: ^2.0.0
  checksum: d2ff650bac0bb6ef08c48f3ba98640bb5fec5cce81e9957eb620408d1bab1204d382a45b785c6b3314dc867bb0684936b84c6867820da6db97cbb5d3c15dd185
  languageName: node
  linkType: hard

"direction@npm:^2.0.0":
  version: 2.0.1
  resolution: "direction@npm:2.0.1"
  bin:
    direction: cli.js
  checksum: 30d2d93ff284b8b55adeeba28204f8f9d357361510d08fea621c64387be908e3c16b52b7383f36122d3c2be8c8a634e98dc36037e7e0cce3e07d296896c629e3
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: ^2.3.0
    domhandler: ^5.0.2
    entities: ^4.2.0
  checksum: cd1810544fd8cdfbd51fa2c0c1128ec3a13ba92f14e61b7650b5de421b88205fd2e3f0cc6ace82f13334114addb90ed1c2f23074a51770a8e9c1273acbc7f3e6
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: ^2.3.0
  checksum: 0f58f4a6af63e6f3a4320aa446d28b5790a009018707bce2859dcb1d21144c7876482b5188395a188dfa974238c019e0a1e610d2fc269a12b2c192ea2b0b131c
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: ^2.0.0
    domelementtype: ^2.3.0
    domhandler: ^5.0.3
  checksum: ae941d56f03d857077d55dde9297e960a625229fc2b933187cc4123084d7c2d2517f58283a7336567127029f1e008449bac8ac8506d44341e29e3bb18e02f906
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dotenv@npm:^8.2.0":
  version: 8.6.0
  resolution: "dotenv@npm:8.6.0"
  checksum: 38e902c80b0666ab59e9310a3d24ed237029a7ce34d976796349765ac96b8d769f6df19090f1f471b77a25ca391971efde8a1ea63bb83111bd8bec8e5cc9b2cd
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 149207e36f07bd4941921b0ca929e3a28f1da7bd6b6ff8ff7f4e2f2e460675af4576eeba359c635723dc189b64cdd4787e0255897d5b135ccc5d15cb8685fc90
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.2":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: 62ba61a830c56801db28ff6305c7d289b6dc9f859054e8c982abd8ee0b0a14d2e9a8e7d086ffee12e868d43e2bbe8a964be55ddbd8c8957714c87373c7a4f9b0
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.96
  resolution: "electron-to-chromium@npm:1.5.96"
  checksum: 18a1f4e7a55754e3423614f788077552803cee5f2026068256494192bb7655e11e5a622d2eec7483f391dc0edd4bac5878e234b9c311522198a8f732844a49d9
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.15.0, enhanced-resolve@npm:^5.18.0":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: de5bea7debe3576e78173bcc409c4aee7fcb56580c602d5c47c533b92952e55d7da3d9f53b864846ba62c8bd3efb0f9ecfe5f865e57de2f3e9b6e5cda03b4e7e
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.4.0, entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 853f8ebd5b425d350bffa97dd6958143179a5938352ccae092c62d1267c4e392a039be1bae7d51b6e4ffad25f51f9617531fedf5237f15df302ccfb452cbf2d7
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9":
  version: 1.23.9
  resolution: "es-abstract@npm:1.23.9"
  dependencies:
    array-buffer-byte-length: ^1.0.2
    arraybuffer.prototype.slice: ^1.0.4
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    data-view-buffer: ^1.0.2
    data-view-byte-length: ^1.0.2
    data-view-byte-offset: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-set-tostringtag: ^2.1.0
    es-to-primitive: ^1.3.0
    function.prototype.name: ^1.1.8
    get-intrinsic: ^1.2.7
    get-proto: ^1.0.0
    get-symbol-description: ^1.1.0
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    internal-slot: ^1.1.0
    is-array-buffer: ^3.0.5
    is-callable: ^1.2.7
    is-data-view: ^1.0.2
    is-regex: ^1.2.1
    is-shared-array-buffer: ^1.0.4
    is-string: ^1.1.1
    is-typed-array: ^1.1.15
    is-weakref: ^1.1.0
    math-intrinsics: ^1.1.0
    object-inspect: ^1.13.3
    object-keys: ^1.1.1
    object.assign: ^4.1.7
    own-keys: ^1.0.1
    regexp.prototype.flags: ^1.5.3
    safe-array-concat: ^1.1.3
    safe-push-apply: ^1.0.0
    safe-regex-test: ^1.1.0
    set-proto: ^1.0.0
    string.prototype.trim: ^1.2.10
    string.prototype.trimend: ^1.0.9
    string.prototype.trimstart: ^1.0.8
    typed-array-buffer: ^1.0.3
    typed-array-byte-length: ^1.0.3
    typed-array-byte-offset: ^1.0.4
    typed-array-length: ^1.0.7
    unbox-primitive: ^1.1.0
    which-typed-array: ^1.1.18
  checksum: f3ee2614159ca197f97414ab36e3f406ee748ce2f97ffbf09e420726db5a442ce13f1e574601468bff6e6eb81588e6c9ce1ac6c03868a37c7cd48ac679f8485a
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-abstract: ^1.23.6
    es-errors: ^1.3.0
    es-set-tostringtag: ^2.0.3
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.6
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    internal-slot: ^1.1.0
    iterator.prototype: ^1.1.4
    safe-array-concat: ^1.1.3
  checksum: 952808dd1df3643d67ec7adf20c30b36e5eecadfbf36354e6f39ed3266c8e0acf3446ce9bc465e38723d613cb1d915c1c07c140df65bdce85da012a6e7bda62b
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: ^1.3.0
  checksum: 214d3767287b12f36d3d7267ef342bbbe1e89f899cfd67040309fc65032372a8e60201410a99a1645f2f90c1912c8c49c8668066f6bdd954bcd614dda2e3da97
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 789f35de4be3dc8d11fdcb91bc26af4ae3e6d602caa93299a8c45cf05d36cc5081454ae2a6d3afa09cceca214b76c046e4f8151e092e6fc7feeb5efb9e794fc6
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2":
  version: 1.0.2
  resolution: "es-shim-unscopables@npm:1.0.2"
  dependencies:
    hasown: ^2.0.0
  checksum: 432bd527c62065da09ed1d37a3f8e623c423683285e6188108286f4a1e8e164a5bcbfbc0051557c7d14633cd2a41ce24c7048e6bbb66a985413fd32f1be72626
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: ^1.2.7
    is-date-object: ^1.0.5
    is-symbol: ^1.0.4
  checksum: 966965880356486cd4d1fe9a523deda2084c81b3702d951212c098f5f2ee93605d1b7c1840062efb48a07d892641c7ed1bc194db563645c0dd2b919cb6d65b93
  languageName: node
  linkType: hard

"esast-util-from-estree@npm:^2.0.0":
  version: 2.0.0
  resolution: "esast-util-from-estree@npm:2.0.0"
  dependencies:
    "@types/estree-jsx": ^1.0.0
    devlop: ^1.0.0
    estree-util-visit: ^2.0.0
    unist-util-position-from-estree: ^2.0.0
  checksum: b9ea5b6db25decbe7c3be23a00251542641c9538499905d740d76fd5c9fea9f727ad1d0cce4f2071b6d9bb2f405f4f11acbdec9b8ea6485649cf60d886b99f28
  languageName: node
  linkType: hard

"esast-util-from-js@npm:^2.0.0":
  version: 2.0.1
  resolution: "esast-util-from-js@npm:2.0.1"
  dependencies:
    "@types/estree-jsx": ^1.0.0
    acorn: ^8.0.0
    esast-util-from-estree: ^2.0.0
    vfile-message: ^4.0.0
  checksum: a262b94d973d8cc80227e083a7f1367028c4acf524e8f8507177626302bac567f260f75ea52321c8a9650e34c47e70bcc4f7696f710002f64b21aaa630e73e43
  languageName: node
  linkType: hard

"esbuild@npm:0.25.2":
  version: 0.25.2
  resolution: "esbuild@npm:0.25.2"
  dependencies:
    "@esbuild/aix-ppc64": 0.25.2
    "@esbuild/android-arm": 0.25.2
    "@esbuild/android-arm64": 0.25.2
    "@esbuild/android-x64": 0.25.2
    "@esbuild/darwin-arm64": 0.25.2
    "@esbuild/darwin-x64": 0.25.2
    "@esbuild/freebsd-arm64": 0.25.2
    "@esbuild/freebsd-x64": 0.25.2
    "@esbuild/linux-arm": 0.25.2
    "@esbuild/linux-arm64": 0.25.2
    "@esbuild/linux-ia32": 0.25.2
    "@esbuild/linux-loong64": 0.25.2
    "@esbuild/linux-mips64el": 0.25.2
    "@esbuild/linux-ppc64": 0.25.2
    "@esbuild/linux-riscv64": 0.25.2
    "@esbuild/linux-s390x": 0.25.2
    "@esbuild/linux-x64": 0.25.2
    "@esbuild/netbsd-arm64": 0.25.2
    "@esbuild/netbsd-x64": 0.25.2
    "@esbuild/openbsd-arm64": 0.25.2
    "@esbuild/openbsd-x64": 0.25.2
    "@esbuild/sunos-x64": 0.25.2
    "@esbuild/win32-arm64": 0.25.2
    "@esbuild/win32-ia32": 0.25.2
    "@esbuild/win32-x64": 0.25.2
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 2c4e91948b939e711e9342e692fc3c8b0a95acbc1fc9c7628db6092c4aef7c32aa643b2782111625871756084536cebc4831b3f1d5c3b6bd4e4774e21bc4bbea
  languageName: node
  linkType: hard

"esbuild@npm:>=0.17":
  version: 0.25.0
  resolution: "esbuild@npm:0.25.0"
  dependencies:
    "@esbuild/aix-ppc64": 0.25.0
    "@esbuild/android-arm": 0.25.0
    "@esbuild/android-arm64": 0.25.0
    "@esbuild/android-x64": 0.25.0
    "@esbuild/darwin-arm64": 0.25.0
    "@esbuild/darwin-x64": 0.25.0
    "@esbuild/freebsd-arm64": 0.25.0
    "@esbuild/freebsd-x64": 0.25.0
    "@esbuild/linux-arm": 0.25.0
    "@esbuild/linux-arm64": 0.25.0
    "@esbuild/linux-ia32": 0.25.0
    "@esbuild/linux-loong64": 0.25.0
    "@esbuild/linux-mips64el": 0.25.0
    "@esbuild/linux-ppc64": 0.25.0
    "@esbuild/linux-riscv64": 0.25.0
    "@esbuild/linux-s390x": 0.25.0
    "@esbuild/linux-x64": 0.25.0
    "@esbuild/netbsd-arm64": 0.25.0
    "@esbuild/netbsd-x64": 0.25.0
    "@esbuild/openbsd-arm64": 0.25.0
    "@esbuild/openbsd-x64": 0.25.0
    "@esbuild/sunos-x64": 0.25.0
    "@esbuild/win32-arm64": 0.25.0
    "@esbuild/win32-ia32": 0.25.0
    "@esbuild/win32-x64": 0.25.0
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 4d1e0cb7c059a373ea3edb20ca5efcea29efada03e4ea82b2b8ab1f2f062e4791e9744213308775d26e07a0225a7d8250da93da5c8e07ef61bb93d58caab8cf9
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 20daabe197f3cb198ec28546deebcf24b3dbb1a5a269184381b3116d12f0532e06007f4bc8da25669d6a7f8efb68db0758df4cd981f57bc5b57f521a3e12c59e
  languageName: node
  linkType: hard

"eslint-config-next@npm:15.2.4":
  version: 15.2.4
  resolution: "eslint-config-next@npm:15.2.4"
  dependencies:
    "@next/eslint-plugin-next": 15.2.4
    "@rushstack/eslint-patch": ^1.10.3
    "@typescript-eslint/eslint-plugin": ^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0
    "@typescript-eslint/parser": ^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0
    eslint-import-resolver-node: ^0.3.6
    eslint-import-resolver-typescript: ^3.5.2
    eslint-plugin-import: ^2.31.0
    eslint-plugin-jsx-a11y: ^6.10.0
    eslint-plugin-react: ^7.37.0
    eslint-plugin-react-hooks: ^5.0.0
  peerDependencies:
    eslint: ^7.23.0 || ^8.0.0 || ^9.0.0
    typescript: ">=3.3.1"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 8e206d89910dbe05282ae1d80da8db87d1dc6870c90366b342aacf248daabfa6baf2433042614e6fa15f8bf51038c7faa2ef5ee25559526b7586fbd37fbb7d14
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^9.1.0":
  version: 9.1.0
  resolution: "eslint-config-prettier@npm:9.1.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 9229b768c879f500ee54ca05925f31b0c0bafff3d9f5521f98ff05127356de78c81deb9365c86a5ec4efa990cb72b74df8612ae15965b14136044c73e1f6a907
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.6, eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: ^3.2.7
    is-core-module: ^2.13.0
    resolve: ^1.22.4
  checksum: 439b91271236b452d478d0522a44482e8c8540bf9df9bd744062ebb89ab45727a3acd03366a6ba2bdbcde8f9f718bab7fe8db64688aca75acf37e04eafd25e22
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:^3.5.2":
  version: 3.7.0
  resolution: "eslint-import-resolver-typescript@npm:3.7.0"
  dependencies:
    "@nolyfill/is-core-module": 1.0.39
    debug: ^4.3.7
    enhanced-resolve: ^5.15.0
    fast-glob: ^3.3.2
    get-tsconfig: ^4.7.5
    is-bun-module: ^1.0.2
    is-glob: ^4.0.3
    stable-hash: ^0.0.4
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
    eslint-plugin-import-x: "*"
  peerDependenciesMeta:
    eslint-plugin-import:
      optional: true
    eslint-plugin-import-x:
      optional: true
  checksum: e24659fbd91957c9db8de72243a6ffcf891ffd1175bca54d6993a9ddecc352e76d512c7ee22a48ae7d3ec1ae4c492fd2ab649cde636a993f4a42bf4d1ae4d34a
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.0":
  version: 2.12.0
  resolution: "eslint-module-utils@npm:2.12.0"
  dependencies:
    debug: ^3.2.7
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: be3ac52e0971c6f46daeb1a7e760e45c7c45f820c8cc211799f85f10f04ccbf7afc17039165d56cb2da7f7ca9cec2b3a777013cddf0b976784b37eb9efa24180
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.31.0":
  version: 2.31.0
  resolution: "eslint-plugin-import@npm:2.31.0"
  dependencies:
    "@rtsao/scc": ^1.1.0
    array-includes: ^3.1.8
    array.prototype.findlastindex: ^1.2.5
    array.prototype.flat: ^1.3.2
    array.prototype.flatmap: ^1.3.2
    debug: ^3.2.7
    doctrine: ^2.1.0
    eslint-import-resolver-node: ^0.3.9
    eslint-module-utils: ^2.12.0
    hasown: ^2.0.2
    is-core-module: ^2.15.1
    is-glob: ^4.0.3
    minimatch: ^3.1.2
    object.fromentries: ^2.0.8
    object.groupby: ^1.0.3
    object.values: ^1.2.0
    semver: ^6.3.1
    string.prototype.trimend: ^1.0.8
    tsconfig-paths: ^3.15.0
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: b1d2ac268b3582ff1af2a72a2c476eae4d250c100f2e335b6e102036e4a35efa530b80ec578dfc36761fabb34a635b9bf5ab071abe9d4404a4bb054fdf22d415
  languageName: node
  linkType: hard

"eslint-plugin-jsx-a11y@npm:^6.10.0":
  version: 6.10.2
  resolution: "eslint-plugin-jsx-a11y@npm:6.10.2"
  dependencies:
    aria-query: ^5.3.2
    array-includes: ^3.1.8
    array.prototype.flatmap: ^1.3.2
    ast-types-flow: ^0.0.8
    axe-core: ^4.10.0
    axobject-query: ^4.1.0
    damerau-levenshtein: ^1.0.8
    emoji-regex: ^9.2.2
    hasown: ^2.0.2
    jsx-ast-utils: ^3.3.5
    language-tags: ^1.0.9
    minimatch: ^3.1.2
    object.fromentries: ^2.0.8
    safe-regex-test: ^1.0.3
    string.prototype.includes: ^2.0.1
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9
  checksum: 0cc861398fa26ada61ed5703eef5b335495fcb96253263dcd5e399488ff019a2636372021baacc040e3560d1a34bfcd5d5ad9f1754f44cd0509c956f7df94050
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.2.0":
  version: 5.2.3
  resolution: "eslint-plugin-prettier@npm:5.2.3"
  dependencies:
    prettier-linter-helpers: ^1.0.0
    synckit: ^0.9.1
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: "*"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 3f3210ed6a52eb2e7cd10a635857328136149c79240627b8f5dbc6c5271d5020b17ab2e7067acc0a82fec686fa35ed182dd8d67feca41818d6a7810bf6dad2b6
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^5.0.0":
  version: 5.1.0
  resolution: "eslint-plugin-react-hooks@npm:5.1.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 14d2692214ea15b19ef330a9abf51cb8c1586339d9e758ebd61b182be68dd772af56462b04e4b9d2be923d72f46db61e8d32fcf37c248b04949c0b02f5bfb3c0
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.37.0":
  version: 7.37.4
  resolution: "eslint-plugin-react@npm:7.37.4"
  dependencies:
    array-includes: ^3.1.8
    array.prototype.findlast: ^1.2.5
    array.prototype.flatmap: ^1.3.3
    array.prototype.tosorted: ^1.1.4
    doctrine: ^2.1.0
    es-iterator-helpers: ^1.2.1
    estraverse: ^5.3.0
    hasown: ^2.0.2
    jsx-ast-utils: ^2.4.1 || ^3.0.0
    minimatch: ^3.1.2
    object.entries: ^1.1.8
    object.fromentries: ^2.0.8
    object.values: ^1.2.1
    prop-types: ^15.8.1
    resolve: ^2.0.0-next.5
    semver: ^6.3.1
    string.prototype.matchall: ^4.0.12
    string.prototype.repeat: ^1.0.0
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 8a37bdc9b347bf3a1273fef73dfbc39279cc3e58441940a5e13b3ba4e82b34132d1d1172db9d6746f153ee981280bd6bd06a9065fb453388c68f4bebe0d9f839
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.2.0":
  version: 8.2.0
  resolution: "eslint-scope@npm:8.2.0"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: 750eff4672ca2bf274ec0d1bbeae08aadd53c1907d5c6aff5564d8e047a5f49afa8ae6eee333cab637fd3ebcab2141659d8f2f040f6fdc982b0f61f8bf03136f
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 779c604672b570bb4da84cef32f6abb085ac78379779c1122d7879eade8bb38ae715645324597cf23232d03cef06032c9844d25c73625bc282a5bfd30247e5b5
  languageName: node
  linkType: hard

"eslint@npm:^9.14.0":
  version: 9.20.0
  resolution: "eslint@npm:9.20.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.12.1
    "@eslint/config-array": ^0.19.0
    "@eslint/core": ^0.11.0
    "@eslint/eslintrc": ^3.2.0
    "@eslint/js": 9.20.0
    "@eslint/plugin-kit": ^0.2.5
    "@humanfs/node": ^0.16.6
    "@humanwhocodes/module-importer": ^1.0.1
    "@humanwhocodes/retry": ^0.4.1
    "@types/estree": ^1.0.6
    "@types/json-schema": ^7.0.15
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.6
    debug: ^4.3.2
    escape-string-regexp: ^4.0.0
    eslint-scope: ^8.2.0
    eslint-visitor-keys: ^4.2.0
    espree: ^10.3.0
    esquery: ^1.5.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^8.0.0
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    json-stable-stringify-without-jsonify: ^1.0.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 660de11c4dbfaa311b17d0670949adb5f023811e50786a6217bd833a299f8245929577ddd62c18bacf39c82462edf795383cf6d26cc4a018a8d118aef7cadea4
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.3.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: ^8.14.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^4.2.0
  checksum: 63e8030ff5a98cea7f8b3e3a1487c998665e28d674af08b9b3100ed991670eb3cbb0e308c4548c79e03762753838fbe530c783f17309450d6b47a889fee72bef
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"estree-util-attach-comments@npm:^3.0.0":
  version: 3.0.0
  resolution: "estree-util-attach-comments@npm:3.0.0"
  dependencies:
    "@types/estree": ^1.0.0
  checksum: 56254eaef39659e6351919ebc2ae53a37a09290a14571c19e373e9d5fad343a3403d9ad0c23ae465d6e7d08c3e572fd56fb8c793efe6434a261bf1489932dbd5
  languageName: node
  linkType: hard

"estree-util-build-jsx@npm:^3.0.0":
  version: 3.0.1
  resolution: "estree-util-build-jsx@npm:3.0.1"
  dependencies:
    "@types/estree-jsx": ^1.0.0
    devlop: ^1.0.0
    estree-util-is-identifier-name: ^3.0.0
    estree-walker: ^3.0.0
  checksum: 185eff060eda2ba32cecd15904db4f5ba0681159fbdf54f0f6586cd9411e77e733861a833d0aee3415e1d1fd4b17edf08bc9e9872cee98e6ec7b0800e1a85064
  languageName: node
  linkType: hard

"estree-util-is-identifier-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "estree-util-is-identifier-name@npm:3.0.0"
  checksum: ea3909f0188ea164af0aadeca87c087e3e5da78d76da5ae9c7954ff1340ea3e4679c4653bbf4299ffb70caa9b322218cc1128db2541f3d2976eb9704f9857787
  languageName: node
  linkType: hard

"estree-util-scope@npm:^1.0.0":
  version: 1.0.0
  resolution: "estree-util-scope@npm:1.0.0"
  dependencies:
    "@types/estree": ^1.0.0
    devlop: ^1.0.0
  checksum: df2ed1b4c078002d50f7e330980e7b6f2630a1f551102203ee5000b61ed8ce5720fe7b9bc1a238a5fded5cf0f157dbe516ad6807323f037b3bb594bd1a0d61bb
  languageName: node
  linkType: hard

"estree-util-to-js@npm:^2.0.0":
  version: 2.0.0
  resolution: "estree-util-to-js@npm:2.0.0"
  dependencies:
    "@types/estree-jsx": ^1.0.0
    astring: ^1.8.0
    source-map: ^0.7.0
  checksum: 833edc94ab9978e0918f90261e0a3361bf4564fec4901f326d2237a9235d3f5fc6482da3be5acc545e702c8c7cb8bc5de5c7c71ba3b080eb1975bcfdf3923d79
  languageName: node
  linkType: hard

"estree-util-value-to-estree@npm:^3.0.0":
  version: 3.3.2
  resolution: "estree-util-value-to-estree@npm:3.3.2"
  dependencies:
    "@types/estree": ^1.0.0
  checksum: f87199fb0823761d3f03728b18ff3c30a8ad5c1ba67dee392d01a4f9fee568dcf2feb6f8f6972d8fd21d998a9807ce79cfcf92ff44998aac5df001f285f9eb7d
  languageName: node
  linkType: hard

"estree-util-visit@npm:^2.0.0":
  version: 2.0.0
  resolution: "estree-util-visit@npm:2.0.0"
  dependencies:
    "@types/estree-jsx": ^1.0.0
    "@types/unist": ^3.0.0
  checksum: 6444b38f224322945a6d19ea81a8828a0eec64aefb2bf1ea791fe20df496f7b7c543408d637df899e6a8e318b638f66226f16378a33c4c2b192ba5c3f891121f
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.0":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": ^1.0.0
  checksum: a65728d5727b71de172c5df323385755a16c0fdab8234dc756c3854cfee343261ddfbb72a809a5660fac8c75d960bb3e21aa898c2d7e9b19bb298482ca58a3af
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 543d6c858ab699303c3c32e0f0f47fc64d360bf73c3daf0ac0b5079710e340d6fe9f15487f94e66c629f5f82cd1a8678d692f3dbb6f6fcd1190e1b97fcad36f8
  languageName: node
  linkType: hard

"execa@npm:7.2.0":
  version: 7.2.0
  resolution: "execa@npm:7.2.0"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.1
    human-signals: ^4.3.0
    is-stream: ^3.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^5.1.0
    onetime: ^6.0.0
    signal-exit: ^3.0.7
    strip-final-newline: ^3.0.0
  checksum: 14fd17ba0ca8c87b277584d93b1d9fc24f2a65e5152b31d5eb159a3b814854283eaae5f51efa9525e304447e2f757c691877f7adff8fde5746aae67eb1edd1cc
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 7e191e3dd6edd8c56c88f2c8037c98fbb8034fe48778be53ed8cb30ccef371a061a4e999a469aab939b92f8f12698f3b426d52f4f76b7a20da5f9f98c3cbc862
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: ^0.1.0
  checksum: 8fb58d9d7a511f4baf78d383e637bd7d2e80843bd9cd0853649108ea835208fb614da502a553acc30208e1325240bb7cc4a68473021612496bb89725483656d8
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: a50a8309ca65ea5d426382ff09f33586527882cf532931cb08ca786ea3146c0553310bda688710ff61d7668eba9f96b923fe1420cdf56a2c3eaf30fcab87b515
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: d22d371b994fdc8cce9ff510d7b8dc4da70ac327bcba20df607dd5b9cae9f908f4d1028f5fe467650f058d1e7270235ae0b8230809a262b4df587a3b3aa216c3
  languageName: node
  linkType: hard

"fast-equals@npm:^2.0.3":
  version: 2.0.4
  resolution: "fast-equals@npm:2.0.4"
  checksum: 1aac8a2e16b33e5e402bb5cd46be65f1ca331903c2c44e3bd75324e8472ee04f0acdbc6889e45fc28f9707ca3964f2a86789b32305d4d8b85dc97f61e446ef4b
  languageName: node
  linkType: hard

"fast-glob@npm:3.3.1":
  version: 3.3.1
  resolution: "fast-glob@npm:3.3.1"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: b6f3add6403e02cf3a798bfbb1183d0f6da2afd368f27456010c0bc1f9640aea308243d4cb2c0ab142f618276e65ecb8be1661d7c62a7b4e5ba774b9ce5432e5
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.12, fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.8
  checksum: 0704d7b85c0305fd2cef37777337dfa26230fdd072dce9fb5c82a4b03156f3ffb8ed3e636033e65d45d2a5805a4e475825369a27404c0307f2db0c8eb3366fbd
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.0
  resolution: "fastq@npm:1.19.0"
  dependencies:
    reusify: ^1.0.4
  checksum: c9203c9e485f5d1c5243e8807b15054533338242af632817f8d65bed6e46488e5b27cea75dfc110cc4c029137381e4d650449428bc42cc8712180f27a6bace9f
  languageName: node
  linkType: hard

"fault@npm:^2.0.0":
  version: 2.0.1
  resolution: "fault@npm:2.0.1"
  dependencies:
    format: ^0.2.0
  checksum: c9b30f47d95769177130a9409976a899ed31eb598450fbad5b0d39f2f5f56d5f4a9ff9257e0bee8407cb0fc3ce37165657888c6aa6d78472e403893104329b72
  languageName: node
  linkType: hard

"fetch-ponyfill@npm:^7.1.0":
  version: 7.1.0
  resolution: "fetch-ponyfill@npm:7.1.0"
  dependencies:
    node-fetch: ~2.6.1
  checksum: 7fd497dd5f7db890e80193de5bc1cd0115a62400272cd9a992849288e66886fcdb0724ea1ed161be7b8db2daeafda8c58d0259acdda42d6561155dbcdbb0720a
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: ^4.0.0
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.4
  checksum: 899fc86bf6df093547d76e7bfaeb900824b869d7d457d02e9b8aae24836f0a99fbad79328cfd6415ee8908f180699bf259dc7614f793447cb14f707caf5996f6
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.2
  resolution: "flatted@npm:3.3.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.4
  resolution: "for-each@npm:0.3.4"
  dependencies:
    is-callable: ^1.2.7
  checksum: 7c094a28f9edd56ad92db03a7c1197032edad18df5dc8bad0351c725e929b70a6a54b3af3301845aadf2ee407ef7e242fa49d31fce56ad3822e6ff6ee50de356
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 1989698488f725b05b26bc9afc8a08f08ec41807cd7b92ad85d96004ddf8243fd3e79486b8348c64a3011ae5cc2c9f0936af989e1f28339805d8bc178a75b451
  languageName: node
  linkType: hard

"form-data@npm:^2.3.1":
  version: 2.5.2
  resolution: "form-data@npm:2.5.2"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.6
    mime-types: ^2.1.12
    safe-buffer: ^5.2.1
  checksum: 89ed3d96238d6fa874d75435e20f1aad28a1c22a88ab4e726ac4f6b0d29bef33d7e5aca51248c1070eccbbf4df94020a53842e800b2f1fb63073881a268113b4
  languageName: node
  linkType: hard

"format@npm:^0.2.0":
  version: 0.2.2
  resolution: "format@npm:0.2.2"
  checksum: 646a60e1336250d802509cf24fb801e43bd4a70a07510c816fa133aa42cdbc9c21e66e9cc0801bb183c5b031c9d68be62e7fbb6877756e52357850f92aa28799
  languageName: node
  linkType: hard

"formidable@npm:^1.1.1":
  version: 1.2.6
  resolution: "formidable@npm:1.2.6"
  checksum: 2b68ed07ba88302b9c63f8eda94f19a460cef6017bfda48348f09f41d2a36660c9353137991618e0e4c3db115b41e4b8f6fa63bc973b7a7c91dec66acdd02a56
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    functions-have-names: ^1.2.3
    hasown: ^2.0.2
    is-callable: ^1.2.7
  checksum: 3a366535dc08b25f40a322efefa83b2da3cd0f6da41db7775f2339679120ef63b6c7e967266182609e655b8f0a8f65596ed21c7fd72ad8bd5621c2340edd4010
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"fuse.js@npm:^6.6.2":
  version: 6.6.2
  resolution: "fuse.js@npm:6.6.2"
  checksum: 17ae758ce205276ebd88bd9c9f088a100be0b4896abac9f6b09847151269d1690f41d7f98ff5813d4a58973162dbd99d0072ce807020fee6f9de60170f6b08eb
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7":
  version: 1.2.7
  resolution: "get-intrinsic@npm:1.2.7"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    function-bind: ^1.1.2
    get-proto: ^1.0.0
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.1.0
  checksum: a1597b3b432074f805b6a0ba1182130dd6517c0ea0c4eecc4b8834c803913e1ea62dfc412865be795b3dacb1555a21775b70cf9af7a18b1454ff3414e5442d4a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: ^1.0.1
    es-object-atoms: ^1.0.0
  checksum: 4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.1":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
  checksum: 655ed04db48ee65ef2ddbe096540d4405e79ba0a7f54225775fef43a7e2afcb93a77d141c5f05fdef0afce2eb93bcbfb3597142189d562ac167ff183582683cd
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.7.5":
  version: 4.10.0
  resolution: "get-tsconfig@npm:4.10.0"
  dependencies:
    resolve-pkg-maps: ^1.0.0
  checksum: cebf14d38ecaa9a1af25fc3f56317402a4457e7e20f30f52a0ab98b4c85962a259f75065e483824f73a1ce4a8e4926c149ead60f0619842b8cd13b94e15fbdec
  languageName: node
  linkType: hard

"giscus@npm:^1.6.0":
  version: 1.6.0
  resolution: "giscus@npm:1.6.0"
  dependencies:
    lit: ^3.2.1
  checksum: 1f5b405a9189234987982929e1f9d004e1f942d5740a64dd8de3562291ec181de60b5fd276da1eeecc050f5b8a2f73036246a03135b05d1b6470d85d66e3f8fa
  languageName: node
  linkType: hard

"github-slugger@npm:^2.0.0":
  version: 2.0.0
  resolution: "github-slugger@npm:2.0.0"
  checksum: 250375cde2058f21454872c2c79f72c4637340c30c51ff158ca4ec71cbc478f33d54477d787a662f9207aeb095a2060f155bc01f15329ba8a5fb6698e0fc81f8
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"glob@npm:^7.0.5":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 534b8216736a5425737f59f6e6a5c7f386254560c9f41d24a9227d60ee3ad4a9e82c5b85def0e212e9d92162f83a92544be4c7fd4c902cb913736c10e08237ac
  languageName: node
  linkType: hard

"globals@npm:^15.12.0":
  version: 15.14.0
  resolution: "globals@npm:15.14.0"
  checksum: fa993433a01bf4a118904fbafbcff34db487fce83f73da75fb4a8653afc6dcd72905e6208c49bab307ff0980928273d0ecd1cfc67e1a4782dabfbd92c234ab68
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: ^1.2.1
    gopd: ^1.0.1
  checksum: 39ad667ad9f01476474633a1834a70842041f70a55571e8dcef5fb957980a92da5022db5430fca8aecc5d47704ae30618c0bc877a579c70710c904e9ef06108a
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"gray-matter@npm:^4.0.2, gray-matter@npm:^4.0.3":
  version: 4.0.3
  resolution: "gray-matter@npm:4.0.3"
  dependencies:
    js-yaml: ^3.13.1
    kind-of: ^6.0.2
    section-matter: ^1.0.0
    strip-bom-string: ^1.0.0
  checksum: 37717bd424344487d655392251ce8d8878a1275ee087003e61208fba3bfd59cbb73a85b2159abf742ae95e23db04964813fdc33ae18b074208428b2528205222
  languageName: node
  linkType: hard

"gzip-size@npm:^6.0.0":
  version: 6.0.0
  resolution: "gzip-size@npm:6.0.0"
  dependencies:
    duplexer: ^0.1.2
  checksum: 2df97f359696ad154fc171dcb55bc883fe6e833bca7a65e457b9358f3cb6312405ed70a8da24a77c1baac0639906cd52358dc0ce2ec1a937eaa631b934c94194
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 79730518ae02c77e4af6a1d1a0b6a2c3e1509785532771f9baf0241e83e36329542c3d7a0e723df8cbc85f74eff4f177828a2265a01ba576adbdc2d40d86538b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-own-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-own-prop@npm:2.0.0"
  checksum: ca6336e85ead2295c9603880cbc199e2d3ff7eaea0e9035d68fbc79892e9cf681abc62c0909520f112c671dad9961be2173b21dff951358cc98425c560e789e0
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: ^1.0.0
  checksum: f55010cb94caa56308041d77967c72a02ffd71386b23f9afa8447e58bc92d49d15c19bf75173713468e92fe3fb1680b03b115da39c21c32c74886d1d50d3e7ff
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"hash-wasm@npm:^4.11.0":
  version: 4.12.0
  resolution: "hash-wasm@npm:4.12.0"
  checksum: f292c94a19a3d7c075e1e7de2764ba8a006ae5a9551b1a35c61f693035df7977e6cb41f3fc036deb4a724e1949711b7cba06f7ad174f5aeb295edb64649b4a32
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"hast-util-embedded@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-embedded@npm:3.0.0"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-element: ^3.0.0
  checksum: b5a1262f68d0f131bff4e8abaf3379f9318c5c825ad609cad3a4289da19d0cf636eab973a2ae716b49ca131eab2507ce2b4c73ac8d9cb0214eb0721da528cd27
  languageName: node
  linkType: hard

"hast-util-from-dom@npm:^5.0.0":
  version: 5.0.1
  resolution: "hast-util-from-dom@npm:5.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hastscript: ^9.0.0
    web-namespaces: ^2.0.0
  checksum: d79e91d90acd81ee00752b791ff3173f7bb29490a34b7156bcc8853743fd6e72702f314a19720d2c0cdf3ff794484be6f3804c93916f430f16b6f59539aa04b7
  languageName: node
  linkType: hard

"hast-util-from-html-isomorphic@npm:^2.0.0":
  version: 2.0.0
  resolution: "hast-util-from-html-isomorphic@npm:2.0.0"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-from-dom: ^5.0.0
    hast-util-from-html: ^2.0.0
    unist-util-remove-position: ^5.0.0
  checksum: a98d02890bd1b5a804a1b2aaacd0332a6563f2a8df620450e38ab8962728cda0485cd29435824840621d1e653943776864e912d78d24cce6a7f484011ee7cef0
  languageName: node
  linkType: hard

"hast-util-from-html@npm:^2.0.0":
  version: 2.0.3
  resolution: "hast-util-from-html@npm:2.0.3"
  dependencies:
    "@types/hast": ^3.0.0
    devlop: ^1.1.0
    hast-util-from-parse5: ^8.0.0
    parse5: ^7.0.0
    vfile: ^6.0.0
    vfile-message: ^4.0.0
  checksum: 50f589f25a82868d611668421ff1d7997778743b34fbde77cd74d152350162b5045090c65ee3c3e2b4d51568f35426a0fc851ee1965723e1abf466f7f9d0bd83
  languageName: node
  linkType: hard

"hast-util-from-parse5@npm:^8.0.0, hast-util-from-parse5@npm:^8.0.1":
  version: 8.0.2
  resolution: "hast-util-from-parse5@npm:8.0.2"
  dependencies:
    "@types/hast": ^3.0.0
    "@types/unist": ^3.0.0
    devlop: ^1.0.0
    hastscript: ^9.0.0
    property-information: ^6.0.0
    vfile: ^6.0.0
    vfile-location: ^5.0.0
    web-namespaces: ^2.0.0
  checksum: d3e1418ed3d7eef4a0977938acdb2fe26372569f61d1354a151f24a2211f9360765083a4f2560e4f3ad1d6ca94f4d84af176b8de88dc5681c42b31db5eea3440
  languageName: node
  linkType: hard

"hast-util-from-string@npm:^3.0.0":
  version: 3.0.1
  resolution: "hast-util-from-string@npm:3.0.1"
  dependencies:
    "@types/hast": ^3.0.0
  checksum: 0fb02fc88cf10aaebe02acedcfa6bedcb075a440028415ba7e32993db247f3cd113aa9131fd10a09b0477cedb350906f193a7738bbb041034b9bcb23623aac72
  languageName: node
  linkType: hard

"hast-util-has-property@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-has-property@npm:3.0.0"
  dependencies:
    "@types/hast": ^3.0.0
  checksum: 3e515e95432c6251eefeb5aade4b9626f033d3ac0020e2f64aa38afbb345c7bb0c5d541fba6c53367245d7f5b555dc3c86543cd8231879d272cb0912808dfc19
  languageName: node
  linkType: hard

"hast-util-heading-rank@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-heading-rank@npm:3.0.0"
  dependencies:
    "@types/hast": ^3.0.0
  checksum: e5ce4ec9e8017b24ab72702fa0dd401ec6eaf32574120d71c2aa4e8e0f43829dba2e291f49d305a47e8d65b82a9c5adad7985385dc5bc8370f8cec7c8f9313d3
  languageName: node
  linkType: hard

"hast-util-is-conditional-comment@npm:^3.0.0":
  version: 3.0.1
  resolution: "hast-util-is-conditional-comment@npm:3.0.1"
  dependencies:
    "@types/hast": ^3.0.0
  checksum: b790ea1964c9517f5c7f1b7f374d80b563fbc47f115e7db1072cea426a4dbfd50d13c1073de778d85de32616ce8a647a22a6ac26f5a332085f3dd580d7f7fc43
  languageName: node
  linkType: hard

"hast-util-is-css-link@npm:^3.0.0":
  version: 3.0.1
  resolution: "hast-util-is-css-link@npm:3.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    collapse-white-space: ^2.0.0
  checksum: dc35cdd8dd5615b9294337d8b2fa25ad9fb66d7c02df7daeeb28b852271d8deae0b506e233f87a95be91228d31299b00f3c1cf6e627007ab9f49923a4cdbdecb
  languageName: node
  linkType: hard

"hast-util-is-css-style@npm:^3.0.0":
  version: 3.0.1
  resolution: "hast-util-is-css-style@npm:3.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    collapse-white-space: ^2.0.0
  checksum: 6ab0bd3e5349de42aed7698ecda9f8820cbbfe8208ba83761292ca303c82232d12d329d07cd3750c39ab0a244d5221d83463dee576eb306e38b51e0416837d40
  languageName: node
  linkType: hard

"hast-util-is-element@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-is-element@npm:3.0.0"
  dependencies:
    "@types/hast": ^3.0.0
  checksum: 82569a420eda5877c52fdbbdbe26675f012c02d70813dfd19acffdee328e42e4bd0b7ae34454cfcbcb932b2bedbd7ddc119f943a0cfb234120f9456d6c0c4331
  languageName: node
  linkType: hard

"hast-util-is-event-handler@npm:^3.0.0":
  version: 3.0.1
  resolution: "hast-util-is-event-handler@npm:3.0.1"
  checksum: 054f4697e056bac45ea203fc3d8b3c8f45a016b8abb44b6653dc7e4333d6a1a92e0d1c7c53cc59af4d2c084979a8928374099cc3e39b43383f128a2e862d2f4d
  languageName: node
  linkType: hard

"hast-util-is-javascript@npm:^3.0.0":
  version: 3.0.1
  resolution: "hast-util-is-javascript@npm:3.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    collapse-white-space: ^2.0.0
  checksum: 1be36b91eff64de205507d07215554f271af4fd2f6b947136a661a52d73e4adc400902d4ad32068abcc4ff02af65ea99b7273ac05c5e2b29a82e0a2ef1502755
  languageName: node
  linkType: hard

"hast-util-minify-whitespace@npm:^1.0.0":
  version: 1.0.1
  resolution: "hast-util-minify-whitespace@npm:1.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-embedded: ^3.0.0
    hast-util-is-element: ^3.0.0
    hast-util-whitespace: ^3.0.0
    unist-util-is: ^6.0.0
  checksum: e74f883a52591798ef99a864a34905163f7754d9fafa2eba96956b5fde798b28e65990cefe056349285b96c3fd9ae50e5c9a2c3429f2af40799831b2a769ddfb
  languageName: node
  linkType: hard

"hast-util-parse-selector@npm:^3.0.0":
  version: 3.1.1
  resolution: "hast-util-parse-selector@npm:3.1.1"
  dependencies:
    "@types/hast": ^2.0.0
  checksum: 511d373465f60dd65e924f88bf0954085f4fb6e3a2b062a4b5ac43b93cbfd36a8dce6234b5d1e3e63499d936375687e83fc5da55628b22bd6b581b5ee167d1c4
  languageName: node
  linkType: hard

"hast-util-parse-selector@npm:^4.0.0":
  version: 4.0.0
  resolution: "hast-util-parse-selector@npm:4.0.0"
  dependencies:
    "@types/hast": ^3.0.0
  checksum: 76087670d3b0b50b23a6cb70bca53a6176d6608307ccdbb3ed18b650b82e7c3513bfc40348f1389dc0c5ae872b9a768851f4335f44654abd7deafd6974c52402
  languageName: node
  linkType: hard

"hast-util-select@npm:^6.0.0":
  version: 6.0.3
  resolution: "hast-util-select@npm:6.0.3"
  dependencies:
    "@types/hast": ^3.0.0
    "@types/unist": ^3.0.0
    bcp-47-match: ^2.0.0
    comma-separated-tokens: ^2.0.0
    css-selector-parser: ^3.0.0
    devlop: ^1.0.0
    direction: ^2.0.0
    hast-util-has-property: ^3.0.0
    hast-util-to-string: ^3.0.0
    hast-util-whitespace: ^3.0.0
    nth-check: ^2.0.0
    property-information: ^6.0.0
    space-separated-tokens: ^2.0.0
    unist-util-visit: ^5.0.0
    zwitch: ^2.0.0
  checksum: 75aecafbc04f9adf90944394efd0a2b2cb890ff3d2460205ce5ffc0d386c3abf122f46d3971f320108a79a45a244c8ab26867de6950aa7178df81145f655c04a
  languageName: node
  linkType: hard

"hast-util-to-estree@npm:^3.0.0":
  version: 3.1.1
  resolution: "hast-util-to-estree@npm:3.1.1"
  dependencies:
    "@types/estree": ^1.0.0
    "@types/estree-jsx": ^1.0.0
    "@types/hast": ^3.0.0
    comma-separated-tokens: ^2.0.0
    devlop: ^1.0.0
    estree-util-attach-comments: ^3.0.0
    estree-util-is-identifier-name: ^3.0.0
    hast-util-whitespace: ^3.0.0
    mdast-util-mdx-expression: ^2.0.0
    mdast-util-mdx-jsx: ^3.0.0
    mdast-util-mdxjs-esm: ^2.0.0
    property-information: ^6.0.0
    space-separated-tokens: ^2.0.0
    style-to-object: ^1.0.0
    unist-util-position: ^5.0.0
    zwitch: ^2.0.0
  checksum: e23eec63b6a0a15b9053a1dd7e8fef767202335ed009c66d82b4ea1226ed9e7f45c6dd4f255810aec470aba8b5cd5ba77f8f914e808f2d3cde4341dd297f911f
  languageName: node
  linkType: hard

"hast-util-to-html@npm:^9.0.0":
  version: 9.0.4
  resolution: "hast-util-to-html@npm:9.0.4"
  dependencies:
    "@types/hast": ^3.0.0
    "@types/unist": ^3.0.0
    ccount: ^2.0.0
    comma-separated-tokens: ^2.0.0
    hast-util-whitespace: ^3.0.0
    html-void-elements: ^3.0.0
    mdast-util-to-hast: ^13.0.0
    property-information: ^6.0.0
    space-separated-tokens: ^2.0.0
    stringify-entities: ^4.0.0
    zwitch: ^2.0.4
  checksum: 6b97f641bca4c1de66bd74dd5a965bc5fd5c4b8e09328448c4952226ebd691c107cc990ce4e29ccb1e6bfff0278d8956fc8159533456c167f94ae067b4b42b11
  languageName: node
  linkType: hard

"hast-util-to-jsx-runtime@npm:^2.0.0":
  version: 2.3.2
  resolution: "hast-util-to-jsx-runtime@npm:2.3.2"
  dependencies:
    "@types/estree": ^1.0.0
    "@types/hast": ^3.0.0
    "@types/unist": ^3.0.0
    comma-separated-tokens: ^2.0.0
    devlop: ^1.0.0
    estree-util-is-identifier-name: ^3.0.0
    hast-util-whitespace: ^3.0.0
    mdast-util-mdx-expression: ^2.0.0
    mdast-util-mdx-jsx: ^3.0.0
    mdast-util-mdxjs-esm: ^2.0.0
    property-information: ^6.0.0
    space-separated-tokens: ^2.0.0
    style-to-object: ^1.0.0
    unist-util-position: ^5.0.0
    vfile-message: ^4.0.0
  checksum: 223cc3e2ea622d14529e2aa070bd88f6ca7255084bd5e6e28015dad435cda22b1ddd98064bba6a4753d546d882dcd3f8067af1ea27c253986f6f303869544075
  languageName: node
  linkType: hard

"hast-util-to-string@npm:^3.0.0":
  version: 3.0.1
  resolution: "hast-util-to-string@npm:3.0.1"
  dependencies:
    "@types/hast": ^3.0.0
  checksum: 556f3cb118fc09e3a6cd149ee4b4056a49028a3858a7d37617e4c6d2c9c5e2336d5fb87eb5f41211b1977a964c705aa70e419464c12debc1959ed03fdad5bed6
  languageName: node
  linkType: hard

"hast-util-to-text@npm:^4.0.0":
  version: 4.0.2
  resolution: "hast-util-to-text@npm:4.0.2"
  dependencies:
    "@types/hast": ^3.0.0
    "@types/unist": ^3.0.0
    hast-util-is-element: ^3.0.0
    unist-util-find-after: ^5.0.0
  checksum: 72cce08666b86511595d3eef52236b86897cfbac166f2a0752b70b16d1f590b5aa91ea1a553c0d1603f9e0c7e373ceacab381be3d8f176129ad6e301d2a56d94
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-whitespace@npm:3.0.0"
  dependencies:
    "@types/hast": ^3.0.0
  checksum: 41d93ccce218ba935dc3c12acdf586193c35069489c8c8f50c2aa824c00dec94a3c78b03d1db40fa75381942a189161922e4b7bca700b3a2cc779634c351a1e4
  languageName: node
  linkType: hard

"hastscript@npm:^7.0.0":
  version: 7.2.0
  resolution: "hastscript@npm:7.2.0"
  dependencies:
    "@types/hast": ^2.0.0
    comma-separated-tokens: ^2.0.0
    hast-util-parse-selector: ^3.0.0
    property-information: ^6.0.0
    space-separated-tokens: ^2.0.0
  checksum: 928a21576ff7b9a8c945e7940bcbf2d27f770edb4279d4d04b33dc90753e26ca35c1172d626f54afebd377b2afa32331e399feb3eb0f7b91a399dca5927078ae
  languageName: node
  linkType: hard

"hastscript@npm:^9.0.0":
  version: 9.0.0
  resolution: "hastscript@npm:9.0.0"
  dependencies:
    "@types/hast": ^3.0.0
    comma-separated-tokens: ^2.0.0
    hast-util-parse-selector: ^4.0.0
    property-information: ^6.0.0
    space-separated-tokens: ^2.0.0
  checksum: de8daf7686b478f459b3a586cdae92a3538d12d19144ab3172e067da10b19f7afb4709c8b015dcd343ef2f4c9701289131d8b6a92474f5a94bd90c5aa74028c1
  languageName: node
  linkType: hard

"html-enumerated-attributes@npm:^1.0.0":
  version: 1.1.1
  resolution: "html-enumerated-attributes@npm:1.1.1"
  checksum: 0dfe93130f074028e6e2fa319ec84979fb4e2b6e3bc20089896c1bb72d7302f5bf312ab60d6535034511f36b18a2ad7d8bf151971076dcc096f7bb0046fc9253
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.2":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: d2df2da3ad40ca9ee3a39c5cc6475ef67c8f83c234475f24d8e9ce0dc80a2c82df8e1d6fa78ddd1e9022a586ea1bd247a615e80a5cd9273d90111ddda7d9e974
  languageName: node
  linkType: hard

"html-url-attributes@npm:^3.0.0":
  version: 3.0.1
  resolution: "html-url-attributes@npm:3.0.1"
  checksum: 1ecbf9cae0c438d2802386710177b7bbf7e30cc61327e9f125eb32fca7302cd1e3ab45c441859cb1e7646109be322fc1163592ad4dfde9b14d09416d101a6573
  languageName: node
  linkType: hard

"html-void-elements@npm:^3.0.0":
  version: 3.0.0
  resolution: "html-void-elements@npm:3.0.0"
  checksum: 59be397525465a7489028afa064c55763d9cccd1d7d9f630cca47137317f0e897a9ca26cef7e745e7cff1abc44260cfa407742b243a54261dfacd42230e94fce
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"human-signals@npm:^4.3.0":
  version: 4.3.1
  resolution: "human-signals@npm:4.3.1"
  checksum: 6f12958df3f21b6fdaf02d90896c271df00636a31e2bbea05bddf817a35c66b38a6fdac5863e2df85bd52f34958997f1f50350ff97249e1dff8452865d5235d1
  languageName: node
  linkType: hard

"husky@npm:^9.0.0":
  version: 9.1.7
  resolution: "husky@npm:9.1.7"
  bin:
    husky: bin.js
  checksum: c2412753f15695db369634ba70f50f5c0b7e5cb13b673d0826c411ec1bd9ddef08c1dad89ea154f57da2521d2605bd64308af748749b27d08c5f563bcd89975f
  languageName: node
  linkType: hard

"hyperdyperid@npm:^1.2.0":
  version: 1.2.0
  resolution: "hyperdyperid@npm:1.2.0"
  checksum: 210029d1c86926f09109f6317d143f8b056fc38e8dd11b0c3e3205fc6c6ff8429fb55b4b9c2bce065462719ed9d34366eced387aaa0035d93eb76b306a8547ef
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.4.4":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 2acfd32a573260ea522ea0bfeff880af426d68f6831f973129e2ba7363f422923cf53aab62f8369cbf4667c7b25b6f8a3761b34ecdb284ea18e87a5262a865be
  languageName: node
  linkType: hard

"image-size@npm:2.0.1":
  version: 2.0.1
  resolution: "image-size@npm:2.0.1"
  bin:
    image-size: bin/image-size.js
  checksum: 1057c58f5b17fddb17c4b1d43e28a9574dfc231411c42b9eca2dcb2b96fa2c19e3ef933b281ce9b549cac42f6fc1ea356481e939bf63655fd9d757781456c1ef
  languageName: node
  linkType: hard

"imagescript@npm:^1.2.16":
  version: 1.3.0
  resolution: "imagescript@npm:1.3.0"
  checksum: b1f16bf1f1d4daf9572c536921b9244edc6bf31c23e399a4f705f75bd52f0d3af5583435389171b9aaf34720a75bce3755129ca62540f6a50e33e31d3c2c16da
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"inflection@npm:^3.0.0":
  version: 3.0.2
  resolution: "inflection@npm:3.0.2"
  checksum: 4151df1bbde49701847df1c176d3b6970aba52f9119977529e1fbaf7840155903f9f422bc96ad903852d68e40cb477711efaf1e0032331b0b48d52ffe61a4bdc
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.1, inherits@npm:~2.0.1, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.2.4":
  version: 0.2.4
  resolution: "inline-style-parser@npm:0.2.4"
  checksum: 5df20a21dd8d67104faaae29774bb50dc9690c75bc5c45dac107559670a5530104ead72c4cf54f390026e617e7014c65b3d68fb0bb573a37c4d1f94e9c36e1ca
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.2
    side-channel: ^1.1.0
  checksum: 8e0991c2d048cc08dab0a91f573c99f6a4215075887517ea4fa32203ce8aea60fa03f95b177977fa27eb502e5168366d0f3e02c762b799691411d49900611861
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"is-alphabetical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphabetical@npm:2.0.1"
  checksum: 56207db8d9de0850f0cd30f4966bf731eb82cedfe496cbc2e97e7c3bacaf66fc54a972d2d08c0d93bb679cb84976a05d24c5ad63de56fabbfc60aadae312edaa
  languageName: node
  linkType: hard

"is-alphanumerical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphanumerical@npm:2.0.1"
  dependencies:
    is-alphabetical: ^2.0.0
    is-decimal: ^2.0.0
  checksum: 87acc068008d4c9c4e9f5bd5e251041d42e7a50995c77b1499cf6ed248f971aadeddb11f239cabf09f7975ee58cac7a48ffc170b7890076d8d227b24a68663c9
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: f137a2a6e77af682cdbffef1e633c140cf596f72321baf8bba0f4ef22685eb4339dde23dfe9e9ca430b5f961dee4d46577dcf12b792b68518c8449b134fb9156
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: ^1.0.0
    call-bound: ^1.0.3
    get-proto: ^1.0.1
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: 9bece45133da26636488ca127d7686b85ad3ca18927e2850cff1937a650059e90be1c71a48623f8791646bb7a241b0cabf602a0b9252dcfa5ab273f2399000e6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: ^1.0.2
  checksum: ee1544f0e664f253306786ed1dce494b8cf242ef415d6375d8545b4d8816b0f054bd9f948a8988ae2c6325d1c28260dd02978236b2f7b8fb70dfc4838a6c9fa7
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 0415b181e8f1bfd5d3f8a20f8108e64d372a72131674eea9c2923f39d065b6ad08d654765553bdbffbd92c3746f1007986c34087db1bd89a31f71be8359ccdaa
  languageName: node
  linkType: hard

"is-bun-module@npm:^1.0.2":
  version: 1.3.0
  resolution: "is-bun-module@npm:1.3.0"
  dependencies:
    semver: ^7.6.3
  checksum: b23d9ec7b4d4bfd89e4e72b5cd52e1bc153facad59fdd7394c656f8859a78740ef35996a2066240a32f39cc9a9da4b4eb69e68df3c71755a61ebbaf56d3daef0
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.15.1, is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: ^2.0.2
  checksum: 6ec5b3c42d9cbf1ac23f164b16b8a140c3cec338bf8f884c076ca89950c7cc04c33e78f02b8cae7ff4751f3247e3174b2330f1fe4de194c7210deb8b1ea316a7
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    is-typed-array: ^1.1.13
  checksum: 31600dd19932eae7fd304567e465709ffbfa17fa236427c9c864148e1b54eb2146357fcf3aed9b686dee13c217e1bb5a649cb3b9c479e1004c0648e9febde1b2
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: d6c36ab9d20971d65f3fc64cef940d57a4900a2ac85fb488a46d164c2072a33da1cb51eefcc039e3e5c208acbce343d3480b84ab5ff0983f617512da2742562a
  languageName: node
  linkType: hard

"is-decimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-decimal@npm:2.0.1"
  checksum: 97132de7acdce77caa7b797632970a2ecd649a88e715db0e4dbc00ab0708b5e7574ba5903962c860cd4894a14fd12b100c0c4ac8aed445cf6f55c6cf747a4158
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: 3875571d20a7563772ecc7a5f36cb03167e9be31ad259041b4a8f73f33f885441f778cee1f1fe0085eb4bc71679b9d8c923690003a36a6a5fdf8023e6e3f0672
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 38c646c506e64ead41a36c182d91639833311970b6b6c6268634f109eef0a1a9d2f1f2e499ef4cb43c744a13443c4cdd2f0812d5afdcee5e9b65b72b28c48557
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-fullwidth-code-point@npm:4.0.0"
  checksum: 8ae89bf5057bdf4f57b346fb6c55e9c3dd2549983d54191d722d5c739397a903012cc41a04ee3403fd872e811243ef91a7c5196da7b5841dc6b6aae31a264a8d
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    get-proto: ^1.0.0
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: f7f7276131bdf7e28169b86ac55a5b080012a597f9d85a0cbef6fe202a7133fa450a3b453e394870e3cb3685c5a764c64a9f12f614684b46969b1e6f297bed6b
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-hexadecimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-hexadecimal@npm:2.0.1"
  checksum: 66a2ea85994c622858f063f23eda506db29d92b52580709eb6f4c19550552d4dcf3fb81952e52f7cf972097237959e00adc7bb8c9400cd12886e15bf06145321
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: e6ce5f6380f32b141b3153e6ba9074892bbbbd655e92e7ba5ff195239777e767a976dcd4e22f864accaf30e53ebf961ab1995424aef91af68788f0591b7396cc
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 6517f0a0e8c4b197a21afb45cd3053dc711e79d45d8878aa3565de38d0102b130ca8732485122c7b336e98c27dacd5236854e3e6526e0eb30cae64956535662f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.0.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 6dc45da70d04a81f35c9310971e78a6a3c7a63547ef782e3a07ee3674695081b6ca4e977fbb8efc48dae3375e0b34558d2bcd722aec9bddfa2d7db5b041be8ce
  languageName: node
  linkType: hard

"is-plain-object@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-plain-object@npm:5.0.0"
  checksum: e32d27061eef62c0847d303125440a38660517e586f2f3db7c9d179ae5b6674ab0f469d519b2e25c147a1a3bc87156d0d5f4d8821e0ce4a9ee7fe1fcf11ce45c
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 99ee0b6d30ef1bb61fa4b22fae7056c6c9b3c693803c0c284ff7a8570f83075a7d38cda53b06b7996d441215c27895ea5d1af62124562e13d91b3dbec41a5e13
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 36e3f8c44bdbe9496c9689762cc4110f6a6a12b767c5d74c0398176aa2678d4467e3bf07595556f2dba897751bde1422480212b97d973c7b08a343100b0c0dfe
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1611fedc175796eebb88f4dfc393dd969a4a8e6c69cadaff424ee9d4464f9f026399a5f84a90f7c62d6d7ee04e3626a912149726de102b0bd6c1ee6a9868fa5a
  languageName: node
  linkType: hard

"is-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-stream@npm:3.0.0"
  checksum: 172093fe99119ffd07611ab6d1bcccfe8bc4aa80d864b15f43e63e54b7abc71e779acd69afdb854c4e2a67fdc16ae710e370eda40088d1cfc956a50ed82d8f16
  languageName: node
  linkType: hard

"is-string@npm:^1.0.7, is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 2eeaaff605250f5e836ea3500d33d1a5d3aa98d008641d9d42fb941e929ffd25972326c2ef912987e54c95b6f10416281aaf1b35cdf81992cfb7524c5de8e193
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.2
    has-symbols: ^1.1.0
    safe-regex-test: ^1.1.0
  checksum: bfafacf037af6f3c9d68820b74be4ae8a736a658a3344072df9642a090016e281797ba8edbeb1c83425879aae55d1cb1f30b38bf132d703692b2570367358032
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: ^1.1.16
  checksum: ea7cfc46c282f805d19a9ab2084fd4542fed99219ee9dbfbc26284728bd713a51eac66daa74eca00ae0a43b61322920ba334793607dc39907465913e921e0892
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: f36aef758b46990e0d3c37269619c0a08c5b29428c0bb11ecba7f75203442d6c7801239c2f31314bc79199217ef08263787f3837d9e22610ad1da62970d6616d
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1769b9aed5d435a3a989ffc18fc4ad1947d2acdaf530eb2bd6af844861b545047ea51102f75901f89043bed0267ed61d914ee21e6e8b9aa734ec201cdfc0726f
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: 5c6c8415a06065d78bdd5e3a771483aa1cd928df19138aa73c4c51333226f203f22117b4325df55cc8b3085a6716870a320c2d757efee92d7a7091a039082041
  languageName: node
  linkType: hard

"isarray@npm:0.0.1":
  version: 0.0.1
  resolution: "isarray@npm:0.0.1"
  checksum: 49191f1425681df4a18c2f0f93db3adb85573bcdd6a4482539d98eac9e705d8961317b01175627e860516a2fc45f8f9302db26e5a380a97a520e272e2a40a8d4
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: ^1.1.4
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.6
    get-proto: ^1.0.0
    has-symbols: ^1.1.0
    set-function-name: ^2.0.2
  checksum: 7db23c42629ba4790e6e15f78b555f41dbd08818c85af306988364bd19d86716a1187cb333444f3a0036bfc078a0e9cb7ec67fef3a61662736d16410d7f77869
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"jiti@npm:^2.4.2":
  version: 2.4.2
  resolution: "jiti@npm:2.4.2"
  bin:
    jiti: lib/jiti-cli.mjs
  checksum: c6c30c7b6b293e9f26addfb332b63d964a9f143cdd2cf5e946dbe5143db89f7c1b50ad9223b77fb1f6ddb0b9c5ecef995fea024ecf7d2861d285d779cde66e1e
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:4.1.0, js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"jsbi@npm:^4.3.0":
  version: 4.3.0
  resolution: "jsbi@npm:4.3.0"
  checksum: 27c4f178eb7fd9d1756144066fdebc62f4a0176e877f55e646e8ce84075c13551bd575a316b9959ccdcca9d5dc05a81c9907cfa09f0cfeb43c9777797e36b0e9
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 19c94095ea026725540c0d29da33ab03144f6bcf2d4159e4833d534976e99e0c09c38cefa9a575279a51fc36b31166f8d6d05c9fe2645d5f15851d690b41f17f
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: a36d3ca40574a974d9c2063bf68c2b6141c20da8f2a36bd3279fc802563f35f0527a6c828801295bdfb2803952cf2cf387786c2c90ed564f88d5782475abfe3c
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: 866458a8c58a95a49bef3adba929c625e82532bcff1fe93f01d29cb02cac7c3fe1f4b79951b7792c2da9de0b32871a8401a6e3c5b36778ad852bf5b8a61165d7
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0, jsx-ast-utils@npm:^3.3.5":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flat: ^1.3.1
    object.assign: ^4.1.4
    object.values: ^1.1.6
  checksum: f4b05fa4d7b5234230c905cfa88d36dc8a58a6666975a3891429b1a8cdc8a140bca76c297225cb7a499fad25a2c052ac93934449a2c31a44fc9edd06c773780a
  languageName: node
  linkType: hard

"katex@npm:^0.16.0":
  version: 0.16.21
  resolution: "katex@npm:0.16.21"
  dependencies:
    commander: ^8.3.0
  bin:
    katex: cli.js
  checksum: 14180322a4e8fe9e4227a08b7d86fde9ee445859ff534e6a540b85eb5022b39ea2be70082776cce8c59b891c247fce3d1c1a090ea7821e005fd8b7bfee714936
  languageName: node
  linkType: hard

"kbar@npm:0.1.0-beta.45":
  version: 0.1.0-beta.45
  resolution: "kbar@npm:0.1.0-beta.45"
  dependencies:
    "@radix-ui/react-portal": ^1.0.1
    fast-equals: ^2.0.3
    fuse.js: ^6.6.2
    react-virtual: ^2.8.2
    tiny-invariant: ^1.2.0
  peerDependencies:
    react: ^16.0.0 || ^17.0.0 || ^18.0.0
    react-dom: ^16.0.0 || ^17.0.0 || ^18.0.0
  checksum: d72c0ad334793ed4df62b929cfdd2502e8102ca55c44091604c8b9ae50655e33f0c1a266c86015d55aa3786ca484da38f2c5b521060bbc4cadb2389d45404efb
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.0, kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"language-subtag-registry@npm:^0.3.20":
  version: 0.3.23
  resolution: "language-subtag-registry@npm:0.3.23"
  checksum: 0b64c1a6c5431c8df648a6d25594ff280613c886f4a1a542d9b864e5472fb93e5c7856b9c41595c38fac31370328fc79fcc521712e89ea6d6866cbb8e0995d81
  languageName: node
  linkType: hard

"language-tags@npm:^1.0.9":
  version: 1.0.9
  resolution: "language-tags@npm:1.0.9"
  dependencies:
    language-subtag-registry: ^0.3.20
  checksum: 57c530796dc7179914dee71bc94f3747fd694612480241d0453a063777265dfe3a951037f7acb48f456bf167d6eb419d4c00263745326b3ba1cdcf4657070e78
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-darwin-arm64@npm:1.29.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-darwin-x64@npm:1.29.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-freebsd-x64@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-freebsd-x64@npm:1.29.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.29.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-linux-arm64-gnu@npm:1.29.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-linux-arm64-musl@npm:1.29.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-linux-x64-gnu@npm:1.29.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-linux-x64-musl@npm:1.29.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-win32-arm64-msvc@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-win32-arm64-msvc@npm:1.29.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.29.1":
  version: 1.29.1
  resolution: "lightningcss-win32-x64-msvc@npm:1.29.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss@npm:^1.29.1":
  version: 1.29.1
  resolution: "lightningcss@npm:1.29.1"
  dependencies:
    detect-libc: ^1.0.3
    lightningcss-darwin-arm64: 1.29.1
    lightningcss-darwin-x64: 1.29.1
    lightningcss-freebsd-x64: 1.29.1
    lightningcss-linux-arm-gnueabihf: 1.29.1
    lightningcss-linux-arm64-gnu: 1.29.1
    lightningcss-linux-arm64-musl: 1.29.1
    lightningcss-linux-x64-gnu: 1.29.1
    lightningcss-linux-x64-musl: 1.29.1
    lightningcss-win32-arm64-msvc: 1.29.1
    lightningcss-win32-x64-msvc: 1.29.1
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-freebsd-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-arm64-msvc:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: d1c4dba66dfe7f6a76532bdb84c35742bee61149550e5eb5b0e84e282f21aecd335f917ca9619bb7ca95fc1eb3092dc7e22f2c16b01e9a0ee472b76452343cce
  languageName: node
  linkType: hard

"lilconfig@npm:2.1.0":
  version: 2.1.0
  resolution: "lilconfig@npm:2.1.0"
  checksum: 8549bb352b8192375fed4a74694cd61ad293904eee33f9d4866c2192865c44c4eb35d10782966242634e0cbc1e91fe62b1247f148dc5514918e3a966da7ea117
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"lint-staged@npm:^13.0.0":
  version: 13.3.0
  resolution: "lint-staged@npm:13.3.0"
  dependencies:
    chalk: 5.3.0
    commander: 11.0.0
    debug: 4.3.4
    execa: 7.2.0
    lilconfig: 2.1.0
    listr2: 6.6.1
    micromatch: 4.0.5
    pidtree: 0.6.0
    string-argv: 0.3.2
    yaml: 2.3.1
  bin:
    lint-staged: bin/lint-staged.js
  checksum: f7c146cc2849c9ce4f1d2808d990fcbdef5e0bb79e6e79cc895f53c91f5ac4dcefdb8c3465156b38a015dcb051f2795c6bda4f20a1e2f2fa654c7ba521b2d2e0
  languageName: node
  linkType: hard

"listr2@npm:6.6.1":
  version: 6.6.1
  resolution: "listr2@npm:6.6.1"
  dependencies:
    cli-truncate: ^3.1.0
    colorette: ^2.0.20
    eventemitter3: ^5.0.1
    log-update: ^5.0.1
    rfdc: ^1.3.0
    wrap-ansi: ^8.1.0
  peerDependencies:
    enquirer: ">= 2.3.0 < 3"
  peerDependenciesMeta:
    enquirer:
      optional: true
  checksum: 99600e8a51f838f7208bce7e16d6b3d91d361f13881e6aa91d0b561a9a093ddcf63b7bc2a7b47aec7fdbff9d0e8c9f68cb66e6dfe2d857e5b1df8ab045c26ce8
  languageName: node
  linkType: hard

"lit-element@npm:^4.1.0":
  version: 4.1.1
  resolution: "lit-element@npm:4.1.1"
  dependencies:
    "@lit-labs/ssr-dom-shim": ^1.2.0
    "@lit/reactive-element": ^2.0.4
    lit-html: ^3.2.0
  checksum: 74d0f2d6fb784b1e96f27c54d036b6eb49ca9883577db627ce9ca42b242c5a5da3ae7b5c874fe2a0559ee6134726a741ec2166a48bfee90ab91346a237f33e85
  languageName: node
  linkType: hard

"lit-html@npm:^3.2.0":
  version: 3.2.1
  resolution: "lit-html@npm:3.2.1"
  dependencies:
    "@types/trusted-types": ^2.0.2
  checksum: 1bacd9f8b2acfe44a989c09c21f1aedbe409409196eff8d203c952c7ad034899b16d239551efa76872a3cea21883db62a5309aeefc604a1e3d4a8d36c9719e63
  languageName: node
  linkType: hard

"lit@npm:^3.2.1":
  version: 3.2.1
  resolution: "lit@npm:3.2.1"
  dependencies:
    "@lit/reactive-element": ^2.0.4
    lit-element: ^4.1.0
    lit-html: ^3.2.0
  checksum: ee22bbc53d5d639258b4a3a3d10f166adf959fd4ddb61db739d0a8328d0094a2232ffebb808fec3a645aeb166b15c03c688f75dde723524bf7b22263e078158f
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: cb9227612f71b83e42de93eccf1232feeb25e705bdb19ba26c04f91e885bfd3dd5c517c4a97137658190581d3493ea3973072ca010aab7e301046d90740393d1
  languageName: node
  linkType: hard

"lodash.castarray@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.castarray@npm:4.4.0"
  checksum: fca8c7047e0ae2738b0b2503fb00157ae0ff6d8a1b716f87ed715b22560e09de438c75b65e01a7e44ceb41c5b31dce2eb576e46db04beb9c699c498e03cbd00f
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: 29c6351f281e0d9a1d58f1a4c8f4400924b4c79f18dfc4613624d7d54784df07efaff97c1ff2659f3e085ecf4fff493300adc4837553104cef2634110b0d5337
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"log-update@npm:^5.0.1":
  version: 5.0.1
  resolution: "log-update@npm:5.0.1"
  dependencies:
    ansi-escapes: ^5.0.0
    cli-cursor: ^4.0.0
    slice-ansi: ^5.0.0
    strip-ansi: ^7.0.1
    wrap-ansi: ^8.0.1
  checksum: 2c6b47dcce6f9233df6d232a37d9834cb3657a0749ef6398f1706118de74c55f158587d4128c225297ea66803f35c5ac3db4f3f617046d817233c45eedc32ef1
  languageName: node
  linkType: hard

"long@npm:^5.0.0":
  version: 5.2.4
  resolution: "long@npm:5.2.4"
  checksum: abffed049d2192a94415dc5d19e471a26d6753ba8b021dcef98b5424eec93cf6f293489524303f025dcdabae83bc07fc38acca34c060356f38449cd246f2646c
  languageName: node
  linkType: hard

"longest-streak@npm:^3.0.0":
  version: 3.1.0
  resolution: "longest-streak@npm:3.1.0"
  checksum: d7f952ed004cbdb5c8bcfc4f7f5c3d65449e6c5a9e9be4505a656e3df5a57ee125f284286b4bf8ecea0c21a7b3bf2b8f9001ad506c319b9815ad6a63a47d0fd0
  languageName: node
  linkType: hard

"loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: ^2.0.3
  checksum: 83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"markdown-extensions@npm:^2.0.0":
  version: 2.0.0
  resolution: "markdown-extensions@npm:2.0.0"
  checksum: ec4ffcb0768f112e778e7ac74cb8ef22a966c168c3e6c29829f007f015b0a0b5c79c73ee8599a0c72e440e7f5cfdbf19e80e2d77b9a313b8f66e180a330cf1b2
  languageName: node
  linkType: hard

"markdown-table@npm:^3.0.0":
  version: 3.0.4
  resolution: "markdown-table@npm:3.0.4"
  checksum: bc24b177cbb3ef170cb38c9f191476aa63f7236ebc8980317c5e91b5bf98c8fb471cf46d8920478c5e770d7f4337326f6b5b3efbf0687c2044fd332d7a64dfcb
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 0e513b29d120f478c85a70f49da0b8b19bc638975eca466f2eeae0071f3ad00454c621bf66e16dd435896c208e719fc91ad79bbfba4e400fe0b372e7c1c9c9a2
  languageName: node
  linkType: hard

"mdast-util-find-and-replace@npm:^3.0.0":
  version: 3.0.2
  resolution: "mdast-util-find-and-replace@npm:3.0.2"
  dependencies:
    "@types/mdast": ^4.0.0
    escape-string-regexp: ^5.0.0
    unist-util-is: ^6.0.0
    unist-util-visit-parents: ^6.0.0
  checksum: 00dde8aaf87d065034b911bdae20d17c107f5103c6ba5a3d117598c847ce005c6b03114b5603e0d07cc61fefcbb05bdb9f66100efeaa0278dbd80eda1087595f
  languageName: node
  linkType: hard

"mdast-util-from-markdown@npm:^2.0.0":
  version: 2.0.2
  resolution: "mdast-util-from-markdown@npm:2.0.2"
  dependencies:
    "@types/mdast": ^4.0.0
    "@types/unist": ^3.0.0
    decode-named-character-reference: ^1.0.0
    devlop: ^1.0.0
    mdast-util-to-string: ^4.0.0
    micromark: ^4.0.0
    micromark-util-decode-numeric-character-reference: ^2.0.0
    micromark-util-decode-string: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
    unist-util-stringify-position: ^4.0.0
  checksum: 1ad19f48b30ac6e0cb756070c210c78ad93c26876edfb3f75127783bc6df8b9402016d8f3e9964f3d1d5430503138ec65c145e869438727e1aa7f3cebf228fba
  languageName: node
  linkType: hard

"mdast-util-frontmatter@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-frontmatter@npm:2.0.1"
  dependencies:
    "@types/mdast": ^4.0.0
    devlop: ^1.0.0
    escape-string-regexp: ^5.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
    micromark-extension-frontmatter: ^2.0.0
  checksum: 86a7c8d9eb183be2621d6d9134b9d33df2a3647e3255f68a9796e2425e25643ffae00a501e36c57d9c10973087b94aa5a2ffd865d33cdd274cc9b88cd2d90a2e
  languageName: node
  linkType: hard

"mdast-util-gfm-autolink-literal@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-gfm-autolink-literal@npm:2.0.1"
  dependencies:
    "@types/mdast": ^4.0.0
    ccount: ^2.0.0
    devlop: ^1.0.0
    mdast-util-find-and-replace: ^3.0.0
    micromark-util-character: ^2.0.0
  checksum: 5630b12e072d7004cb132231c94f667fb5813486779cb0dfb0a196d7ae0e048897a43b0b37e080017adda618ddfcbea1d7bf23c0fa31c87bfc683e0898ea1cfe
  languageName: node
  linkType: hard

"mdast-util-gfm-footnote@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-gfm-footnote@npm:2.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    devlop: ^1.1.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
  checksum: 45d26b40e7a093712e023105791129d76e164e2168d5268e113298a22de30c018162683fb7893cdc04ab246dac0087eed708b2a136d1d18ed2b32b3e0cae4a79
  languageName: node
  linkType: hard

"mdast-util-gfm-strikethrough@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-gfm-strikethrough@npm:2.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: fe9b1d0eba9b791ff9001c008744eafe3dd7a81b085f2bf521595ce4a8e8b1b44764ad9361761ad4533af3e5d913d8ad053abec38172031d9ee32a8ebd1c7dbd
  languageName: node
  linkType: hard

"mdast-util-gfm-table@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-gfm-table@npm:2.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    devlop: ^1.0.0
    markdown-table: ^3.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: 063a627fd0993548fd63ca0c24c437baf91ba7d51d0a38820bd459bc20bf3d13d7365ef8d28dca99176dd5eb26058f7dde51190479c186dfe6af2e11202957c9
  languageName: node
  linkType: hard

"mdast-util-gfm-task-list-item@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdast-util-gfm-task-list-item@npm:2.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    devlop: ^1.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: 37db90c59b15330fc54d790404abf5ef9f2f83e8961c53666fe7de4aab8dd5e6b3c296b6be19797456711a89a27840291d8871ff0438e9b4e15c89d170efe072
  languageName: node
  linkType: hard

"mdast-util-gfm@npm:^3.0.0":
  version: 3.0.0
  resolution: "mdast-util-gfm@npm:3.0.0"
  dependencies:
    mdast-util-from-markdown: ^2.0.0
    mdast-util-gfm-autolink-literal: ^2.0.0
    mdast-util-gfm-footnote: ^2.0.0
    mdast-util-gfm-strikethrough: ^2.0.0
    mdast-util-gfm-table: ^2.0.0
    mdast-util-gfm-task-list-item: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: 62039d2f682ae3821ea1c999454863d31faf94d67eb9b746589c7e136076d7fb35fabc67e02f025c7c26fd7919331a0ee1aabfae24f565d9a6a9ebab3371c626
  languageName: node
  linkType: hard

"mdast-util-math@npm:^3.0.0":
  version: 3.0.0
  resolution: "mdast-util-math@npm:3.0.0"
  dependencies:
    "@types/hast": ^3.0.0
    "@types/mdast": ^4.0.0
    devlop: ^1.0.0
    longest-streak: ^3.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.1.0
    unist-util-remove-position: ^5.0.0
  checksum: dc7dfb14aec2ec143420f2d92f80c5e6d69293d7cfb6b8180e7f411ce4e1314b5cf4a8d3345eefe06ab0ddd95e3c7801c4174b343fd2c26741180ca4dbad5371
  languageName: node
  linkType: hard

"mdast-util-mdx-expression@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-mdx-expression@npm:2.0.1"
  dependencies:
    "@types/estree-jsx": ^1.0.0
    "@types/hast": ^3.0.0
    "@types/mdast": ^4.0.0
    devlop: ^1.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: 6af56b06bde3ab971129db9855dcf0d31806c70b3b052d7a90a5499a366b57ffd0c2efca67d281c448c557298ba7e3e61bd07133733b735440840dd339b28e19
  languageName: node
  linkType: hard

"mdast-util-mdx-jsx@npm:^3.0.0":
  version: 3.2.0
  resolution: "mdast-util-mdx-jsx@npm:3.2.0"
  dependencies:
    "@types/estree-jsx": ^1.0.0
    "@types/hast": ^3.0.0
    "@types/mdast": ^4.0.0
    "@types/unist": ^3.0.0
    ccount: ^2.0.0
    devlop: ^1.1.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
    parse-entities: ^4.0.0
    stringify-entities: ^4.0.0
    unist-util-stringify-position: ^4.0.0
    vfile-message: ^4.0.0
  checksum: 224f5f6ad247f0f2622ee36c82ac7a4c6a60c31850de4056bf95f531bd2f7ec8943ef34dfe8a8375851f65c07e4913c4f33045d703df4ff4d11b2de5a088f7f9
  languageName: node
  linkType: hard

"mdast-util-mdx@npm:^3.0.0":
  version: 3.0.0
  resolution: "mdast-util-mdx@npm:3.0.0"
  dependencies:
    mdast-util-from-markdown: ^2.0.0
    mdast-util-mdx-expression: ^2.0.0
    mdast-util-mdx-jsx: ^3.0.0
    mdast-util-mdxjs-esm: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: e2b007d826fcd49fd57ed03e190753c8b0f7d9eff6c7cb26ba609cde15cd3a472c0cd5e4a1ee3e39a40f14be22fdb57de243e093cea0c064d6f3366cff3e3af2
  languageName: node
  linkType: hard

"mdast-util-mdxjs-esm@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-mdxjs-esm@npm:2.0.1"
  dependencies:
    "@types/estree-jsx": ^1.0.0
    "@types/hast": ^3.0.0
    "@types/mdast": ^4.0.0
    devlop: ^1.0.0
    mdast-util-from-markdown: ^2.0.0
    mdast-util-to-markdown: ^2.0.0
  checksum: 1f9dad04d31d59005332e9157ea9510dc1d03092aadbc607a10475c7eec1c158b475aa0601a3a4f74e13097ca735deb8c2d9d37928ddef25d3029fd7c9e14dc3
  languageName: node
  linkType: hard

"mdast-util-phrasing@npm:^4.0.0":
  version: 4.1.0
  resolution: "mdast-util-phrasing@npm:4.1.0"
  dependencies:
    "@types/mdast": ^4.0.0
    unist-util-is: ^6.0.0
  checksum: 3a97533e8ad104a422f8bebb34b3dde4f17167b8ed3a721cf9263c7416bd3447d2364e6d012a594aada40cac9e949db28a060bb71a982231693609034ed5324e
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^13.0.0":
  version: 13.2.0
  resolution: "mdast-util-to-hast@npm:13.2.0"
  dependencies:
    "@types/hast": ^3.0.0
    "@types/mdast": ^4.0.0
    "@ungap/structured-clone": ^1.0.0
    devlop: ^1.0.0
    micromark-util-sanitize-uri: ^2.0.0
    trim-lines: ^3.0.0
    unist-util-position: ^5.0.0
    unist-util-visit: ^5.0.0
    vfile: ^6.0.0
  checksum: 7e5231ff3d4e35e1421908437577fd5098141f64918ff5cc8a0f7a8a76c5407f7a3ee88d75f7a1f7afb763989c9f357475fa0ba8296c00aaff1e940098fe86a6
  languageName: node
  linkType: hard

"mdast-util-to-markdown@npm:^2.0.0, mdast-util-to-markdown@npm:^2.1.0":
  version: 2.1.2
  resolution: "mdast-util-to-markdown@npm:2.1.2"
  dependencies:
    "@types/mdast": ^4.0.0
    "@types/unist": ^3.0.0
    longest-streak: ^3.0.0
    mdast-util-phrasing: ^4.0.0
    mdast-util-to-string: ^4.0.0
    micromark-util-classify-character: ^2.0.0
    micromark-util-decode-string: ^2.0.0
    unist-util-visit: ^5.0.0
    zwitch: ^2.0.0
  checksum: 288d152bd50c00632e6e01c610bb904a220d1e226c8086c40627877959746f83ab0b872f4150cb7d910198953b1bf756e384ac3fee3e7b0ddb4517f9084c5803
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "mdast-util-to-string@npm:4.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
  checksum: 35489fb5710d58cbc2d6c8b6547df161a3f81e0f28f320dfb3548a9393555daf07c310c0c497708e67ed4dfea4a06e5655799e7d631ca91420c288b4525d6c29
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.28":
  version: 2.0.28
  resolution: "mdn-data@npm:2.0.28"
  checksum: f51d587a6ebe8e426c3376c74ea6df3e19ec8241ed8e2466c9c8a3904d5d04397199ea4f15b8d34d14524b5de926d8724ae85207984be47e165817c26e49e0aa
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.30":
  version: 2.0.30
  resolution: "mdn-data@npm:2.0.30"
  checksum: d6ac5ac7439a1607df44b22738ecf83f48e66a0874e4482d6424a61c52da5cde5750f1d1229b6f5fa1b80a492be89465390da685b11f97d62b8adcc6e88189aa
  languageName: node
  linkType: hard

"mdx-bundler@npm:^10.0.2":
  version: 10.1.0
  resolution: "mdx-bundler@npm:10.1.0"
  dependencies:
    "@babel/runtime": ^7.23.2
    "@esbuild-plugins/node-resolve": ^0.2.2
    "@fal-works/esbuild-plugin-global-externals": ^2.1.2
    "@mdx-js/esbuild": ^3.0.0
    gray-matter: ^4.0.3
    remark-frontmatter: ^5.0.0
    remark-mdx-frontmatter: ^4.0.0
    uuid: ^9.0.1
    vfile: ^6.0.1
  peerDependencies:
    esbuild: 0.*
  checksum: d729e1827ec0dae7241cbebde05ffd50e04a60f6cf7a22ed1c0a27ebc37939365f68ef60da241acbc4fb31c6a3274ad59c3cf9ec98d55469798a6e748792be3f
  languageName: node
  linkType: hard

"mdx-bundler@npm:^10.1.1":
  version: 10.1.1
  resolution: "mdx-bundler@npm:10.1.1"
  dependencies:
    "@babel/runtime": ^7.23.2
    "@esbuild-plugins/node-resolve": ^0.2.2
    "@fal-works/esbuild-plugin-global-externals": ^2.1.2
    "@mdx-js/esbuild": ^3.0.0
    gray-matter: ^4.0.3
    remark-frontmatter: ^5.0.0
    remark-mdx-frontmatter: ^4.0.0
    uuid: ^9.0.1
    vfile: ^6.0.1
  peerDependencies:
    esbuild: 0.*
  checksum: 305e3db8eed7c4f62438ca9fd18d2f3a472fb042e13828d680abc2d567d22242c058827d53e7052a0b85fd93b28660cb139577078cb3cbe30c2c53223bf6523a
  languageName: node
  linkType: hard

"memfs@npm:^4.8.2":
  version: 4.17.0
  resolution: "memfs@npm:4.17.0"
  dependencies:
    "@jsonjoy.com/json-pack": ^1.0.3
    "@jsonjoy.com/util": ^1.3.0
    tree-dump: ^1.0.1
    tslib: ^2.0.0
  checksum: 58d7917e252f30f13e59967a4895c5fc60448df0d58c6844c95255f2ee9db5dcf145190558e84b30fd364db041755d2a1b81668c8c29a31ca8f1bf4f463ddcc1
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"methods@npm:^1.1.1":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 0917ff4041fa8e2f2fda5425a955fe16ca411591fbd123c0d722fcf02b73971ed6f764d85f0a6f547ce49ee0221ce2c19a5fa692157931cecb422984f1dcd13a
  languageName: node
  linkType: hard

"micromark-core-commonmark@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-core-commonmark@npm:2.0.2"
  dependencies:
    decode-named-character-reference: ^1.0.0
    devlop: ^1.0.0
    micromark-factory-destination: ^2.0.0
    micromark-factory-label: ^2.0.0
    micromark-factory-space: ^2.0.0
    micromark-factory-title: ^2.0.0
    micromark-factory-whitespace: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-chunked: ^2.0.0
    micromark-util-classify-character: ^2.0.0
    micromark-util-html-tag-name: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
    micromark-util-resolve-all: ^2.0.0
    micromark-util-subtokenize: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: e49d78429baf72533a02d06ae83e5a24d4d547bc832173547ffbae93c0960a7dbf0d8896058301498fa4297f280070a5a66891e0e6160040d6c5ef9bc5d9cd51
  languageName: node
  linkType: hard

"micromark-extension-frontmatter@npm:^2.0.0":
  version: 2.0.0
  resolution: "micromark-extension-frontmatter@npm:2.0.0"
  dependencies:
    fault: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: f68032df38c00ae47de15b63bcd72515bfcce39de4a9262a3a1ac9c5990f253f8e41bdc65fd17ec4bb3d144c32529ce0829571331e4901a9a413f1a53785d1e8
  languageName: node
  linkType: hard

"micromark-extension-gfm-autolink-literal@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-autolink-literal@npm:2.1.0"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-sanitize-uri: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: e00a570c70c837b9cbbe94b2c23b787f44e781cd19b72f1828e3453abca2a9fb600fa539cdc75229fa3919db384491063645086e02249481e6ff3ec2c18f767c
  languageName: node
  linkType: hard

"micromark-extension-gfm-footnote@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-footnote@npm:2.1.0"
  dependencies:
    devlop: ^1.0.0
    micromark-core-commonmark: ^2.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
    micromark-util-sanitize-uri: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: ac6fb039e98395d37b71ebff7c7a249aef52678b5cf554c89c4f716111d4be62ef99a5d715a5bd5d68fa549778c977d85cb671d1d8506dc8a3a1b46e867ae52f
  languageName: node
  linkType: hard

"micromark-extension-gfm-strikethrough@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-strikethrough@npm:2.1.0"
  dependencies:
    devlop: ^1.0.0
    micromark-util-chunked: ^2.0.0
    micromark-util-classify-character: ^2.0.0
    micromark-util-resolve-all: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: cdb7a38dd6eefb6ceb6792a44a6796b10f951e8e3e45b8579f599f43e7ae26ccd048c0aa7e441b3c29dd0c54656944fe6eb0098de2bc4b5106fbc0a42e9e016c
  languageName: node
  linkType: hard

"micromark-extension-gfm-table@npm:^2.0.0":
  version: 2.1.1
  resolution: "micromark-extension-gfm-table@npm:2.1.1"
  dependencies:
    devlop: ^1.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 16a59c8c2381c8418d9cf36c605abb0b66cfebaad07e09c4c9b113298d13e0c517b652885529fcb74d149afec3f6e8ab065fd27a900073d5ec0a1d8f0c51b593
  languageName: node
  linkType: hard

"micromark-extension-gfm-tagfilter@npm:^2.0.0":
  version: 2.0.0
  resolution: "micromark-extension-gfm-tagfilter@npm:2.0.0"
  dependencies:
    micromark-util-types: ^2.0.0
  checksum: cf21552f4a63592bfd6c96ae5d64a5f22bda4e77814e3f0501bfe80e7a49378ad140f827007f36044666f176b3a0d5fea7c2e8e7973ce4b4579b77789f01ae95
  languageName: node
  linkType: hard

"micromark-extension-gfm-task-list-item@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-extension-gfm-task-list-item@npm:2.1.0"
  dependencies:
    devlop: ^1.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: b1ad86a4e9d68d9ad536d94fb25a5182acbc85cc79318f4a6316034342f6a71d67983cc13f12911d0290fd09b2bda43cdabe8781a2d9cca2ebe0d421e8b2b8a4
  languageName: node
  linkType: hard

"micromark-extension-gfm@npm:^3.0.0":
  version: 3.0.0
  resolution: "micromark-extension-gfm@npm:3.0.0"
  dependencies:
    micromark-extension-gfm-autolink-literal: ^2.0.0
    micromark-extension-gfm-footnote: ^2.0.0
    micromark-extension-gfm-strikethrough: ^2.0.0
    micromark-extension-gfm-table: ^2.0.0
    micromark-extension-gfm-tagfilter: ^2.0.0
    micromark-extension-gfm-task-list-item: ^2.0.0
    micromark-util-combine-extensions: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 2060fa62666a09532d6b3a272d413bc1b25bbb262f921d7402795ac021e1362c8913727e33d7528d5b4ccaf26922ec51208c43f795a702964817bc986de886c9
  languageName: node
  linkType: hard

"micromark-extension-math@npm:^3.0.0":
  version: 3.1.0
  resolution: "micromark-extension-math@npm:3.1.0"
  dependencies:
    "@types/katex": ^0.16.0
    devlop: ^1.0.0
    katex: ^0.16.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 60a9813d456a7bf1ca493b5b9a1f1df3828b5f635fdc72a3b36a0cf1ebded2a9ed12899493d80578a737d1e36e94113da09aed381f99d0103e82467f16989e28
  languageName: node
  linkType: hard

"micromark-extension-mdx-expression@npm:^3.0.0":
  version: 3.0.0
  resolution: "micromark-extension-mdx-expression@npm:3.0.0"
  dependencies:
    "@types/estree": ^1.0.0
    devlop: ^1.0.0
    micromark-factory-mdx-expression: ^2.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-events-to-acorn: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: abd6ba0acdebc03bc0836c51a1ec4ca28e0be86f10420dd8cfbcd6c10dd37cd3f31e7c8b9792e9276e7526748883f4a30d0803d72b6285dae47d4e5348c23a10
  languageName: node
  linkType: hard

"micromark-extension-mdx-jsx@npm:^3.0.0":
  version: 3.0.1
  resolution: "micromark-extension-mdx-jsx@npm:3.0.1"
  dependencies:
    "@types/acorn": ^4.0.0
    "@types/estree": ^1.0.0
    devlop: ^1.0.0
    estree-util-is-identifier-name: ^3.0.0
    micromark-factory-mdx-expression: ^2.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-events-to-acorn: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
    vfile-message: ^4.0.0
  checksum: d1c7e3cb144284b8ab958a7bc67f3e9f8f0de8cb3e4931aa2d46841b318a7e9998f3aa1d5f35e0afc5a57955697a9a2c74a12491e309b139973e91e30089025b
  languageName: node
  linkType: hard

"micromark-extension-mdx-md@npm:^2.0.0":
  version: 2.0.0
  resolution: "micromark-extension-mdx-md@npm:2.0.0"
  dependencies:
    micromark-util-types: ^2.0.0
  checksum: 7daf03372fd7faddf3f0ac87bdb0debb0bb770f33b586f72251e1072b222ceee75400ab6194c0e130dbf1e077369a5b627be6e9130d7a2e9e6b849f0d18ff246
  languageName: node
  linkType: hard

"micromark-extension-mdxjs-esm@npm:^3.0.0":
  version: 3.0.0
  resolution: "micromark-extension-mdxjs-esm@npm:3.0.0"
  dependencies:
    "@types/estree": ^1.0.0
    devlop: ^1.0.0
    micromark-core-commonmark: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-events-to-acorn: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
    unist-util-position-from-estree: ^2.0.0
    vfile-message: ^4.0.0
  checksum: fb33d850200afce567b95c90f2f7d42259bd33eea16154349e4fa77c3ec934f46c8e5c111acea16321dce3d9f85aaa4c49afe8b810e31b34effc11617aeee8f6
  languageName: node
  linkType: hard

"micromark-extension-mdxjs@npm:^3.0.0":
  version: 3.0.0
  resolution: "micromark-extension-mdxjs@npm:3.0.0"
  dependencies:
    acorn: ^8.0.0
    acorn-jsx: ^5.0.0
    micromark-extension-mdx-expression: ^3.0.0
    micromark-extension-mdx-jsx: ^3.0.0
    micromark-extension-mdx-md: ^2.0.0
    micromark-extension-mdxjs-esm: ^3.0.0
    micromark-util-combine-extensions: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 7da6f0fb0e1e0270a2f5ad257e7422cc16e68efa7b8214c63c9d55bc264cb872e9ca4ac9a71b9dfd13daf52e010f730bac316086f4340e4fcc6569ec699915bf
  languageName: node
  linkType: hard

"micromark-factory-destination@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-destination@npm:2.0.1"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 9c4baa9ca2ed43c061bbf40ddd3d85154c2a0f1f485de9dea41d7dd2ad994ebb02034a003b2c1dbe228ba83a0576d591f0e90e0bf978713f84ee7d7f3aa98320
  languageName: node
  linkType: hard

"micromark-factory-label@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-label@npm:2.0.1"
  dependencies:
    devlop: ^1.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: bd03f5a75f27cdbf03b894ddc5c4480fc0763061fecf9eb927d6429233c930394f223969a99472df142d570c831236134de3dc23245d23d9f046f9d0b623b5c2
  languageName: node
  linkType: hard

"micromark-factory-mdx-expression@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-factory-mdx-expression@npm:2.0.2"
  dependencies:
    "@types/estree": ^1.0.0
    devlop: ^1.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-events-to-acorn: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
    unist-util-position-from-estree: ^2.0.0
    vfile-message: ^4.0.0
  checksum: fc4bd9cba0f657093537bff02365f528e8a847f2f20d8d62bb6e21cb343f8179974a9289a198164f88a383d45f403bc29c06749ae5af531c4ce1ab2164090439
  languageName: node
  linkType: hard

"micromark-factory-space@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-space@npm:2.0.1"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 1bd68a017c1a66f4787506660c1e1c5019169aac3b1cb075d49ac5e360e0b2065e984d4e1d6e9e52a9d44000f2fa1c98e66a743d7aae78b4b05616bf3242ed71
  languageName: node
  linkType: hard

"micromark-factory-title@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-title@npm:2.0.1"
  dependencies:
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: b4d2e4850a8ba0dff25ce54e55a3eb0d43dda88a16293f53953153288f9d84bcdfa8ca4606b2cfbb4f132ea79587bbb478a73092a349f893f5264fbcdbce2ee1
  languageName: node
  linkType: hard

"micromark-factory-whitespace@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-whitespace@npm:2.0.1"
  dependencies:
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 67b3944d012a42fee9e10e99178254a04d48af762b54c10a50fcab988688799993efb038daf9f5dbc04001a97b9c1b673fc6f00e6a56997877ab25449f0c8650
  languageName: node
  linkType: hard

"micromark-util-character@npm:^2.0.0":
  version: 2.1.1
  resolution: "micromark-util-character@npm:2.1.1"
  dependencies:
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: e9e409efe4f2596acd44587e8591b722bfc041c1577e8fe0d9c007a4776fb800f9b3637a22862ad2ba9489f4bdf72bb547fce5767dbbfe0a5e6760e2a21c6495
  languageName: node
  linkType: hard

"micromark-util-chunked@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-chunked@npm:2.0.1"
  dependencies:
    micromark-util-symbol: ^2.0.0
  checksum: f8cb2a67bcefe4bd2846d838c97b777101f0043b9f1de4f69baf3e26bb1f9885948444e3c3aec66db7595cad8173bd4567a000eb933576c233d54631f6323fe4
  languageName: node
  linkType: hard

"micromark-util-classify-character@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-classify-character@npm:2.0.1"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 4d8bbe3a6dbf69ac0fc43516866b5bab019fe3f4568edc525d4feaaaf78423fa54e6b6732b5bccbeed924455279a3758ffc9556954aafb903982598a95a02704
  languageName: node
  linkType: hard

"micromark-util-combine-extensions@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-combine-extensions@npm:2.0.1"
  dependencies:
    micromark-util-chunked: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 5d22fb9ee37e8143adfe128a72b50fa09568c2cc553b3c76160486c96dbbb298c5802a177a10a215144a604b381796071b5d35be1f2c2b2ee17995eda92f0c8e
  languageName: node
  linkType: hard

"micromark-util-decode-numeric-character-reference@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-decode-numeric-character-reference@npm:2.0.2"
  dependencies:
    micromark-util-symbol: ^2.0.0
  checksum: ee11c8bde51e250e302050474c4a2adca094bca05c69f6cdd241af12df285c48c88d19ee6e022b9728281c280be16328904adca994605680c43af56019f4b0b6
  languageName: node
  linkType: hard

"micromark-util-decode-string@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-decode-string@npm:2.0.1"
  dependencies:
    decode-named-character-reference: ^1.0.0
    micromark-util-character: ^2.0.0
    micromark-util-decode-numeric-character-reference: ^2.0.0
    micromark-util-symbol: ^2.0.0
  checksum: e9546ae53f9b5a4f9aa6aaf3e750087100d3429485ca80dbacec99ff2bb15a406fa7d93784a0fc2fe05ad7296b9295e75160ef71faec9e90110b7be2ae66241a
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-encode@npm:2.0.1"
  checksum: be890b98e78dd0cdd953a313f4148c4692cc2fb05533e56fef5f421287d3c08feee38ca679f318e740530791fc251bfe8c80efa926fcceb4419b269c9343d226
  languageName: node
  linkType: hard

"micromark-util-events-to-acorn@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-events-to-acorn@npm:2.0.2"
  dependencies:
    "@types/acorn": ^4.0.0
    "@types/estree": ^1.0.0
    "@types/unist": ^3.0.0
    devlop: ^1.0.0
    estree-util-visit: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
    vfile-message: ^4.0.0
  checksum: bcb3eeac52a4ae5c3ca3d8cff514de3a7d1f272d9a94cce26a08c578bef64df4d61820874c01207e92fcace9eae5c9a7ecdddef0c6e10014b255a07b7880bf94
  languageName: node
  linkType: hard

"micromark-util-html-tag-name@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-html-tag-name@npm:2.0.1"
  checksum: dea365f5ad28ad74ff29fcb581f7b74fc1f80271c5141b3b2bc91c454cbb6dfca753f28ae03730d657874fcbd89d0494d0e3965dfdca06d9855f467c576afa9d
  languageName: node
  linkType: hard

"micromark-util-normalize-identifier@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-normalize-identifier@npm:2.0.1"
  dependencies:
    micromark-util-symbol: ^2.0.0
  checksum: 1eb9a289d7da067323df9fdc78bfa90ca3207ad8fd893ca02f3133e973adcb3743b233393d23d95c84ccaf5d220ae7f5a28402a644f135dcd4b8cfa60a7b5f84
  languageName: node
  linkType: hard

"micromark-util-resolve-all@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-resolve-all@npm:2.0.1"
  dependencies:
    micromark-util-types: ^2.0.0
  checksum: 9275f3ddb6c26f254dd2158e66215d050454b279707a7d9ce5a3cd0eba23201021cedcb78ae1a746c1b23227dcc418ee40dd074ade195359506797a5493550cc
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-sanitize-uri@npm:2.0.1"
  dependencies:
    micromark-util-character: ^2.0.0
    micromark-util-encode: ^2.0.0
    micromark-util-symbol: ^2.0.0
  checksum: d01517840c17de67aaa0b0f03bfe05fac8a41d99723cd8ce16c62f6810e99cd3695364a34c335485018e5e2c00e69031744630a1b85c6868aa2f2ca1b36daa2f
  languageName: node
  linkType: hard

"micromark-util-subtokenize@npm:^2.0.0":
  version: 2.0.4
  resolution: "micromark-util-subtokenize@npm:2.0.4"
  dependencies:
    devlop: ^1.0.0
    micromark-util-chunked: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: a084e626773f0750747770226c017a2d31f30fa5bd7c7e9df62755681395fea7d8693eeb81b5f41a15a05db941f2491c37af5c23f05e50d2d9777e4036a2b6bb
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-symbol@npm:2.0.1"
  checksum: fb7346950550bc85a55793dda94a8b3cb3abc068dbd7570d1162db7aee803411d06c0a5de4ae59cd945f46143bdeadd4bba02a02248fa0d18cc577babaa00044
  languageName: node
  linkType: hard

"micromark-util-types@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-types@npm:2.0.1"
  checksum: 630aac466628a360962f478f69421599c53ff8b3080765201b7be3b3a4be7f4c5b73632b9a6dd426b9e06035353c18acccee637d6c43d9b0bf1c31111bbb88a7
  languageName: node
  linkType: hard

"micromark@npm:^4.0.0":
  version: 4.0.1
  resolution: "micromark@npm:4.0.1"
  dependencies:
    "@types/debug": ^4.0.0
    debug: ^4.0.0
    decode-named-character-reference: ^1.0.0
    devlop: ^1.0.0
    micromark-core-commonmark: ^2.0.0
    micromark-factory-space: ^2.0.0
    micromark-util-character: ^2.0.0
    micromark-util-chunked: ^2.0.0
    micromark-util-combine-extensions: ^2.0.0
    micromark-util-decode-numeric-character-reference: ^2.0.0
    micromark-util-encode: ^2.0.0
    micromark-util-normalize-identifier: ^2.0.0
    micromark-util-resolve-all: ^2.0.0
    micromark-util-sanitize-uri: ^2.0.0
    micromark-util-subtokenize: ^2.0.0
    micromark-util-symbol: ^2.0.0
    micromark-util-types: ^2.0.0
  checksum: 83ea084e8bf84442cc70c1207e916df11f0fde0ebd9daf978c895a1466c47a1dd4ed42b21b6e65bcc0d268fcbec24b4b1b28bc59c548940fe690929b8e0e7732
  languageName: node
  linkType: hard

"micromatch@npm:4.0.5":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: ^3.0.2
    picomatch: ^2.3.1
  checksum: 02a17b671c06e8fefeeb6ef996119c1e597c942e632a21ef589154f23898c9c6a9858526246abb14f8bca6e77734aa9dcf65476fca47cedfb80d9577d52843fc
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.5, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mime@npm:^1.4.1":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-fn@npm:4.0.0"
  checksum: 995dcece15ee29aa16e188de6633d43a3db4611bcf93620e7e62109ec41c79c0f34277165b8ce5e361205049766e371851264c21ac64ca35499acb5421c2ba56
  languageName: node
  linkType: hard

"mini-svg-data-uri@npm:^1.2.3":
  version: 1.4.4
  resolution: "mini-svg-data-uri@npm:1.4.4"
  bin:
    mini-svg-data-uri: cli.js
  checksum: 997f1fbd8d59a70f03761e18626d335197a3479cb9d1ff75678e4b64b864d32a0b8fc18115eabde035e5299b8b4a354a78e57dd6ac10f9d604162a6170898d09
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.3, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 7d59a31011ab9e4d1af6562dd4c4440e425b2baf4c5edbdd2e22fb25a88629e1cdceca39953ff209da504a46021df520f18fd9a519f36efae4750ff724ddadea
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: ^7.0.4
    rimraf: ^5.0.5
  checksum: da0a53899252380475240c587e52c824f8998d9720982ba5c4693c68e89230718884a209858c156c6e08d51aad35700a3589987e540593c36f6713fe30cd7338
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"moo@npm:^0.5.1":
  version: 0.5.2
  resolution: "moo@npm:0.5.2"
  checksum: 5a41ddf1059fd0feb674d917c4774e41c877f1ca980253be4d3aae1a37f4bc513f88815041243f36f5cf67a62fb39324f3f997cf7fb17b6cb00767c165e7c499
  languageName: node
  linkType: hard

"mrmime@npm:^2.0.0":
  version: 2.0.0
  resolution: "mrmime@npm:2.0.0"
  checksum: f6fe11ec667c3d96f1ce5fd41184ed491d5f0a5f4045e82446a471ccda5f84c7f7610dff61d378b73d964f73a320bd7f89788f9e6b9403e32cc4be28ba99f569
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6, nanoid@npm:^3.3.8":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: dfe0adbc0c77e9655b550c333075f51bb28cfc7568afbf3237249904f9c86c9aaaed1f113f0fddddba75673ee31c758c30c43d4414f014a52a7a626efc5958c9
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"needle@npm:^2.5.2":
  version: 2.9.1
  resolution: "needle@npm:2.9.1"
  dependencies:
    debug: ^3.2.6
    iconv-lite: ^0.4.4
    sax: ^1.2.4
  bin:
    needle: ./bin/needle
  checksum: 746ae3a3782f0a057ff304a98843cc6f2009f978a0fad0c3e641a9d46d0b5702bb3e197ba08aecd48678067874a991c4f5fc320c7e51a4c041d9dd3441146cf0
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"next-contentlayer2@npm:0.5.5":
  version: 0.5.5
  resolution: "next-contentlayer2@npm:0.5.5"
  dependencies:
    "@contentlayer2/core": 0.5.5
    "@contentlayer2/utils": 0.5.5
  peerDependencies:
    contentlayer2: 0.5.5
    next: ">=12.0.0"
    react: ^18 || ^19 || ^19.0.0-rc
    react-dom: ^18 || ^19 || ^19.0.0-rc
  checksum: aa849d54166b13b3090686e238b7d1b866a457e34405d26bcd0da331f63a8c768273e98d94d1fe4e8aaf530d6d474ac9a03682d82719d01584af076ca4db4716
  languageName: node
  linkType: hard

"next-contentlayer2@npm:^0.5.3":
  version: 0.5.4
  resolution: "next-contentlayer2@npm:0.5.4"
  dependencies:
    "@contentlayer2/core": 0.5.4
    "@contentlayer2/utils": 0.5.4
  peerDependencies:
    contentlayer2: 0.5.4
    next: ">=12.0.0"
    react: ^18 || ^19 || ^19.0.0-rc
    react-dom: ^18 || ^19 || ^19.0.0-rc
  checksum: 69be76af50933fe98da5a3904f4f46f27b842ebd3834dcd3b27d94dd5cb28af3f1395dd0925918f3da89ced9f19d2e8e254011327a8dbdd6a26c53f47f135bde
  languageName: node
  linkType: hard

"next-themes@npm:^0.3.0":
  version: 0.3.0
  resolution: "next-themes@npm:0.3.0"
  peerDependencies:
    react: ^16.8 || ^17 || ^18
    react-dom: ^16.8 || ^17 || ^18
  checksum: 4285c4969eac517ad7addd773bcb71e7d14bc6c6e3b24eb97b80a6e06ac03fb6cb345e75dfb448156d14430d06289948eb8cfdeb52402ca7ce786093d01d2878
  languageName: node
  linkType: hard

"next-themes@npm:^0.4.6":
  version: 0.4.6
  resolution: "next-themes@npm:0.4.6"
  peerDependencies:
    react: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
    react-dom: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
  checksum: 295256ee5689629e6578ff4f10f163257172085cac0a250f5f81eae5791fde984a0398be09b959a90c12cf72cf95e82ae11fad6fe13edc4e2d2a6ec2734be0cd
  languageName: node
  linkType: hard

"next@npm:15.2.4":
  version: 15.2.4
  resolution: "next@npm:15.2.4"
  dependencies:
    "@next/env": 15.2.4
    "@next/swc-darwin-arm64": 15.2.4
    "@next/swc-darwin-x64": 15.2.4
    "@next/swc-linux-arm64-gnu": 15.2.4
    "@next/swc-linux-arm64-musl": 15.2.4
    "@next/swc-linux-x64-gnu": 15.2.4
    "@next/swc-linux-x64-musl": 15.2.4
    "@next/swc-win32-arm64-msvc": 15.2.4
    "@next/swc-win32-x64-msvc": 15.2.4
    "@swc/counter": 0.1.3
    "@swc/helpers": 0.5.15
    busboy: 1.6.0
    caniuse-lite: ^1.0.30001579
    postcss: 8.4.31
    sharp: ^0.33.5
    styled-jsx: 5.1.6
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    "@playwright/test": ^1.41.2
    babel-plugin-react-compiler: "*"
    react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
    sharp:
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    "@playwright/test":
      optional: true
    babel-plugin-react-compiler:
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: a235fbe6a77e7b81ea8d9372a85ce02a1e1ecc5666b19f60b071acaa3135bf42fa4a29ad616257e3a2e60fadb9400cb4041da0e8217d46c76d7703c197896bd4
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: ^2.0.2
    tslib: ^2.0.3
  checksum: 0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.1, node-fetch@npm:^2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: d76d2f5edb451a3f05b15115ec89fc6be39de37c6089f1b6368df03b91e1633fd379a7e01b7ab05089a25034b2023d959b47e59759cb38d88341b2459e89d6e5
  languageName: node
  linkType: hard

"node-fetch@npm:~2.6.1":
  version: 2.6.13
  resolution: "node-fetch@npm:2.6.13"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 055845ae5b4796c78c7053564745345025cf959563b3568b43c385f67d311779e6b00e5fef6ed1b79f86ba4048e4b4b722e1aa948305521b9353eb7e788912c9
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: d7d5055ccc88177f721c7cd4f8f9440c29a0eb40e7b79dba89ef882ec957975dfc1dcb8225e79ab32481a02016eb13bbc051a913ea88d482d3cbdf2131156af4
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 917dbced519f48c6289a44830a0ca6dc944c3ee9243c468ebd8515a41c97c8b2c256edb7f3f750416bc37952cc9608684e6483c7b6c6f39f6bd8d86c52cfe658
  languageName: node
  linkType: hard

"noms@npm:0.0.0":
  version: 0.0.0
  resolution: "noms@npm:0.0.0"
  dependencies:
    inherits: ^2.0.1
    readable-stream: ~1.0.31
  checksum: a05f056dabf764c86472b6b5aad10455f3adcb6971f366cdf36a72b559b29310a940e316bca30802f2804fdd41707941366224f4cba80c4f53071512245bf200
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: ^3.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 49cfd3eb6f565e292bf61f2ff1373a457238804d5a5a63a8d786c923007498cba89f3648e3b952bc10203e3e7285752abf5b14eaf012edb821e84f24e881a92a
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"npm-run-path@npm:^5.1.0":
  version: 5.3.0
  resolution: "npm-run-path@npm:5.3.0"
  dependencies:
    path-key: ^4.0.0
  checksum: ae8e7a89da9594fb9c308f6555c73f618152340dcaae423e5fb3620026fefbec463618a8b761920382d666fa7a2d8d240b6fe320e8a6cdd54dc3687e2b659d25
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.0, nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 582810c6a8d2ef988ea0a39e69e115a138dad8f42dd445383b394877e5816eb4268489f316a6f74ee9c4e0a984b3eab1028e3e79d62b1ed67c726661d55c7a8b
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
    has-symbols: ^1.1.0
    object-keys: ^1.1.1
  checksum: 60e07d2651cf4f5528c485f1aa4dbded9b384c47d80e8187cefd11320abb1aebebf78df5483451dfa549059f8281c21f7b4bf7d19e9e5e97d8d617df0df298de
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.8":
  version: 1.1.8
  resolution: "object.entries@npm:1.1.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: 5314877cb637ef3437a30bba61d9bacdb3ce74bf73ac101518be0633c37840c8cc67407edb341f766e8093b3d7516d5c3358f25adfee4a2c697c0ec4c8491907
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
  checksum: 29b2207a2db2782d7ced83f93b3ff5d425f901945f3665ffda1821e30a7253cd1fd6b891a64279976098137ddfa883d748787a6fea53ecdb51f8df8b8cec0ae1
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
  checksum: 0d30693ca3ace29720bffd20b3130451dca7a56c612e1926c0a1a15e4306061d84410bdb1456be2656c5aca53c81b7a3661eceaa362db1bba6669c2c9b6d1982
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.0, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: f9b9a2a125ccf8ded29414d7c056ae0d187b833ee74919821fc60d7e216626db220d9cb3cf33f965c84aaaa96133626ca13b80f3c158b673976dc8cfcfcd26bb
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"onetime@npm:^6.0.0":
  version: 6.0.0
  resolution: "onetime@npm:6.0.0"
  dependencies:
    mimic-fn: ^4.0.0
  checksum: 0846ce78e440841335d4e9182ef69d5762e9f38aa7499b19f42ea1c4cd40f0b4446094c455c713f9adac3f4ae86f613bb5e30c99e52652764d06a89f709b3788
  languageName: node
  linkType: hard

"oo-ascii-tree@npm:^1.94.0":
  version: 1.106.0
  resolution: "oo-ascii-tree@npm:1.106.0"
  checksum: e5a8a16ee074bcce90cb890d97d8b3aafb4e1200dc42138b7f57e5a5f7d9567bc57ecef28d87c91248b39bf966f043a39ec4e5055eac3d7c1b09c4569b350f7a
  languageName: node
  linkType: hard

"opener@npm:^1.5.2":
  version: 1.5.2
  resolution: "opener@npm:1.5.2"
  bin:
    opener: bin/opener-bin.js
  checksum: 33b620c0d53d5b883f2abc6687dd1c5fd394d270dbe33a6356f2d71e0a2ec85b100d5bac94694198ccf5c30d592da863b2292c5539009c715a9c80c697b4f6cc
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.2.6
    object-keys: ^1.1.1
    safe-push-apply: ^1.0.0
  checksum: cc9dd7d85c4ccfbe8109fce307d581ac7ede7b26de892b537873fbce2dc6a206d89aea0630dbb98e47ce0873517cefeaa7be15fcf94aaf4764a3b34b474a5b61
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-entities@npm:^4.0.0":
  version: 4.0.2
  resolution: "parse-entities@npm:4.0.2"
  dependencies:
    "@types/unist": ^2.0.0
    character-entities-legacy: ^3.0.0
    character-reference-invalid: ^2.0.0
    decode-named-character-reference: ^1.0.0
    is-alphanumerical: ^2.0.0
    is-decimal: ^2.0.0
    is-hexadecimal: ^2.0.0
  checksum: db22b46da1a62af00409c929ac49fbd306b5ebf0dbacf4646d2ae2b58616ef90a40eedc282568a3cf740fac2a7928bc97146973a628f6977ca274dedc2ad6edc
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse-numeric-range@npm:^1.3.0":
  version: 1.3.0
  resolution: "parse-numeric-range@npm:1.3.0"
  checksum: 289ca126d5b8ace7325b199218de198014f58ea6895ccc88a5247491d07f0143bf047f80b4a31784f1ca8911762278d7d6ecb90a31dfae31da91cc1a2524c8ce
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0, parse5@npm:^7.1.2":
  version: 7.2.1
  resolution: "parse5@npm:7.2.1"
  dependencies:
    entities: ^4.5.0
  checksum: 11253cf8aa2e7fc41c004c64cba6f2c255f809663365db65bd7ad0e8cf7b89e436a563c20059346371cc543a6c1b567032088883ca6a2cbc88276c666b68236d
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: ba98bfd595fc91ef3d30f4243b1aee2f6ec41c53b4546bfa3039487c367abaa182471dcfc830a1f9e1a0df00c14a370514fa2b3a1aacc68b15a460c31116873e
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 8e6c314ae6d16b83e93032c61020129f6f4484590a777eed709c4a01b50e498822b00f76ceaf94bc64dbd90b327df56ceadce27da3d83393790f1219e07721d7
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pidtree@npm:0.6.0":
  version: 0.6.0
  resolution: "pidtree@npm:0.6.0"
  bin:
    pidtree: bin/pidtree.js
  checksum: 8fbc073ede9209dd15e80d616e65eb674986c93be49f42d9ddde8dbbd141bb53d628a7ca4e58ab5c370bb00383f67d75df59a9a226dede8fa801267a7030c27a
  languageName: node
  linkType: hard

"pliny@npm:0.4.1":
  version: 0.4.1
  resolution: "pliny@npm:0.4.1"
  dependencies:
    "@docsearch/react": ^3.6.2
    "@giscus/react": ^3.0.0
    "@mailchimp/mailchimp_marketing": ^3.0.80
    contentlayer2: ^0.5.3
    copyfiles: ^2.4.1
    github-slugger: ^2.0.0
    js-yaml: 4.1.0
    kbar: 0.1.0-beta.45
    next-contentlayer2: ^0.5.3
    next-themes: ^0.3.0
    probe-image-size: ^7.2.3
    remark: ^15.0.0
    unist-util-visit: ^5.0.0
  peerDependencies:
    next: ">=13.0.0"
    react: ^17.0.2 || ^18 || ^19 || ^19.0.0-rc
    react-dom: ^17.0.2 || ^18 || ^19 || ^19.0.0-rc
  checksum: 0400965974c7b1c1c5d96d5541978aec43c6137874b51e2dd59f1c8f688150c53158fefa8a2d17e35b8e16d9827e70aa86afc8ab6374f230aa27636240a2d9d9
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: cfcd4f05264eee8fd184cd4897a17890561d1d473434b43ab66ad3673d9c9128981ec01e0cb1d65a52cd6b1eebfb2eae1e53e39b2e0eca86afc823ede7a4f41b
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:6.0.10":
  version: 6.0.10
  resolution: "postcss-selector-parser@npm:6.0.10"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: 46afaa60e3d1998bd7adf6caa374baf857cc58d3ff944e29459c9a9e4680a7fe41597bd5b755fc81d7c388357e9bf67c0251d047c640a09f148e13606b8a8608
  languageName: node
  linkType: hard

"postcss@npm:8.4.31":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: 1d8611341b073143ad90486fcdfeab49edd243377b1f51834dc4f6d028e82ce5190e4f11bb2633276864503654fb7cab28e67abdc0fbf9d1f88cad4a0ff0beea
  languageName: node
  linkType: hard

"postcss@npm:^8.4.24, postcss@npm:^8.4.41":
  version: 8.5.1
  resolution: "postcss@npm:8.5.1"
  dependencies:
    nanoid: ^3.3.8
    picocolors: ^1.1.1
    source-map-js: ^1.2.1
  checksum: cfdcfcd019fca78160341080ba8986cf80cd6e9ca327ba61b86c03e95043e9bce56ad2e018851858039fd7264781797360bfba718dd216b17b3cd803a5134f2f
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: ^1.1.2
  checksum: 00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier-plugin-tailwindcss@npm:^0.6.11":
  version: 0.6.11
  resolution: "prettier-plugin-tailwindcss@npm:0.6.11"
  peerDependencies:
    "@ianvs/prettier-plugin-sort-imports": "*"
    "@prettier/plugin-pug": "*"
    "@shopify/prettier-plugin-liquid": "*"
    "@trivago/prettier-plugin-sort-imports": "*"
    "@zackad/prettier-plugin-twig": "*"
    prettier: ^3.0
    prettier-plugin-astro: "*"
    prettier-plugin-css-order: "*"
    prettier-plugin-import-sort: "*"
    prettier-plugin-jsdoc: "*"
    prettier-plugin-marko: "*"
    prettier-plugin-multiline-arrays: "*"
    prettier-plugin-organize-attributes: "*"
    prettier-plugin-organize-imports: "*"
    prettier-plugin-sort-imports: "*"
    prettier-plugin-style-order: "*"
    prettier-plugin-svelte: "*"
  peerDependenciesMeta:
    "@ianvs/prettier-plugin-sort-imports":
      optional: true
    "@prettier/plugin-pug":
      optional: true
    "@shopify/prettier-plugin-liquid":
      optional: true
    "@trivago/prettier-plugin-sort-imports":
      optional: true
    "@zackad/prettier-plugin-twig":
      optional: true
    prettier-plugin-astro:
      optional: true
    prettier-plugin-css-order:
      optional: true
    prettier-plugin-import-sort:
      optional: true
    prettier-plugin-jsdoc:
      optional: true
    prettier-plugin-marko:
      optional: true
    prettier-plugin-multiline-arrays:
      optional: true
    prettier-plugin-organize-attributes:
      optional: true
    prettier-plugin-organize-imports:
      optional: true
    prettier-plugin-sort-imports:
      optional: true
    prettier-plugin-style-order:
      optional: true
    prettier-plugin-svelte:
      optional: true
  checksum: b626a09248e94d39b0ac26fe26323503faaf11aeae9a741b8a93ed65ee27ac12eadc00fa8f7113a0c54f88df59aa0136e4efb830d47ab204808a21b16e7d9b84
  languageName: node
  linkType: hard

"prettier@npm:^3.0.0":
  version: 3.4.2
  resolution: "prettier@npm:3.4.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 061c84513db62d3944c8dc8df36584dad82883ce4e49efcdbedd8703dce5b173c33fd9d2a4e1725d642a3b713c932b55418342eaa347479bc4a9cca114a04cd0
  languageName: node
  linkType: hard

"probe-image-size@npm:^7.2.3":
  version: 7.2.3
  resolution: "probe-image-size@npm:7.2.3"
  dependencies:
    lodash.merge: ^4.6.2
    needle: ^2.5.2
    stream-parser: ~0.3.1
  checksum: 1a5eeb8f5cb979172144a5d7a017c70fcd664ccc8af9ad3a803903ee81864abea4036adae4fc6e66e9ae21bd3ce0febefaf1f32e65a77ff226b2eb61e9e4978c
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"property-information@npm:^6.0.0":
  version: 6.5.0
  resolution: "property-information@npm:6.5.0"
  checksum: 6e55664e2f64083b715011e5bafaa1e694faf36986c235b0907e95d09259cc37c38382e3cc94a4c3f56366e05336443db12c8a0f0968a8c0a1b1416eebfc8f53
  languageName: node
  linkType: hard

"protobufjs@npm:^7.2.5, protobufjs@npm:^7.3.0":
  version: 7.4.0
  resolution: "protobufjs@npm:7.4.0"
  dependencies:
    "@protobufjs/aspromise": ^1.1.2
    "@protobufjs/base64": ^1.1.2
    "@protobufjs/codegen": ^2.0.4
    "@protobufjs/eventemitter": ^1.1.0
    "@protobufjs/fetch": ^1.1.0
    "@protobufjs/float": ^1.0.2
    "@protobufjs/inquire": ^1.1.0
    "@protobufjs/path": ^1.1.2
    "@protobufjs/pool": ^1.1.0
    "@protobufjs/utf8": ^1.1.0
    "@types/node": ">=13.7.0"
    long: ^5.0.0
  checksum: ba0e6b60541bbf818bb148e90f5eb68bd99004e29a6034ad9895a381cbd352be8dce5376e47ae21b2e05559f2505b4a5f4a3c8fa62402822c6ab4dcdfb89ffb3
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"qs@npm:^6.5.1":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: ^1.1.0
  checksum: 189b52ad4e9a0da1a16aff4c58b2a554a8dad9bd7e287c7da7446059b49ca2e33a49e570480e8be406b87fccebf134f51c373cbce36c8c83859efa0c9b71d635
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"react-dom@npm:19.0.0":
  version: 19.0.0
  resolution: "react-dom@npm:19.0.0"
  dependencies:
    scheduler: ^0.25.0
  peerDependencies:
    react: ^19.0.0
  checksum: 009cc6e575263a0d1906f9dd4aa6532d2d3d0d71e4c2b7777c8fe4de585fa06b5b77cdc2e0fbaa2f3a4a5e5d3305c189ba152153f358ee7da4d9d9ba5d3a8975
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-virtual@npm:^2.8.2":
  version: 2.10.4
  resolution: "react-virtual@npm:2.10.4"
  dependencies:
    "@reach/observe-rect": ^1.1.0
  peerDependencies:
    react: ^16.6.3 || ^17.0.0
  checksum: 1bebc741b01057829a7d7f29256114caecf0597d41b187cb41e75af77f24a87c780bc1a81ec11205b78ee2e9c801fc5e36b20a9e1ab7ddc70a18dd95417795f8
  languageName: node
  linkType: hard

"react@npm:19.0.0":
  version: 19.0.0
  resolution: "react@npm:19.0.0"
  checksum: 86de15d85b2465feb40297a90319c325cb07cf27191a361d47bcfe8c6126c973d660125aa67b8f4cbbe39f15a2f32efd0c814e98196d8e5b68c567ba40a399c6
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.5, readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readable-stream@npm:~1.0.31":
  version: 1.0.34
  resolution: "readable-stream@npm:1.0.34"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.1
    isarray: 0.0.1
    string_decoder: ~0.10.x
  checksum: 85042c537e4f067daa1448a7e257a201070bfec3dd2706abdbd8ebc7f3418eb4d3ed4b8e5af63e2544d69f88ab09c28d5da3c0b77dc76185fddd189a59863b60
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"reading-time@npm:1.5.0":
  version: 1.5.0
  resolution: "reading-time@npm:1.5.0"
  checksum: e27bc5a70ba0f4ac337896b18531b914d38f4bee67cbad48029d0c11dd0a7a847b2a6bba895ab7ce2ad3e7ecb86912bdc477d8fa2d48405a3deda964be54d09b
  languageName: node
  linkType: hard

"recma-build-jsx@npm:^1.0.0":
  version: 1.0.0
  resolution: "recma-build-jsx@npm:1.0.0"
  dependencies:
    "@types/estree": ^1.0.0
    estree-util-build-jsx: ^3.0.0
    vfile: ^6.0.0
  checksum: ba82fe08efdf5ecd178ab76a08a4acac792a41d9f38aea99f93cb3d9e577ba8952620c547e730ba6717c13efa08fdb3dfe893bccfa9717f5a81d3fb2ab20c572
  languageName: node
  linkType: hard

"recma-jsx@npm:^1.0.0":
  version: 1.0.0
  resolution: "recma-jsx@npm:1.0.0"
  dependencies:
    acorn-jsx: ^5.0.0
    estree-util-to-js: ^2.0.0
    recma-parse: ^1.0.0
    recma-stringify: ^1.0.0
    unified: ^11.0.0
  checksum: bc7e3f744e82c9826ddf6fdf8933351b59f0663409a51abe0f3179380584b732f981c16e15c653e60c1e1cc366d4eb9b38e37832a241ec2247062997846e7eef
  languageName: node
  linkType: hard

"recma-parse@npm:^1.0.0":
  version: 1.0.0
  resolution: "recma-parse@npm:1.0.0"
  dependencies:
    "@types/estree": ^1.0.0
    esast-util-from-js: ^2.0.0
    unified: ^11.0.0
    vfile: ^6.0.0
  checksum: 676b2097a63ba444985a61af51c2628a546a2537a9ca036ed2249a627fb096f3373139765388b60164e6f5a50c819146a3660351e3f993a360ef107f2ab1c6f8
  languageName: node
  linkType: hard

"recma-stringify@npm:^1.0.0":
  version: 1.0.0
  resolution: "recma-stringify@npm:1.0.0"
  dependencies:
    "@types/estree": ^1.0.0
    estree-util-to-js: ^2.0.0
    unified: ^11.0.0
    vfile: ^6.0.0
  checksum: 3a4f80fe0f6bc11fefa71782dfedb43c28b42518dea450cd1b1591057d9d570f83c85d645bf5ed6da2e47de15a021172c076a8ff4675799855d9f9436cec3c82
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.9
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.7
    get-proto: ^1.0.1
    which-builtin-type: ^1.2.1
  checksum: ccc5debeb66125e276ae73909cecb27e47c35d9bb79d9cc8d8d055f008c58010ab8cb401299786e505e4aab733a64cba9daf5f312a58e96a43df66adad221870
  languageName: node
  linkType: hard

"refractor@npm:^4.8.0":
  version: 4.8.1
  resolution: "refractor@npm:4.8.1"
  dependencies:
    "@types/hast": ^2.0.0
    "@types/prismjs": ^1.0.0
    hastscript: ^7.0.0
    parse-entities: ^4.0.0
  checksum: 51762ed1d62523e3fb4b1ccec3f846965d497b7828e43d1668b2839dcbbe6b0d4edfd9113ad6e3679e5a6b6fb2f6983883123d762a7e46e3b5a8cd480a0a1930
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: ^1.4.2
  checksum: d5c5fc13f8b8d7e16e791637a4bfef741f8d70e267d51845ee7d5404a32fa14c75b181c4efba33e4bff8b0000a2f13e9773593713dfe5b66597df4259275ce63
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 9f57c93277b5585d3c83b0cf76be47b473ae8c6d9142a46ce8b0291a04bb2cf902059f0f8445dcabb3fb7378e5fe4bb4ea1e008876343d42e46d3b484534ce38
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.15.2":
  version: 0.15.2
  resolution: "regenerator-transform@npm:0.15.2"
  dependencies:
    "@babel/runtime": ^7.8.4
  checksum: 20b6f9377d65954980fe044cfdd160de98df415b4bff38fbade67b3337efaf078308c4fed943067cd759827cc8cfeca9cb28ccda1f08333b85d6a2acbd022c27
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    get-proto: ^1.0.1
    gopd: ^1.2.0
    set-function-name: ^2.0.2
  checksum: 18cb667e56cb328d2dda569d7f04e3ea78f2683135b866d606538cf7b1d4271f7f749f09608c877527799e6cf350e531368f3c7a20ccd1bb41048a48926bdeeb
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.2.0
    regjsgen: ^0.8.0
    regjsparser: ^0.12.0
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.1.0
  checksum: 67d3c4a3f6c99bc80b5d690074a27e6f675be1c1739f8a9acf028fbc36f1a468472574ea65e331e217995198ba4404d7878f3cb3739a73552dd3c70d3fb7f8e6
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: a1d925ff14a4b2be774e45775ee6b33b256f89c42d480e6d85152d2133f18bd3d6af662161b226fa57466f7efec367eaf7ccd2a58c0ec2a1306667ba2ad07b0d
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: ~3.0.2
  bin:
    regjsparser: bin/parser
  checksum: 094b55b0ab3e1fd58f8ce5132a1d44dab08d91f7b0eea4132b0157b303ebb8ded20a9cbd893d25402d2aeddb23fac1f428ab4947b295d6fa51dd1c334a9e76f0
  languageName: node
  linkType: hard

"rehype-autolink-headings@npm:^7.1.0":
  version: 7.1.0
  resolution: "rehype-autolink-headings@npm:7.1.0"
  dependencies:
    "@types/hast": ^3.0.0
    "@ungap/structured-clone": ^1.0.0
    hast-util-heading-rank: ^3.0.0
    hast-util-is-element: ^3.0.0
    unified: ^11.0.0
    unist-util-visit: ^5.0.0
  checksum: 546fb14ffac628a4bfa8cf7549a2b94d921e392393441d602154ca3131549737bf21af6fddcd15abb4f50272ca9a90dad8bd66e7122e58bea85e81e79dce793a
  languageName: node
  linkType: hard

"rehype-citation@npm:^2.3.0":
  version: 2.3.0
  resolution: "rehype-citation@npm:2.3.0"
  dependencies:
    "@citation-js/core": ^0.7.14
    "@citation-js/date": ^0.5.1
    "@citation-js/name": ^0.4.2
    "@citation-js/plugin-bibjson": ^0.7.14
    "@citation-js/plugin-bibtex": ^0.7.14
    "@citation-js/plugin-csl": ^0.7.14
    citeproc: ^2.4.63
    cross-fetch: ^4.0.0
    hast-util-from-dom: ^5.0.0
    hast-util-from-parse5: ^8.0.1
    js-yaml: ^4.1.0
    parse5: ^7.1.2
    unified: ^11.0.0
    unist-util-visit: ^5.0.0
  checksum: 21ae35baee07cb476cb5400395bfb05f189d7195daa778c7a357f06bed132ea939fabc40628a2b54350b4896d08d8a1e3c03acd0db3eead4573af8cb3d9f7369
  languageName: node
  linkType: hard

"rehype-katex-notranslate@npm:^1.1.4":
  version: 1.1.4
  resolution: "rehype-katex-notranslate@npm:1.1.4"
  dependencies:
    unified: ^11.0.5
    unist-util-visit: ^5.0.0
  checksum: e43de356b695e0c7fa9d806f18c4447a976d0c7b9b11b7a70ac392b83566cc92622d3f73f408d3c923fcb159a5467d800db311816c436407b25208e16d27714d
  languageName: node
  linkType: hard

"rehype-katex@npm:^7.0.0":
  version: 7.0.1
  resolution: "rehype-katex@npm:7.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    "@types/katex": ^0.16.0
    hast-util-from-html-isomorphic: ^2.0.0
    hast-util-to-text: ^4.0.0
    katex: ^0.16.0
    unist-util-visit-parents: ^6.0.0
    vfile: ^6.0.0
  checksum: d8f90f2b481fcf8a922410b812dbaed253a488bf61f4981b7b37d4983845060c1fcefb1690063e31ecfb941fb6ef6858e1a044e2f8c6146687266df1f6423a7b
  languageName: node
  linkType: hard

"rehype-minify-attribute-whitespace@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-minify-attribute-whitespace@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    collapse-white-space: ^2.0.0
    hast-util-is-element: ^3.0.0
    hast-util-is-event-handler: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: ae0faf9568b94c6604dc056c7d919ec51568026dacab6fdb9beff0d6da783c596cd8a7cae2d1fcc12a7c68d79bc2874fd1fafe9af1c8b0fb0f8fbb057c5ebea2
  languageName: node
  linkType: hard

"rehype-minify-css-style@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-minify-css-style@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    clean-css: ^5.0.0
    hast-util-from-string: ^3.0.0
    hast-util-is-css-style: ^3.0.0
    hast-util-to-string: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: e01fd5bf233b6d31648e9827a7e852934bf97cd1c4be40ed45cc84766460057af0f44485460194e90ca260ea3c053e85a25b0d64767d8fefe37b44f155708772
  languageName: node
  linkType: hard

"rehype-minify-enumerated-attribute@npm:^5.0.0":
  version: 5.0.1
  resolution: "rehype-minify-enumerated-attribute@npm:5.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-select: ^6.0.0
    html-enumerated-attributes: ^1.0.0
    property-information: ^6.0.0
    space-separated-tokens: ^2.0.0
    unist-util-visit: ^5.0.0
  checksum: bf1a60b5841a765ba0af18952cf2116059134f35d214d1d3a83f18479ef741364c9360237b582b74e3d676c06ae2661b96723e27451061893ccfbb1374cebe76
  languageName: node
  linkType: hard

"rehype-minify-event-handler@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-minify-event-handler@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-event-handler: ^3.0.0
    uglify-js: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 3262e27706e49d2e47746e5ac19b9e70403ad80e2e708518671a6f531635406514c0b49bbcc6b1c856139a6c92488d1908f081ad13ed4534d5883f5663c5a7f5
  languageName: node
  linkType: hard

"rehype-minify-javascript-script@npm:^5.0.0":
  version: 5.0.1
  resolution: "rehype-minify-javascript-script@npm:5.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-from-string: ^3.0.0
    hast-util-is-javascript: ^3.0.0
    hast-util-to-string: ^3.0.0
    uglify-js: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 95f2b287a1b1bf99fdfacdefc01450340d4edf0e9622e8cd0ccc883157d7fdea7fea56337af51efd2e92c498dfd2dd3cc68e8ff1d63955d9f381e39a9d46c20f
  languageName: node
  linkType: hard

"rehype-minify-javascript-url@npm:^5.0.0":
  version: 5.0.1
  resolution: "rehype-minify-javascript-url@npm:5.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-element: ^3.0.0
    html-url-attributes: ^3.0.0
    uglify-js: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: ad47705cd1fea700fa3001cfc6f394d39cac270b0843c46854cfb65c85600df0c8f9df43602949bbb785ec981434d8a2b7bc9c2e56689103373c2926e4c5dfa9
  languageName: node
  linkType: hard

"rehype-minify-json-script@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-minify-json-script@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-from-string: ^3.0.0
    hast-util-to-string: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: f2fe4c9d5c8eabeb18676be4a9fcd005a8e86b5ac50b02ae18188a217e85a8ccd8296de88262cba97769aba9734dacb54005c2b222782ba365608e9364b0a48b
  languageName: node
  linkType: hard

"rehype-minify-language@npm:^3.0.0":
  version: 3.0.1
  resolution: "rehype-minify-language@npm:3.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    bcp-47-normalize: ^2.0.0
    unist-util-visit: ^5.0.0
  checksum: c86a247f95c5807af63d62aa6e62cb35d88505fb3218da4568f0a71579cff356de437475368336c770634b128de9e9eb79308a463fc17f5fd5e6dfa2c25841d7
  languageName: node
  linkType: hard

"rehype-minify-media-attribute@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-minify-media-attribute@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    clean-css: ^5.0.0
    unist-util-visit: ^5.0.0
  checksum: 73df5199853d7294c7be931efe499ea7902802476bed355684604faa71cac842f70d9c502caf325127a096dc4fb1a21cf58c4d6dbba0c96dc97bbf4118708f0f
  languageName: node
  linkType: hard

"rehype-minify-meta-color@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-minify-meta-color@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    clean-css: ^5.0.0
    unist-util-visit: ^5.0.0
  checksum: 68541f58ac57da5e742ebb46e7664a751ac12def510e33621a39c36848580397103b753232d001dcdc24c7a4440d8155c34044dd0598ef7ef1182f61bc41a27a
  languageName: node
  linkType: hard

"rehype-minify-meta-content@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-minify-meta-content@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    comma-separated-tokens: ^2.0.0
    unist-util-visit: ^5.0.0
  checksum: 1b1e8fed59eab99c2086049c225c6a869f61591940ee83a28be6cde849bd69d8053ff9ef6af99e1122b679d677b41981979eecbb9df8acbbc62bc85006f61309
  languageName: node
  linkType: hard

"rehype-minify-style-attribute@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-minify-style-attribute@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    clean-css: ^5.0.0
    unist-util-visit: ^5.0.0
  checksum: 7bb0df5d0468eeba1df1586807842f91288745b72ba6a16e16872cf550b678d0933447d531664b9875d8330cf9c4092ccd06915eb1732d15e0c4cd4c21985e7b
  languageName: node
  linkType: hard

"rehype-minify-whitespace@npm:^6.0.0":
  version: 6.0.2
  resolution: "rehype-minify-whitespace@npm:6.0.2"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-minify-whitespace: ^1.0.0
  checksum: 3ebceb08af1ce08dab68860ecbf56dc804ede3200a4757e1681c967e8390cbf615e662862a92154c565c3841f847425fcc0fdce8ecd46696c58eb55facfce7f1
  languageName: node
  linkType: hard

"rehype-normalize-attribute-value-case@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-normalize-attribute-value-case@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-element: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 8aa24c71179bed95d1a7d7ba9b2a0b72ea6bd150403d1d979ad5fceeebee706cb902418111e4f9dc7de4c079d7e94551e2ec202b777fc6170f38ff4d657d1691
  languageName: node
  linkType: hard

"rehype-parse@npm:^9.0.0":
  version: 9.0.1
  resolution: "rehype-parse@npm:9.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-from-html: ^2.0.0
    unified: ^11.0.0
  checksum: 3175c8d352ca2c8bddb4749fa1bd21336cab27b06165f6db77678e944479ac4fdee729ced8956bf303ba8ff01a7e8d0666e8123980cfbce28a7b4527b2167717
  languageName: node
  linkType: hard

"rehype-preset-minify@npm:7.0.0":
  version: 7.0.0
  resolution: "rehype-preset-minify@npm:7.0.0"
  dependencies:
    rehype-minify-attribute-whitespace: ^4.0.0
    rehype-minify-css-style: ^4.0.0
    rehype-minify-enumerated-attribute: ^5.0.0
    rehype-minify-event-handler: ^4.0.0
    rehype-minify-javascript-script: ^5.0.0
    rehype-minify-javascript-url: ^5.0.0
    rehype-minify-json-script: ^4.0.0
    rehype-minify-language: ^3.0.0
    rehype-minify-media-attribute: ^4.0.0
    rehype-minify-meta-color: ^4.0.0
    rehype-minify-meta-content: ^4.0.0
    rehype-minify-style-attribute: ^4.0.0
    rehype-minify-whitespace: ^6.0.0
    rehype-normalize-attribute-value-case: ^4.0.0
    rehype-remove-comments: ^6.0.0
    rehype-remove-duplicate-attribute-values: ^4.0.0
    rehype-remove-empty-attribute: ^4.0.0
    rehype-remove-external-script-content: ^4.0.0
    rehype-remove-meta-http-equiv: ^4.0.0
    rehype-remove-script-type-javascript: ^4.0.0
    rehype-remove-style-type-css: ^4.0.0
    rehype-sort-attribute-values: ^5.0.0
    rehype-sort-attributes: ^5.0.0
    unified: ^11.0.0
  checksum: bf323da70f00ef6b2449e601dd4a433dc20f32b7e9a796fee31e5aa4698a1122ca84740a21af4db7e59e3a97b8f86cf4a6e53cd34232f12dc16e9f4bdc554d93
  languageName: node
  linkType: hard

"rehype-prism-plus@npm:^2.0.0":
  version: 2.0.0
  resolution: "rehype-prism-plus@npm:2.0.0"
  dependencies:
    hast-util-to-string: ^3.0.0
    parse-numeric-range: ^1.3.0
    refractor: ^4.8.0
    rehype-parse: ^9.0.0
    unist-util-filter: ^5.0.0
    unist-util-visit: ^5.0.0
  checksum: c025f5b96d55431000cb4c688c9cbb174e6ed87c683d8499683f840d55e621b619365316b3d01f3c788086dd7a7abcdddca77f3054ba61c14ce6e73e31eb37e6
  languageName: node
  linkType: hard

"rehype-recma@npm:^1.0.0":
  version: 1.0.0
  resolution: "rehype-recma@npm:1.0.0"
  dependencies:
    "@types/estree": ^1.0.0
    "@types/hast": ^3.0.0
    hast-util-to-estree: ^3.0.0
  checksum: d3d544ad4a18485ec6b03a194b40473f96e2169c63d6a8ee3ce9af5e87b946c308fb9549b53e010c7dd39740337e387bb1a8856ce1b47f3e957b696f1d5b2d0c
  languageName: node
  linkType: hard

"rehype-remove-comments@npm:^6.0.0":
  version: 6.1.1
  resolution: "rehype-remove-comments@npm:6.1.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-conditional-comment: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: df40dafb6f7eeaf5c6e00ffd8d05edcd8b323d65e3adaa7c59382c05ffac04252af973fac4ae298902234af62df10effd86b2a3deb4d73162ddc3cffead5b40a
  languageName: node
  linkType: hard

"rehype-remove-duplicate-attribute-values@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-remove-duplicate-attribute-values@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-element: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 13343c1e496912f705da1fc55b69ccead5e8943a39bcc69cbf3ae7e4ed89db0ae8c2df47ca576d37c007e8c2f66a6aaa6342e71f8bbc5e3d450341a64514312d
  languageName: node
  linkType: hard

"rehype-remove-empty-attribute@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-remove-empty-attribute@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-element: ^3.0.0
    hast-util-is-event-handler: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: cbd968c725afe4f10111aed565a4f9d45b16e4bbcdb3cffc55a7812e188578a7dcc243de8022d76fc53920d04d7e13febccf2a527e71b730394582d4550c85e2
  languageName: node
  linkType: hard

"rehype-remove-external-script-content@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-remove-external-script-content@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-javascript: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 6803bfe7d986feb411436c0c8e80a2aa4ae6ca54103a9d7cd4dca294f669a16c1a2bf3f28d6bf87d0dac52cbca4aa33221f7a7e4a5034546c4fc388774906d80
  languageName: node
  linkType: hard

"rehype-remove-meta-http-equiv@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-remove-meta-http-equiv@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    space-separated-tokens: ^2.0.0
    unist-util-visit: ^5.0.0
  checksum: 0aa22eaa08355c83cea268ddf403cd0f3b7ec5ed41afbc8785670e6571ee07fbb86470084b69d96df1495938b27413be286268cbdb91c1ed6625bacdf0015888
  languageName: node
  linkType: hard

"rehype-remove-script-type-javascript@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-remove-script-type-javascript@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-javascript: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 149cbd33dd4eea171e3be219cd9adb8788b0467d6d8a981989444ffa9d0cfcaac0d802be999137cdef670aaff4a20e2d999e66360d86ede06c0de540cf4c654b
  languageName: node
  linkType: hard

"rehype-remove-style-type-css@npm:^4.0.0":
  version: 4.0.1
  resolution: "rehype-remove-style-type-css@npm:4.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-css-link: ^3.0.0
    hast-util-is-css-style: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: a7d2580e9d7cf62b4417318c87299d34fd6c933034e71273e8b193554ce195312d9fe08ba260447d517447abe86a44f2a95e7fdd21bfc369ced2c8d3deaeb63c
  languageName: node
  linkType: hard

"rehype-slug@npm:^6.0.0":
  version: 6.0.0
  resolution: "rehype-slug@npm:6.0.0"
  dependencies:
    "@types/hast": ^3.0.0
    github-slugger: ^2.0.0
    hast-util-heading-rank: ^3.0.0
    hast-util-to-string: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 0e13ec558eb142d14a6daeab21bbef7c9230bfabec45987e15a24283650226eae3898ad162b8cb29ee39a8bce536bcc013eeab7dc6faa0295b0e91612a8c9f6e
  languageName: node
  linkType: hard

"rehype-sort-attribute-values@npm:^5.0.0":
  version: 5.0.1
  resolution: "rehype-sort-attribute-values@npm:5.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-is-element: ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 3af86c5e1ae4234dadca9f4eb71ad424eb783a60c2d1c09568556c3c0e9a500f4dc06c28bac3aba704af9b46c426805665c00bb8df59a398b69c6fd03b36f3f0
  languageName: node
  linkType: hard

"rehype-sort-attributes@npm:^5.0.0":
  version: 5.0.1
  resolution: "rehype-sort-attributes@npm:5.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: d207392d06c0c7ac4d179203fa1d93f0425dafc0fcd956b9a5454c0f693c022f2b2de6b5fbb673a4bed6bed4906787d302a15f99c004912130a0f10b68530390
  languageName: node
  linkType: hard

"rehype-stringify@npm:^10.0.0":
  version: 10.0.1
  resolution: "rehype-stringify@npm:10.0.1"
  dependencies:
    "@types/hast": ^3.0.0
    hast-util-to-html: ^9.0.0
    unified: ^11.0.0
  checksum: 239d1ca75d6dcf4bb863202697ceb000ad746f9c0e99c5df3e49397bb09af73adaa6b934a2c852c9bd5e8ab80ca1e35c51a4b2c565999599576c691ad06c149d
  languageName: node
  linkType: hard

"remark-frontmatter@npm:^5.0.0":
  version: 5.0.0
  resolution: "remark-frontmatter@npm:5.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    mdast-util-frontmatter: ^2.0.0
    micromark-extension-frontmatter: ^2.0.0
    unified: ^11.0.0
  checksum: b36e11d528d1d0172489c74ce7961bb6073f7272e71ea1349f765fc79c4246a758aef949174d371a088c48e458af776fcfbb3b043c49cd1120ca8239aeafe16a
  languageName: node
  linkType: hard

"remark-gfm@npm:^4.0.0":
  version: 4.0.0
  resolution: "remark-gfm@npm:4.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    mdast-util-gfm: ^3.0.0
    micromark-extension-gfm: ^3.0.0
    remark-parse: ^11.0.0
    remark-stringify: ^11.0.0
    unified: ^11.0.0
  checksum: 84bea84e388061fbbb697b4b666089f5c328aa04d19dc544c229b607446bc10902e46b67b9594415a1017bbbd7c811c1f0c30d36682c6d1a6718b66a1558261b
  languageName: node
  linkType: hard

"remark-github-blockquote-alert@npm:^1.2.1":
  version: 1.3.0
  resolution: "remark-github-blockquote-alert@npm:1.3.0"
  dependencies:
    unist-util-visit: ^5.0.0
  checksum: 34a9eebe5000570933d83cd28997668e0cc2bb2d1c4ef1bcc2ccffdfdc742e5bc7fa84bf03c88957a956fa20402a17606523246fd105433ecafb7fa3d1f2a2d7
  languageName: node
  linkType: hard

"remark-math@npm:^6.0.0":
  version: 6.0.0
  resolution: "remark-math@npm:6.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    mdast-util-math: ^3.0.0
    micromark-extension-math: ^3.0.0
    unified: ^11.0.0
  checksum: fef489acb6cae6e40af05012367dc22a846ce16301e8a96006c6d78935887bdb3e6c5018b6514884ecee57f9c7a51f97a10862526ab0a0f5f7b7d339fe0eb20f
  languageName: node
  linkType: hard

"remark-mdx-frontmatter@npm:^4.0.0":
  version: 4.0.0
  resolution: "remark-mdx-frontmatter@npm:4.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    estree-util-is-identifier-name: ^3.0.0
    estree-util-value-to-estree: ^3.0.0
    toml: ^3.0.0
    unified: ^11.0.0
    yaml: ^2.0.0
  checksum: 01f3874bf5728c7ec314a0802d53de46ed0730a7f53fd9d8305ab95593b4b411336789fd84581157c7bd3398e4848082d84d17d656475846b9bd08be6ef6d71d
  languageName: node
  linkType: hard

"remark-mdx@npm:^3.0.0":
  version: 3.1.0
  resolution: "remark-mdx@npm:3.1.0"
  dependencies:
    mdast-util-mdx: ^3.0.0
    micromark-extension-mdxjs: ^3.0.0
  checksum: ac631296b3f87f46c03b51e8b1e7c90b854361da57ec5d0fddc410c63400fcffae216f2cee3af946407fc88e60bfc5765ba8acabe8e91afc0b7c824db77df152
  languageName: node
  linkType: hard

"remark-parse@npm:^11.0.0":
  version: 11.0.0
  resolution: "remark-parse@npm:11.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    mdast-util-from-markdown: ^2.0.0
    micromark-util-types: ^2.0.0
    unified: ^11.0.0
  checksum: d83d245290fa84bb04fb3e78111f09c74f7417e7c012a64dd8dc04fccc3699036d828fbd8eeec8944f774b6c30cc1d925c98f8c46495ebcee7c595496342ab7f
  languageName: node
  linkType: hard

"remark-rehype@npm:^11.0.0, remark-rehype@npm:^11.1.0":
  version: 11.1.1
  resolution: "remark-rehype@npm:11.1.1"
  dependencies:
    "@types/hast": ^3.0.0
    "@types/mdast": ^4.0.0
    mdast-util-to-hast: ^13.0.0
    unified: ^11.0.0
    vfile: ^6.0.0
  checksum: e199dff098ae6a560e13dd1778dec9c61440f979cc931c4ca4dcde0d58e51c0723243a901c1842379b189083cf4d74f224f57833738095ffa9535179d7cf3f01
  languageName: node
  linkType: hard

"remark-stringify@npm:^11.0.0":
  version: 11.0.0
  resolution: "remark-stringify@npm:11.0.0"
  dependencies:
    "@types/mdast": ^4.0.0
    mdast-util-to-markdown: ^2.0.0
    unified: ^11.0.0
  checksum: 59e07460eb629d6c3b3c0f438b0b236e7e6858fd5ab770303078f5a556ec00354d9c7fb9ef6d5f745a4617ac7da1ab618b170fbb4dac120e183fecd9cc86bce6
  languageName: node
  linkType: hard

"remark@npm:^15.0.0":
  version: 15.0.1
  resolution: "remark@npm:15.0.1"
  dependencies:
    "@types/mdast": ^4.0.0
    remark-parse: ^11.0.0
    remark-stringify: ^11.0.0
    unified: ^11.0.0
  checksum: ac7edb7f9b70c22964bbc6c5d1c038dd10e1a43ccf436fbdb55fb8c89d54f1b77190b89386063ba410fbdd086fde9dca81ef470fc8358eed1ff76a9741ae3dcc
  languageName: node
  linkType: hard

"repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 1b809fc6db97decdc68f5b12c4d1a671c8e3f65ec4a40c238bc5200e44e85bcc52a54f78268ab9c29fcf5fe4f1343e805420056d1f30fa9a9ee4c2d93e3cc6c0
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 1012afc566b3fdb190a6309cc37ef3b2dcc35dff5fa6683a9d00cd25c3247edfbc4691b91078c97adc82a29b77a2660c30d791d65dab4fc78bfc473f60289977
  languageName: node
  linkType: hard

"resolve@npm:^1.14.2, resolve@npm:^1.19.0, resolve@npm:^1.22.4":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: ab7a32ff4046fcd7c6fdd525b24a7527847d03c3650c733b909b01b757f92eb23510afa9cc3e9bf3f26a3e073b48c88c706dfd4c1d2fb4a16a96b73b6328ddcf
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: a73ac69a1c4bd34c56b213d91f5b17ce390688fdb4a1a96ed3025cc7e08e7bfb90b3a06fcce461780cb0b589c958afcb0080ab802c71c01a7ecc8c64feafc89f
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.14.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.19.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.4#~builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#~builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 8aac1e4e4628bd00bf4b94b23de137dd3fe44097a8d528fd66db74484be929936e20c696e1a3edf4488f37e14180b73df6f600992baea3e089e8674291f16c9d
  languageName: node
  linkType: hard

"resolve@patch:resolve@^2.0.0-next.5#~builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#~builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 064d09c1808d0c51b3d90b5d27e198e6d0c5dad0eb57065fd40803d6a20553e5398b07f76739d69cbabc12547058bec6b32106ea66622375fb0d7e8fca6a846c
  languageName: node
  linkType: hard

"restore-cursor@npm:^4.0.0":
  version: 4.0.0
  resolution: "restore-cursor@npm:4.0.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: 5b675c5a59763bf26e604289eab35711525f11388d77f409453904e1e69c0d37ae5889295706b2c81d23bd780165084d040f9b68fffc32cc921519031c4fa4af
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rfdc@npm:^1.3.0":
  version: 1.4.1
  resolution: "rfdc@npm:1.4.1"
  checksum: 3b05bd55062c1d78aaabfcea43840cdf7e12099968f368e9a4c3936beb744adb41cbdb315eac6d4d8c6623005d6f87fdf16d8a10e1ff3722e84afea7281c8d13
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: ^10.3.7
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 50e27388dd2b3fa6677385fc1e2966e9157c89c86853b96d02e6915663a96b7ff4d590e14f6f70e90f9b554093aa5dbc05ac3012876be558c06a65437337bc05
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    has-symbols: ^1.1.0
    isarray: ^2.0.5
  checksum: 00f6a68140e67e813f3ad5e73e6dedcf3e42a9fa01f04d44b0d3f7b1f4b257af876832a9bfc82ac76f307e8a6cc652e3cf95876048a26cbec451847cf6ae3707
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.2.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    isarray: ^2.0.5
  checksum: 8c11cbee6dc8ff5cc0f3d95eef7052e43494591384015902e4292aef4ae9e539908288520ed97179cee17d6ffb450fe5f05a46ce7a1749685f7524fd568ab5db
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3, safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-regex: ^1.2.1
  checksum: 3c809abeb81977c9ed6c869c83aca6873ea0f3ab0f806b8edbba5582d51713f8a6e9757d24d2b4b088f563801475ea946c8e77e7713e8c65cdd02305b6caedab
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sax@npm:^1.2.4":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 3ad64df16b743f0f2eb7c38ced9692a6d924f1cd07bbe45c39576c2cf50de8290d9d04e7b2228f924c7d05fecc4ec5cf651423278e0c7b63d260c387ef3af84a
  languageName: node
  linkType: hard

"scheduler@npm:^0.25.0":
  version: 0.25.0
  resolution: "scheduler@npm:0.25.0"
  checksum: b7bb9fddbf743e521e9aaa5198a03ae823f5e104ebee0cb9ec625392bb7da0baa1c28ab29cee4b1e407a94e76acc6eee91eeb749614f91f853efda2613531566
  languageName: node
  linkType: hard

"section-matter@npm:^1.0.0":
  version: 1.0.0
  resolution: "section-matter@npm:1.0.0"
  dependencies:
    extend-shallow: ^2.0.1
    kind-of: ^6.0.0
  checksum: 3cc4131705493b2955729b075dcf562359bba66183debb0332752dc9cad35616f6da7a23e42b6cab45cd2e4bb5cda113e9e84c8f05aee77adb6b0289a0229101
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.2, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 586b825d36874007c9382d9e1ad8f93888d8670040add24a28e06a910aeebd673a2eb9e3bf169c6679d9245e66efb9057e0852e70d9daa6c27372aab1dda7104
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
  checksum: ec27cbbe334598547e99024403e96da32aca3e530583e4dba7f5db1c43cbc4affa9adfbd77c7b2c210b9b8b2e7b2e600bad2a6c44fd62e804d8233f96bbb62f4
  languageName: node
  linkType: hard

"sharp@npm:^0.33.5":
  version: 0.33.5
  resolution: "sharp@npm:0.33.5"
  dependencies:
    "@img/sharp-darwin-arm64": 0.33.5
    "@img/sharp-darwin-x64": 0.33.5
    "@img/sharp-libvips-darwin-arm64": 1.0.4
    "@img/sharp-libvips-darwin-x64": 1.0.4
    "@img/sharp-libvips-linux-arm": 1.0.5
    "@img/sharp-libvips-linux-arm64": 1.0.4
    "@img/sharp-libvips-linux-s390x": 1.0.4
    "@img/sharp-libvips-linux-x64": 1.0.4
    "@img/sharp-libvips-linuxmusl-arm64": 1.0.4
    "@img/sharp-libvips-linuxmusl-x64": 1.0.4
    "@img/sharp-linux-arm": 0.33.5
    "@img/sharp-linux-arm64": 0.33.5
    "@img/sharp-linux-s390x": 0.33.5
    "@img/sharp-linux-x64": 0.33.5
    "@img/sharp-linuxmusl-arm64": 0.33.5
    "@img/sharp-linuxmusl-x64": 0.33.5
    "@img/sharp-wasm32": 0.33.5
    "@img/sharp-win32-ia32": 0.33.5
    "@img/sharp-win32-x64": 0.33.5
    color: ^4.2.3
    detect-libc: ^2.0.3
    semver: ^7.6.3
  dependenciesMeta:
    "@img/sharp-darwin-arm64":
      optional: true
    "@img/sharp-darwin-x64":
      optional: true
    "@img/sharp-libvips-darwin-arm64":
      optional: true
    "@img/sharp-libvips-darwin-x64":
      optional: true
    "@img/sharp-libvips-linux-arm":
      optional: true
    "@img/sharp-libvips-linux-arm64":
      optional: true
    "@img/sharp-libvips-linux-s390x":
      optional: true
    "@img/sharp-libvips-linux-x64":
      optional: true
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
    "@img/sharp-linux-arm":
      optional: true
    "@img/sharp-linux-arm64":
      optional: true
    "@img/sharp-linux-s390x":
      optional: true
    "@img/sharp-linux-x64":
      optional: true
    "@img/sharp-linuxmusl-arm64":
      optional: true
    "@img/sharp-linuxmusl-x64":
      optional: true
    "@img/sharp-wasm32":
      optional: true
    "@img/sharp-win32-ia32":
      optional: true
    "@img/sharp-win32-x64":
      optional: true
  checksum: 04beae89910ac65c5f145f88de162e8466bec67705f497ace128de849c24d168993e016f33a343a1f3c30b25d2a90c3e62b017a9a0d25452371556f6cd2471e4
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"sirv@npm:^2.0.3":
  version: 2.0.4
  resolution: "sirv@npm:2.0.4"
  dependencies:
    "@polka/url": ^1.0.0-next.24
    mrmime: ^2.0.0
    totalist: ^3.0.0
  checksum: 6853384a51d6ee9377dd657e2b257e0e98b29abbfbfa6333e105197f0f100c8c56a4520b47028b04ab1833cf2312526206f38fcd4f891c6df453f40da1a15a57
  languageName: node
  linkType: hard

"slice-ansi@npm:^5.0.0":
  version: 5.0.0
  resolution: "slice-ansi@npm:5.0.0"
  dependencies:
    ansi-styles: ^6.0.0
    is-fullwidth-code-point: ^4.0.0
  checksum: 7e600a2a55e333a21ef5214b987c8358fe28bfb03c2867ff2cbf919d62143d1812ac27b4297a077fdaf27a03da3678e49551c93e35f9498a3d90221908a1180e
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: ^3.0.4
    tslib: ^2.0.3
  checksum: 0a7a79900bbb36f8aaa922cf111702a3647ac6165736d5dc96d3ef367efc50465cac70c53cd172c382b022dac72ec91710608e5393de71f76d7142e6fd80e8a3
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 7a6b7f6eedf7482b9e4597d9a20e09505824208006ea8f2c49b71657427f3c137ca2ae662089baa73e1971c62322d535d9d0cf1c9235cf6f55e315c18203eadd
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.1, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 4eb0cd997cdf228bc253bcaff9340afeb706176e64868ecd20efbe6efea931465f43955612346d6b7318789e5265bdc419bc7669c1cebe3db0eb255f57efa76b
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.21":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:~0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"source-map@npm:^0.7.0":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 01cc5a74b1f0e1d626a58d36ad6898ea820567e87f18dfc9d24a9843a351aaa2ec09b87422589906d6ff1deed29693e176194dc88bcae7c9a852dc74b311dbf5
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^2.0.0":
  version: 2.0.2
  resolution: "space-separated-tokens@npm:2.0.2"
  checksum: 202e97d7ca1ba0758a0aa4fe226ff98142073bcceeff2da3aad037968878552c3bbce3b3231970025375bbba5aee00c5b8206eda408da837ab2dc9c0f26be990
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"stable-hash@npm:^0.0.4":
  version: 0.0.4
  resolution: "stable-hash@npm:0.0.4"
  checksum: 21c039d21c1cb739cf8342561753a5e007cb95ea682ccd452e76310bbb9c6987a89de8eda023e320b019f3e4691aabda75079cdbb7dadf7ab9013e931f2f23cd
  languageName: node
  linkType: hard

"stream-parser@npm:~0.3.1":
  version: 0.3.1
  resolution: "stream-parser@npm:0.3.1"
  dependencies:
    debug: 2
  checksum: 4d86ff8cffe7c7587dc91433fff9dce38a93ea7e9f47560055addc81eae6b6befab22b75643ce539faf325fe2b17d371778242566bed086e75f6cffb1e76c06c
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 1cce16cea8405d7a233d32ca5e00a00169cc0e19fbc02aa839959985f267335d435c07f96e5e0edd0eadc6d39c98d5435fb5bbbdefc62c41834eadc5622ad942
  languageName: node
  linkType: hard

"string-argv@npm:0.3.2":
  version: 0.3.2
  resolution: "string-argv@npm:0.3.2"
  checksum: 8703ad3f3db0b2641ed2adbb15cf24d3945070d9a751f9e74a924966db9f325ac755169007233e8985a39a6a292f14d4fee20482989b89b96e473c4221508a0f
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.0, string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.includes@npm:^2.0.1":
  version: 2.0.1
  resolution: "string.prototype.includes@npm:2.0.1"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
  checksum: ed4b7058b092f30d41c4df1e3e805eeea92479d2c7a886aa30f42ae32fde8924a10cc99cccc99c29b8e18c48216608a0fe6bf887f8b4aadf9559096a758f313a
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-abstract: ^1.23.6
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.6
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    internal-slot: ^1.1.0
    regexp.prototype.flags: ^1.5.3
    set-function-name: ^2.0.2
    side-channel: ^1.1.0
  checksum: 98a09d6af91bfc6ee25556f3d7cd6646d02f5f08bda55d45528ed273d266d55a71af7291fe3fc76854deffb9168cc1a917d0b07a7d5a178c7e9537c99e6d2b57
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: ^1.1.3
    es-abstract: ^1.17.5
  checksum: 95dfc514ed7f328d80a066dabbfbbb1615c3e51490351085409db2eb7cbfed7ea29fdadaf277647fbf9f4a1e10e6dd9e95e78c0fd2c4e6bb6723ea6e59401004
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-data-property: ^1.1.4
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-object-atoms: ^1.0.0
    has-property-descriptors: ^1.0.2
  checksum: 87659cd8561237b6c69f5376328fda934693aedde17bb7a2c57008e9d9ff992d0c253a391c7d8d50114e0e49ff7daf86a362f7961cf92f7564cd01342ca2e385
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8, string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: cb86f639f41d791a43627784be2175daa9ca3259c7cb83e7a207a729909b74f2ea0ec5d85de5761e6835e5f443e9420c6ff3f63a845378e4a61dd793177bc287
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: df1007a7f580a49d692375d996521dc14fd103acda7f3034b3c558a60b82beeed3a64fa91e494e164581793a8ab0ae2f59578a49896a7af6583c1f20472bce96
  languageName: node
  linkType: hard

"string_decoder@npm:~0.10.x":
  version: 0.10.31
  resolution: "string_decoder@npm:0.10.31"
  checksum: fe00f8e303647e5db919948ccb5ce0da7dea209ab54702894dd0c664edd98e5d4df4b80d6fabf7b9e92b237359d21136c95bf068b2f7760b772ca974ba970202
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"stringify-entities@npm:^4.0.0":
  version: 4.0.4
  resolution: "stringify-entities@npm:4.0.4"
  dependencies:
    character-entities-html4: ^2.0.0
    character-entities-legacy: ^3.0.0
  checksum: ac1344ef211eacf6cf0a0a8feaf96f9c36083835b406560d2c6ff5a87406a41b13f2f0b4c570a3b391f465121c4fd6822b863ffb197e8c0601a64097862cc5b5
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom-string@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-bom-string@npm:1.0.0"
  checksum: 5635a3656d8512a2c194d6c8d5dee7ef0dde6802f7be9413b91e201981ad4132506656d9cf14137f019fd50f0269390d91c7f6a2601b1bee039a4859cfce4934
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-final-newline@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-final-newline@npm:3.0.0"
  checksum: 23ee263adfa2070cd0f23d1ac14e2ed2f000c9b44229aec9c799f1367ec001478469560abefd00c5c99ee6f0b31c137d53ec6029c53e9f32a93804e18c201050
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"style-to-object@npm:^1.0.0":
  version: 1.0.8
  resolution: "style-to-object@npm:1.0.8"
  dependencies:
    inline-style-parser: 0.2.4
  checksum: 80ca4773fc728d7919edc552eb46bab11aa8cdd0b426528ee8b817ba6872ea7b9d38fbb97b6443fd2d4895a4c4b02ec32765387466a302d0b4d1b91deab1e1a0
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.6":
  version: 5.1.6
  resolution: "styled-jsx@npm:5.1.6"
  dependencies:
    client-only: 0.0.1
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 879ad68e3e81adcf4373038aaafe55f968294955593660e173fbf679204aff158c59966716a60b29af72dc88795cfb2c479b6d2c3c87b2b2d282f3e27cc66461
  languageName: node
  linkType: hard

"superagent@npm:3.8.1":
  version: 3.8.1
  resolution: "superagent@npm:3.8.1"
  dependencies:
    component-emitter: ^1.2.0
    cookiejar: ^2.1.0
    debug: ^3.1.0
    extend: ^3.0.0
    form-data: ^2.3.1
    formidable: ^1.1.1
    methods: ^1.1.1
    mime: ^1.4.1
    qs: ^6.5.1
    readable-stream: ^2.0.5
  checksum: 42895e220fb5aab303edeef7ec4d9c38fef31638d18254fe57329366e7a74624dffe20acd682a9a27df4f62fe4acb953b33d16be9611f184284815c2492d35cf
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"svg-parser@npm:^2.0.4":
  version: 2.0.4
  resolution: "svg-parser@npm:2.0.4"
  checksum: b3de6653048212f2ae7afe4a423e04a76ec6d2d06e1bf7eacc618a7c5f7df7faa5105561c57b94579ec831fbbdbf5f190ba56a9205ff39ed13eabdf8ab086ddf
  languageName: node
  linkType: hard

"svgo@npm:^3.0.2":
  version: 3.3.2
  resolution: "svgo@npm:3.3.2"
  dependencies:
    "@trysound/sax": 0.2.0
    commander: ^7.2.0
    css-select: ^5.1.0
    css-tree: ^2.3.1
    css-what: ^6.1.0
    csso: ^5.0.5
    picocolors: ^1.0.0
  bin:
    svgo: ./bin/svgo
  checksum: a3f8aad597dec13ab24e679c4c218147048dc1414fe04e99447c5f42a6e077b33d712d306df84674b5253b98c9b84dfbfb41fdd08552443b04946e43d03e054e
  languageName: node
  linkType: hard

"sync-fetch@npm:^0.4.1":
  version: 0.4.5
  resolution: "sync-fetch@npm:0.4.5"
  dependencies:
    buffer: ^5.7.1
    node-fetch: ^2.6.1
  checksum: 84aa2bf85b1a6558b7c75716d7b7c3bd787f02602993a08bdfdd69384afa7d6dc54e94b006a6362277334edf92940e0f4de57801166c52854912443fbbe78e00
  languageName: node
  linkType: hard

"synckit@npm:^0.9.1":
  version: 0.9.2
  resolution: "synckit@npm:0.9.2"
  dependencies:
    "@pkgr/core": ^0.1.0
    tslib: ^2.6.2
  checksum: 3a30e828efbdcf3b50fccab4da6e90ea7ca24d8c5c2ad3ffe98e07d7c492df121e0f75227c6e510f96f976aae76f1fa4710cb7b1d69db881caf66ef9de89360e
  languageName: node
  linkType: hard

"tabbable@npm:^6.0.0":
  version: 6.2.0
  resolution: "tabbable@npm:6.2.0"
  checksum: f8440277d223949272c74bb627a3371be21735ca9ad34c2570f7e1752bd646ccfc23a9d8b1ee65d6561243f4134f5fbbf1ad6b39ac3c4b586554accaff4a1300
  languageName: node
  linkType: hard

"tailwind-nextjs-starter-blog@workspace:.":
  version: 0.0.0-use.local
  resolution: "tailwind-nextjs-starter-blog@workspace:."
  dependencies:
    "@eslint/eslintrc": ^3.2.0
    "@eslint/js": ^9.16.0
    "@headlessui/react": 2.2.0
    "@next/bundle-analyzer": 15.2.4
    "@svgr/webpack": ^8.0.1
    "@tailwindcss/forms": ^0.5.9
    "@tailwindcss/postcss": ^4.0.5
    "@tailwindcss/typography": ^0.5.15
    "@types/mdx": ^2.0.12
    "@types/react": ^19.0.8
    "@typescript-eslint/eslint-plugin": ^8.12.0
    "@typescript-eslint/parser": ^8.12.0
    body-scroll-lock: ^4.0.0-beta.0
    contentlayer2: 0.5.5
    cross-env: ^7.0.3
    esbuild: 0.25.2
    eslint: ^9.14.0
    eslint-config-next: 15.2.4
    eslint-config-prettier: ^9.1.0
    eslint-plugin-prettier: ^5.2.0
    github-slugger: ^2.0.0
    globals: ^15.12.0
    gray-matter: ^4.0.2
    hast-util-from-html-isomorphic: ^2.0.0
    husky: ^9.0.0
    image-size: 2.0.1
    lint-staged: ^13.0.0
    next: 15.2.4
    next-contentlayer2: 0.5.5
    next-themes: ^0.4.6
    pliny: 0.4.1
    postcss: ^8.4.24
    prettier: ^3.0.0
    prettier-plugin-tailwindcss: ^0.6.11
    react: 19.0.0
    react-dom: 19.0.0
    reading-time: 1.5.0
    rehype-autolink-headings: ^7.1.0
    rehype-citation: ^2.3.0
    rehype-katex: ^7.0.0
    rehype-katex-notranslate: ^1.1.4
    rehype-preset-minify: 7.0.0
    rehype-prism-plus: ^2.0.0
    rehype-slug: ^6.0.0
    remark: ^15.0.0
    remark-gfm: ^4.0.0
    remark-github-blockquote-alert: ^1.2.1
    remark-math: ^6.0.0
    tailwindcss: ^4.0.5
    typescript: ^5.1.3
    unist-util-visit: ^5.0.0
  languageName: unknown
  linkType: soft

"tailwindcss@npm:4.0.5, tailwindcss@npm:^4.0.5":
  version: 4.0.5
  resolution: "tailwindcss@npm:4.0.5"
  checksum: 9b1dccf24cd4aa70372647881d1b809f39666300ae84df786a9a6b7f51c7748cd3ba2615bed1163b6a667e0d233ceb6e76d827f3d039ad3638bacc0cb650e9b4
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"thingies@npm:^1.20.0":
  version: 1.21.0
  resolution: "thingies@npm:1.21.0"
  peerDependencies:
    tslib: ^2
  checksum: 283a2785e513dc892822dd0bbadaa79e873a7fc90b84798164717bf7cf837553e0b4518d8027b2307d8f6fc6caab088fa717112cd9196c6222763cc3cc1b7e79
  languageName: node
  linkType: hard

"through2@npm:^2.0.1":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: ~2.3.6
    xtend: ~4.0.1
  checksum: beb0f338aa2931e5660ec7bf3ad949e6d2e068c31f4737b9525e5201b824ac40cac6a337224856b56bd1ddd866334bbfb92a9f57cd6f66bc3f18d3d86fc0fe50
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.2.0":
  version: 1.3.3
  resolution: "tiny-invariant@npm:1.3.3"
  checksum: 5e185c8cc2266967984ce3b352a4e57cb89dad5a8abb0dea21468a6ecaa67cd5bb47a3b7a85d08041008644af4f667fb8b6575ba38ba5fb00b3b5068306e59fe
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toml@npm:^3.0.0":
  version: 3.0.0
  resolution: "toml@npm:3.0.0"
  checksum: 5d7f1d8413ad7780e9bdecce8ea4c3f5130dd53b0a4f2e90b93340979a137739879d7b9ce2ce05c938b8cc828897fe9e95085197342a1377dd8850bf5125f15f
  languageName: node
  linkType: hard

"totalist@npm:^3.0.0":
  version: 3.0.1
  resolution: "totalist@npm:3.0.1"
  checksum: 5132d562cf88ff93fd710770a92f31dbe67cc19b5c6ccae2efc0da327f0954d211bbfd9456389655d726c624f284b4a23112f56d1da931ca7cfabbe1f45e778a
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"tree-dump@npm:^1.0.1":
  version: 1.0.2
  resolution: "tree-dump@npm:1.0.2"
  peerDependencies:
    tslib: 2
  checksum: 3b0cae6cd74c208da77dac1c65e6a212f5678fe181f1dfffbe05752be188aa88e56d5d5c33f5701d1f603ffcf33403763f722c9e8e398085cde0c0994323cb8d
  languageName: node
  linkType: hard

"trim-lines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-lines@npm:3.0.1"
  checksum: e241da104682a0e0d807222cc1496b92e716af4db7a002f4aeff33ae6a0024fef93165d49eab11aa07c71e1347c42d46563f91dfaa4d3fb945aa535cdead53ed
  languageName: node
  linkType: hard

"trough@npm:^2.0.0":
  version: 2.2.0
  resolution: "trough@npm:2.2.0"
  checksum: 6097df63169aca1f9b08c263b1b501a9b878387f46e161dde93f6d0bba7febba93c95f876a293c5ea370f6cb03bcb687b2488c8955c3cfb66c2c0161ea8c00f6
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.0.1":
  version: 2.0.1
  resolution: "ts-api-utils@npm:2.0.1"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: ca31f4dc3c0d69691599de2955b41879c27cb91257f2a468bbb444d3f09982a5f717a941fcebd3aaa092b778710647a0be1c2b1dd75cf6c82ceffc3bf4c7d27d
  languageName: node
  linkType: hard

"ts-pattern@npm:^5.0.6":
  version: 5.6.2
  resolution: "ts-pattern@npm:5.6.2"
  checksum: aa8bcbe02b198a33a374b9720cef4e2ba2f3c7d91560dd430a9a20d7ca324a9db9ea45fd51d6ab55af9e62da09188dcdd98011c23f1b9b7aac02323bcad33e60
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 59f35407a390d9482b320451f52a411a256a130ff0e7543d18c6f20afab29ac19fbe55c360a93d6476213cc335a4d76ce90f67df54c4e9037f7d240920832201
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.0.3, tslib@npm:^2.4.0, tslib@npm:^2.4.1, tslib@npm:^2.6.2, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: e4aba30e632b8c8902b47587fd13345e2827fa639e7c3121074d5ee0880723282411a8838f830b55100cbe4517672f84a2472667d355b81e8af165a55dc6203a
  languageName: node
  linkType: hard

"typanion@npm:^3.12.1, typanion@npm:^3.8.0":
  version: 3.14.0
  resolution: "typanion@npm:3.14.0"
  checksum: fc0590d02c13c659eb1689e8adf7777e6c00dc911377e44cd36fe1b1271cfaca71547149f12cdc275058c0de5562a14e5273adbae66d47e6e0320e36007f5912
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-fest@npm:^1.0.2":
  version: 1.4.0
  resolution: "type-fest@npm:1.4.0"
  checksum: b011c3388665b097ae6a109a437a04d6f61d81b7357f74cbcb02246f2f5bd72b888ae33631b99871388122ba0a87f4ff1c94078e7119ff22c70e52c0ff828201
  languageName: node
  linkType: hard

"type-fest@npm:^4.10.0":
  version: 4.33.0
  resolution: "type-fest@npm:4.33.0"
  checksum: 42c9a4e305ef86826f6c3e9fb0d230765523eba248b7927580e76fa0384a4a12dfcde3ba04ac94b3cfab664b16608f1f9b8fb6116a48c728b87350e8252fd32c
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-typed-array: ^1.1.14
  checksum: 3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.14
  checksum: cda9352178ebeab073ad6499b03e938ebc30c4efaea63a26839d89c4b1da9d2640b0d937fc2bd1f049eb0a38def6fbe8a061b601292ae62fe079a410ce56e3a6
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.15
    reflect.getprototypeof: ^1.0.9
  checksum: 670b7e6bb1d3c2cf6160f27f9f529e60c3f6f9611c67e47ca70ca5cfa24ad95415694c49d1dbfeda016d3372cab7dfc9e38c7b3e1bb8d692cae13a63d3c144d7
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    is-typed-array: ^1.1.13
    possible-typed-array-names: ^1.0.0
    reflect.getprototypeof: ^1.0.6
  checksum: deb1a4ffdb27cd930b02c7030cb3e8e0993084c643208e52696e18ea6dd3953dfc37b939df06ff78170423d353dc8b10d5bae5796f3711c1b3abe52872b3774c
  languageName: node
  linkType: hard

"typescript@npm:^5.1.3":
  version: 5.7.3
  resolution: "typescript@npm:5.7.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 6c38b1e989918e576f0307e6ee013522ea480dfce5f3ca85c9b2d8adb1edeffd37f4f30cd68de0c38a44563d12ba922bdb7e36aa2dac9c51de5d561e6e9a2e9c
  languageName: node
  linkType: hard

"typescript@patch:typescript@^5.1.3#~builtin<compat/typescript>":
  version: 5.7.3
  resolution: "typescript@patch:typescript@npm%3A5.7.3#~builtin<compat/typescript>::version=5.7.3&hash=14eedb"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 633cd749d6cd7bc842c6b6245847173bba99742a60776fae3c0fbcc0d1733cd51a733995e5f4dadd8afb0e64e57d3c7dbbeae953a072ee303940eca69e22f311
  languageName: node
  linkType: hard

"uglify-js@npm:^3.0.0":
  version: 3.19.3
  resolution: "uglify-js@npm:3.19.3"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 7ed6272fba562eb6a3149cfd13cda662f115847865c03099e3995a0e7a910eba37b82d4fccf9e88271bb2bcbe505bb374967450f433c17fa27aa36d94a8d0553
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    has-bigints: ^1.0.2
    has-symbols: ^1.1.0
    which-boxed-primitive: ^1.1.1
  checksum: 729f13b84a5bfa3fead1d8139cee5c38514e63a8d6a437819a473e241ba87eeb593646568621c7fc7f133db300ef18d65d1a5a60dc9c7beb9000364d93c581df
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: b7bc50f012dc6afbcce56c9fd62d7e86b20a62ff21f12b7b5cbf1973b9578d90f22a9c7fe50e638e96905d33893bf2f9f16d98929c4673c2480de05c6c96ea8b
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 3c3dabdb1d22aef4904399f9e810d0b71c0b12b3815169d96fac97e56d5642840c6071cf709adcace2252bc6bb80242396c2ec74b37224eb015c5f7aca40bad7
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 9e3151e1d0bc6be35c4cef105e317c04090364173e8462005b5cde08a1e7c858b6586486cfebac39dc2c6c8c9ee24afb245de6d527604866edfa454fe2a35fae
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unified@npm:^11.0.0, unified@npm:^11.0.4, unified@npm:^11.0.5":
  version: 11.0.5
  resolution: "unified@npm:11.0.5"
  dependencies:
    "@types/unist": ^3.0.0
    bail: ^2.0.0
    devlop: ^1.0.0
    extend: ^3.0.0
    is-plain-obj: ^4.0.0
    trough: ^2.0.0
    vfile: ^6.0.0
  checksum: b3bf7fd6f568cc261e074dae21188483b0f2a8ab858d62e6e85b75b96cc655f59532906ae3c64d56a9b257408722d71f1d4135292b3d7ee02907c8b592fb3cf0
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"unist-util-filter@npm:^5.0.0":
  version: 5.0.1
  resolution: "unist-util-filter@npm:5.0.1"
  dependencies:
    "@types/unist": ^3.0.0
    unist-util-is: ^6.0.0
    unist-util-visit-parents: ^6.0.0
  checksum: d9d1d9dfd89a91f15ced255aa8dc61cb7648273eb791e3d9e9385c05995fe24ccf1f2119c2f7e2c671c61f7275ffa93f13440e4adfad1d0e494e8507cb818981
  languageName: node
  linkType: hard

"unist-util-find-after@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-find-after@npm:5.0.0"
  dependencies:
    "@types/unist": ^3.0.0
    unist-util-is: ^6.0.0
  checksum: e64bd5ebee7ac021cf990bf33e9ec29fc6452159187d4a7fa0f77334bea8e378fea7a7fb0bcf957300b2ffdba902ff25b62c165fc8b86309613da35ad793ada0
  languageName: node
  linkType: hard

"unist-util-is@npm:^6.0.0":
  version: 6.0.0
  resolution: "unist-util-is@npm:6.0.0"
  dependencies:
    "@types/unist": ^3.0.0
  checksum: f630a925126594af9993b091cf807b86811371e465b5049a6283e08537d3e6ba0f7e248e1e7dab52cfe33f9002606acef093441137181b327f6fe504884b20e2
  languageName: node
  linkType: hard

"unist-util-position-from-estree@npm:^2.0.0":
  version: 2.0.0
  resolution: "unist-util-position-from-estree@npm:2.0.0"
  dependencies:
    "@types/unist": ^3.0.0
  checksum: d3b3048a5727c2367f64ef6dcc5b20c4717215ef8b1372ff9a7c426297c5d1e5776409938acd01531213e2cd2543218d16e73f9f862f318e9496e2c73bb18354
  languageName: node
  linkType: hard

"unist-util-position@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-position@npm:5.0.0"
  dependencies:
    "@types/unist": ^3.0.0
  checksum: f89b27989b19f07878de9579cd8db2aa0194c8360db69e2c99bd2124a480d79c08f04b73a64daf01a8fb3af7cba65ff4b45a0b978ca243226084ad5f5d441dde
  languageName: node
  linkType: hard

"unist-util-remove-position@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-remove-position@npm:5.0.0"
  dependencies:
    "@types/unist": ^3.0.0
    unist-util-visit: ^5.0.0
  checksum: 8aabdb9d0e3e744141bc123d8f87b90835d521209ad3c6c4619d403b324537152f0b8f20dda839b40c3aa0abfbf1828b3635a7a8bb159c3ed469e743023510ee
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^4.0.0":
  version: 4.0.0
  resolution: "unist-util-stringify-position@npm:4.0.0"
  dependencies:
    "@types/unist": ^3.0.0
  checksum: e2e7aee4b92ddb64d314b4ac89eef7a46e4c829cbd3ee4aee516d100772b490eb6b4974f653ba0717a0071ca6ea0770bf22b0a2ea62c65fcba1d071285e96324
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^6.0.0":
  version: 6.0.1
  resolution: "unist-util-visit-parents@npm:6.0.1"
  dependencies:
    "@types/unist": ^3.0.0
    unist-util-is: ^6.0.0
  checksum: 08927647c579f63b91aafcbec9966dc4a7d0af1e5e26fc69f4e3e6a01215084835a2321b06f3cbe7bf7914a852830fc1439f0fc3d7153d8804ac3ef851ddfa20
  languageName: node
  linkType: hard

"unist-util-visit@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-visit@npm:5.0.0"
  dependencies:
    "@types/unist": ^3.0.0
    unist-util-is: ^6.0.0
    unist-util-visit-parents: ^6.0.0
  checksum: 9ec42e618e7e5d0202f3c191cd30791b51641285732767ee2e6bcd035931032e3c1b29093f4d7fd0c79175bbc1f26f24f26ee49770d32be76f8730a652a857e6
  languageName: node
  linkType: hard

"untildify@npm:^4.0.0":
  version: 4.0.0
  resolution: "untildify@npm:4.0.0"
  checksum: 39ced9c418a74f73f0a56e1ba4634b4d959422dff61f4c72a8e39f60b99380c1b45ed776fbaa0a4101b157e4310d873ad7d114e8534ca02609b4916bb4187fb9
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.2
  resolution: "update-browserslist-db@npm:1.1.2"
  dependencies:
    escalade: ^3.2.0
    picocolors: ^1.1.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 088d2bad8ddeaeccd82d87d3f6d736d5256d697b725ffaa2b601dfd0ec16ba5fad20db8dcdccf55396e1a36194236feb69e3f5cce772e5be15a5e4261ff2815d
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 39931f6da74e307f51c0fb463dc2462807531dc80760a9bff1e35af4316131b4fc3203d16da60ae33f07fdca5b56f3f1dd662da0c99fea9aaeab2004780cc5f4
  languageName: node
  linkType: hard

"vfile-location@npm:^5.0.0":
  version: 5.0.3
  resolution: "vfile-location@npm:5.0.3"
  dependencies:
    "@types/unist": ^3.0.0
    vfile: ^6.0.0
  checksum: bfb3821b6981b6e9aa369bed67a40090b800562064ea312e84437762562df3225a0ca922695389cc0ef1e115f19476c363f53e3ed44dec17c50678b7670b5f2b
  languageName: node
  linkType: hard

"vfile-message@npm:^4.0.0":
  version: 4.0.2
  resolution: "vfile-message@npm:4.0.2"
  dependencies:
    "@types/unist": ^3.0.0
    unist-util-stringify-position: ^4.0.0
  checksum: 964e7e119f4c0e0270fc269119c41c96da20afa01acb7c9809a88365c8e0c64aa692fafbd952669382b978002ecd7ad31ef4446d85e8a22cdb62f6df20186c2d
  languageName: node
  linkType: hard

"vfile@npm:^6.0.0, vfile@npm:^6.0.1":
  version: 6.0.3
  resolution: "vfile@npm:6.0.3"
  dependencies:
    "@types/unist": ^3.0.0
    vfile-message: ^4.0.0
  checksum: 152b6729be1af70df723efb65c1a1170fd483d41086557da3651eea69a1dd1f0c22ea4344834d56d30734b9185bcab63e22edc81d3f0e9bed8aa4660d61080af
  languageName: node
  linkType: hard

"web-namespaces@npm:^2.0.0":
  version: 2.0.1
  resolution: "web-namespaces@npm:2.0.1"
  checksum: b6d9f02f1a43d0ef0848a812d89c83801d5bbad57d8bb61f02eb6d7eb794c3736f6cc2e1191664bb26136594c8218ac609f4069722c6f56d9fc2d808fa9271c6
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"webpack-bundle-analyzer@npm:4.10.1":
  version: 4.10.1
  resolution: "webpack-bundle-analyzer@npm:4.10.1"
  dependencies:
    "@discoveryjs/json-ext": 0.5.7
    acorn: ^8.0.4
    acorn-walk: ^8.0.0
    commander: ^7.2.0
    debounce: ^1.2.1
    escape-string-regexp: ^4.0.0
    gzip-size: ^6.0.0
    html-escaper: ^2.0.2
    is-plain-object: ^5.0.0
    opener: ^1.5.2
    picocolors: ^1.0.0
    sirv: ^2.0.3
    ws: ^7.3.1
  bin:
    webpack-bundle-analyzer: lib/bin/analyzer.js
  checksum: 77f48f10a493b1cc95674526472978a2de32412ddbf556bd3903738f14890611426f19477352993efe5a9fd6ca16711eb912d986f2221b17ba6eeca1b6f71fb6
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: ^1.1.0
    is-boolean-object: ^1.2.1
    is-number-object: ^1.1.1
    is-string: ^1.1.1
    is-symbol: ^1.1.1
  checksum: ee41d0260e4fd39551ad77700c7047d3d281ec03d356f5e5c8393fe160ba0db53ef446ff547d05f76ffabfd8ad9df7c9a827e12d4cccdbc8fccf9239ff8ac21e
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    function.prototype.name: ^1.1.6
    has-tostringtag: ^1.0.2
    is-async-function: ^2.0.0
    is-date-object: ^1.1.0
    is-finalizationregistry: ^1.1.0
    is-generator-function: ^1.0.10
    is-regex: ^1.2.1
    is-weakref: ^1.0.2
    isarray: ^2.0.5
    which-boxed-primitive: ^1.1.0
    which-collection: ^1.0.2
    which-typed-array: ^1.1.16
  checksum: 7a3617ba0e7cafb795f74db418df889867d12bce39a477f3ee29c6092aa64d396955bf2a64eae3726d8578440e26777695544057b373c45a8bcf5fbe920bf633
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: ^2.0.3
    is-set: ^2.0.3
    is-weakmap: ^2.0.2
    is-weakset: ^2.0.3
  checksum: c51821a331624c8197916598a738fc5aeb9a857f1e00d89f5e4c03dc7c60b4032822b8ec5696d28268bb83326456a8b8216344fb84270d18ff1d7628051879d9
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.18":
  version: 1.1.18
  resolution: "which-typed-array@npm:1.1.18"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
  checksum: d2feea7f51af66b3a240397aa41c796585033e1069f18e5b6d4cd3878538a1e7780596fd3ea9bf347c43d9e98e13be09b37d9ea3887cef29b11bc291fd47bb52
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.0.1, wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:^7.3.1":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: f9bb062abf54cc8f02d94ca86dcd349c3945d63851f5d07a3a61c2fcb755b15a88e943a63cf580cbdb5b74436d67ef6b67f745b8f7c0814e411379138e1863cb
  languageName: node
  linkType: hard

"xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yaml@npm:2.3.1":
  version: 2.3.1
  resolution: "yaml@npm:2.3.1"
  checksum: 2c7bc9a7cd4c9f40d3b0b0a98e370781b68b8b7c4515720869aced2b00d92f5da1762b4ffa947f9e795d6cd6b19f410bd4d15fdd38aca7bd96df59bd9486fb54
  languageName: node
  linkType: hard

"yaml@npm:^2.0.0, yaml@npm:^2.3.1":
  version: 2.7.0
  resolution: "yaml@npm:2.7.0"
  bin:
    yaml: bin.mjs
  checksum: 6e8b2f9b9d1b18b10274d58eb3a47ec223d9a93245a890dcb34d62865f7e744747190a9b9177d5f0ef4ea2e44ad2c0214993deb42e0800766203ac46f00a12dd
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 8bb69015f2b0ff9e17b2c8e6bfe224ab463dd00ca211eece72a4cd8a906224d2703fb8a326d36fdd0e68701e201b2a60ed7cf81ce0fd9b3799f9fe7745977ae3
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs@npm:^16.1.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: ^7.0.2
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.0
    y18n: ^5.0.5
    yargs-parser: ^20.2.2
  checksum: b14afbb51e3251a204d81937c86a7e9d4bdbf9a2bcee38226c900d00f522969ab675703bee2a6f99f8e20103f608382936034e64d921b74df82b63c07c5e8f59
  languageName: node
  linkType: hard

"yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 73b572e863aa4a8cbef323dd911d79d193b772defd5a51aab0aca2d446655216f5002c42c5306033968193bdbf892a7a4c110b0d77954a7fdf563e653967b56a
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zod@npm:^3.22.4":
  version: 3.24.1
  resolution: "zod@npm:3.24.1"
  checksum: dcd5334725b29555593c186fd6505878bb7ccb4f5954f728d2de24bf71f9397492d83bdb69d5b8a376eb500a02273ae0691b57deb1eb8718df3f64c77cc5534a
  languageName: node
  linkType: hard

"zwitch@npm:^2.0.0, zwitch@npm:^2.0.4":
  version: 2.0.4
  resolution: "zwitch@npm:2.0.4"
  checksum: f22ec5fc2d5f02c423c93d35cdfa83573a3a3bd98c66b927c368ea4d0e7252a500df2a90a6b45522be536a96a73404393c958e945fdba95e6832c200791702b6
  languageName: node
  linkType: hard
