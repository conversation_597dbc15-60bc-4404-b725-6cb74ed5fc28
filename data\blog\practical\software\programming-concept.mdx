---
title: Software  - Programming concept
date: '2025-04-11'
tags: ['practical']
draft: false
summary: About Programming concept
---

### **Programming Concepts (C Language - Pattern Printing)**

This section requires practical application of programming.

**Sample Task 1:** Write a C program to print the following two patterns based on a user-inputted number of rows (e.g., if the user enters 4):.

  **Pattern 1:**

  ```
  *
  **
  ***
  ****
  ```

  **Pattern 2:**

  ```
  1
  1 2
  1 2 3
  1 2 3 4
  ```

- **Sample Task 2: Different Patterns**

  - **Task:** Write a C program that takes an integer `n` as input and prints:
    - An inverted right-angled triangle of stars (e.g., for n=4: \***_, _**, \*_, _).

  ```
  ****
  ***
  **
  *
  ```

- **Sample Task 3: Number Sequences**

  - **Task:** Write a C program that takes an integer `n` as input and prints the first `n` numbers of the <PERSON><PERSON><PERSON><PERSON> sequence

  ```
  for n= 8
  0 1 1 2 3 5 8 13

  ```

  - **Skills Tested:** Loops, variable assignment, handling sequences.

- **Sample Task 4: Simple Array Processing**

  - **Task:** Write a C program that asks the user to enter 5 integers, stores them in an array, and then calculates and prints the sum and average of the numbers in the array.
  - **Skills Tested:** Array declaration and access, loops for input and processing, basic arithmetic operations, integer/float division for average.

**Sample Answer for task 1 (C Code):**.

  ```c
  #include <stdio.h>

  int main() {
      int rows;

      // Get input from user
      printf("Enter the number of rows: ");
      scanf("%d", &rows);

      if (rows <= 0) {
          printf("Please enter a positive number for rows.\n");
          return 1; // Indicate error
      }

      // --- Print Pattern 1 ---
      printf("\nPattern 1:\n");
      for (int i = 1; i <= rows; i++) { // Outer loop for rows
          for (int j = 1; j <= i; j++) { // Inner loop for stars in each row
              printf("*");
          }
          printf("\n"); // Move to the next line after each row
      }

      // --- Print Pattern 2 ---
      printf("\nPattern 2:\n");
      for (int i = 1; i <= rows; i++) { // Outer loop for rows
          for (int j = 1; j <= i; j++) { // Inner loop for numbers in each row
              printf("%d ", j); // Print the inner loop counter 'j'
          }
          printf("\n"); // Move to the next line after each row
      }

      return 0; // Indicate successful execution
  }
  ```
