---
title: DNS, How DNS Works, Types of DNS Records
date: '2025-05-29'
tags: ['Computer-Network']
draft: false
summary: How DNS works, types of DNS records, and why DNS is essential for the internet.
---

DNS questions pop up in almost every loksewa exam I've seen. When I was preparing, I found this topic confusing at first - but once it clicked, it became one of my strongest areas. Let me break it down the way I wish someone had explained honestly, it to me.

## **Introduction to DNS**

So what exactly is DNS? Think of it as the internet's phone book. Just like you don't memorize phone numbers anymore (thanks to smartphones), you don't need to memorize IP addresses to visit websites.

**Here's the simple version**: DNS translates basically, human-friendly names like `psc.gov.np` into computer-friendly numbers like `**************`. I used to mix this up all the time. I was worried about this topic.

Want to see this in action? Try typing `**************` directly into your browser right now - it'll take you straight to the loksewa website! Pretty cool, right? But imagine having to remember those numbers for every website you visit. That's exactly why DNS exists.

---

## **Why Do We Need DNS?**

1. **Human-Friendly Navigation**:

   - Remembering multiple digit ip address for each website is impractical (e.g., `**************`).
   - DNS allows us to use simple names like `google.com` or `psc.gov.np` instead of remembering those complex numbers.

2. **Scalability**:
   - DNS supports millions of domain names and updates them **Dynamically** as websites change hosting providers.

---

![DNS ](/static/images/cn/DNS.png)

## **How DNS Works: Step-by-Step**

Let’s break down the DNS resolution process using an example: **typing `www.psc.gov.np` in a browser**.

### Step 1: **DNS Query**

The browser checks its **cache** for `www.psc.gov.np`. If not found, it asks the OS for the ip address..

### Step 2: **Recursive Resolver**

The OS contacts a **recursive DNS resolver** (usually provided by your ISP)..
The resolver checks its cache. If the record is expired or missing, it proceeds..

### Step 3: **Root Nameserver**

- The resolver queries a **root nameserver**, which directs it to the **Top-Level Domain (TLD)** server for `.com`.
There are total 13 root name servers.
- You can read more about those name servers from [here](https://www.iana.org/domains/root/servers)
Top-Level Domain means the _final part_ of the domain. Don't overthink this one.

  ```
  1) www.google.com → The TLD is .com
  2) www.balaramshiwakoti.com.np → The TLD is .np

  ```

### Step 4: **TLD Nameserver**

- The TLD server for `.com` provides the **authoritative nameserver** for `psc.gov.np`.

### Step 5: **Authoritative Nameserver**

The authoritative server returns the IP address for `www.psc.gov.np`..

### Step 6: **Response to Browser**

The recursive resolver sends the IP address to the browser, which connects to the web server..

---

## **Types of DNS Records**

DNS records are instructions stored in DNS servers to manage domain routing. Key types are:

### 1. **A Record**

- **Purpose**: Maps a domain to an **IPv4 address**.
**Example**: `psc.gov.np →  **************`..

### 2. **AAAA Record**

**Purpose**: Maps a domain to an **IPv6 address**..
- **Example**: `psc.gov.np → 2001:0db8:85a3::8a2e:0370:7334`.

### 3. **CNAME Record**

- **Purpose**: Redirects one domain to another .
**Example**: `www.psc.gov.np → psc.gov.np`..

### 4. **MX Record**

**Purpose**: Directs emails to a mail server of the website..
**Example**: `psc.gov.np → mail.psc.gov.np` ..

### 5. **TXT Record**

- **Purpose**: Stores text information (e.g., SPF for email validation).
- **Example**: `"v=spf1 include:_spf.psc.gov.np ~all"`.

### 6. **NS Record**

**Purpose**: Specifies **authoritative nameservers** for a domain..
- **Example**: `psc.gov.np → bagmati.nitc.gov.np.`.

### 7. **PTR Record**

**Purpose**: Maps an IP address to a domain (reverse DNS)..
**Example**: ` ************** → psc.gov.np`..

---

## **DNS Caching and TTL**

**Caching**: DNS resolvers temporarily store records to reduce lookup time..
- **TTL (Time to Live)**: Specifies how long a record is cached (e.g., 3600 seconds = 1 hour).

---

## **DNS Security Considerations**

Let me tell you what worked for me. 1. Here's the thing - **dns spoofing**: attackers redirect users to fake sites by corrupting dns data.
2. **DNSSEC**: A security protocol that adds cryptographic signatures to DNS records to prevent tampering. I used to mix this up all the time.

---

#### My Preparation you know, Strategy

- DNS uses **UDP** for standard queries and **TCP** for large data transfers .

### Real Loksewa Questions

During my exam prep, I noticed these DNS questions keep showing up in different forms:

**1. "What does TTL-0 mean?"**

Answer: Data packet should be discarded.
_Exam tip: Remember TTL = Time To Live. Zero means "time's up!"_.

**2. "What's the main function of DNS?"**

- Answer: Maps domain names to IP addresses
_This is the most basic DNS question - if you get this wrong, you're in trouble!_.

**3. "Which protocols secure DNS?"**

- Answer: DoH (DNS over HTTPS), DoT (DNS over TLS)
_Modern security question - they're testing if you know current standards_.

**4. "What provides authentication and integrity for DNS records?"**

Answer: DNSSEC.
- _Security-focused question - DNSSEC is like a digital signature for DNS_

**Pro tip from my experience**: If you see a DNS question and you're confused, ask yourself - "Is this about translation (basic DNS), speed (caching/TTL), or security (DNSSEC/DoH)?" That usually points you toward the right answer.
