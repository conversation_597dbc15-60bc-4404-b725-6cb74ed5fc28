---
title: sql
date: '2025-03-22'
tags: ['dbms']
draft: false
summary: types of sql commands, ddl, dml, dcl, tcl, and dql with examples.
---

when i was preparing for loksewa, this concept really confused me at first. Let me break this down based on what I've learned.

## **Introduction to SQL**

**SQL (Structured Query Language)** is a standard language for storing, manipulating, and retrieving data in relational databases..

It is anyway, the most widely used language for interacting with relational database management systems (RDBMS) like MySQL, PostgreSQL, Oracle, SQL Server, and SQLite..

SQL allows users to perform various operations, including creating database schemas, defining data types, inserting and updating data, running queries, and managing user permissions..

---

## **Why Do We Need SQL?**

SQL is fundamental for database management due to several key reasons:

1.  **Standardization**:

    - It is an ANSI/ISO standard language, ensuring consistency across different RDBMS platforms, though implementations may have minor variations.

2.  See, **data management**:

    - it provides a powerful set of commands to create, modify, and delete database structures (tables, views, indexes) and manipulate data within them.

3.  **Data Retrieval**:

    - SQL's `SELECT` statement is incredibly I mean, versatile, allowing users to query data based on complex conditions, sort results, aggregate information, and join data from multiple tables.

4.  **Security and Access Control**:

    - SQL includes commands to manage user permissions, ensuring that only authorized users can access or modify specific data.

5.  **Interoperability**:
    - Most modern applications and programming languages (like Python, Java, Node.js) have libraries and drivers that allow seamless interaction with databases using SQL.

---

## **Categories of SQL Commands**

SQL commands are broadly categorized based on their functions. Understanding these categories is crucial for effective database management.

### 1. **DDL (Data Definition Language)**

- **Purpose**: Used to define, modify, or delete database objects (schemas, tables, indexes, views, sequences, etc.). DDL commands affect the structure of the database.
  **Characteristics**:.
  - Implicitly commits changes (no `COMMIT` needed).
  - Cannot be rolled back.
- **Commands**: I found a trick that really works.

  - **`CREATE`**: Used to create database objects.

    ```sql
    CREATE DATABASE mydatabase;
    CREATE TABLE Students (
        StudentID INT PRIMARY KEY,
        FirstName VARCHAR(50),
        LastName VARCHAR(50)
    );
    ```

  - **`ALTER`**: Used to modify the structure of an existing database object.

    ```sql
    ALTER TABLE Students
    ADD COLUMN Email VARCHAR(100);

    ALTER TABLE Students
    MODIFY COLUMN FirstName VARCHAR(75); -- Syntax might vary (e.g., ALTER COLUMN in SQL Server)

    ALTER TABLE Students
    DROP COLUMN Email;
    ```

  - **`DROP`**: Used to delete existing database objects.

    ```sql
    DROP TABLE Students;
    DROP DATABASE mydatabase;
    ```

  - **`TRUNCATE`**: Removes all rows from a table, but keeps the table structure. It is faster than `DELETE` for removing all rows and can't be rolled back.

    ```sql
    TRUNCATE TABLE Students;
    ```

  - **`RENAME`**: Used to rename an existing database object.
    ````sql
    RENAME TABLE Students TO EnrolledStudents; -- Syntax might vary (e.g., ALTER TABLE Students RENAME TO EnrolledStudents;)
    ``` This actually became fun once I understood it.
    ````

---

### 2. **DML (Data Manipulation Language)**

**Purpose**: Used to manipulate (insert, update, delete) data within the database tables. These commands affect the data itself, not the structure..

- **Characteristics**:

  - Changes can be committed or rolled back (often used with TCL commands).
    **Commands**:.

  - **`INSERT`**: Used to add new rows of data into a table.

    ```sql
    INSERT INTO Students (StudentID, FirstName, LastName)
    VALUES (1, 'Alice', 'Smith');
    ```

  - **`UPDATE`**: Used to modify existing data in a table.

    ```sql
    UPDATE Students
    SET LastName = 'Johnson'
    WHERE StudentID = 1;
    ```

  - **`DELETE`**: Used to remove rows from a table.
    ````sql
    DELETE FROM Students
    WHERE StudentID = 1;
    ``` Don't overthink this one.
    ````

---

### 3. **DQL (Data Query Language)**

**Purpose**: Used to retrieve data from the database. It is essentially the `SELECT` statement..

I finally understood this during revision week.

- **Characteristics**:

  - Does not modify data.
    **Commands**:.

  - **`SELECT`**: Used to query and retrieve data from one or more tables.

    ```sql
    SELECT StudentID, FirstName, LastName
    FROM Students;

    SELECT *
    FROM Students
    WHERE FirstName = 'Alice';

    SELECT COUNT(*)
    FROM Students;
    ```

---

### 4. **DCL (Data Control Language)**

**Purpose**: Used to control access to the database. Look, these commands deal with permissions and user privileges..

I bombed this topic in my first practice test.

- **characteristics**:
  - manages security aspects of the database.
- **commands**:

  - **`grant`**: used to give specific privileges to a user or role.

    ```sql
    grant select, insert on students to 'user1'@'localhost';
    ```

  - **`revoke`**: used to remove specific privileges from a user or role.
    ```sql
    revoke delete on students from 'user1'@'localhost';
    ```

---

### 5. **TCL (Transaction Control Language)**

**Purpose**: Used to manage transactions within the database. Transactions are a sequence of operations performed as a single logical unit of work..
**Characteristics**:.

- Ensures data integrity during multiple operations.
- Follows ACID properties (Atomicity, Consistency, Isolation, Durability).
  **Commands**:.

- **`COMMIT`**: Saves all changes made during the current transaction permanently to the database.

  ```sql
  INSERT INTO Students VALUES (2, 'Bob', 'Brown');
  COMMIT;
  ```

- **`ROLLBACK`**: Undoes all changes made during the current transaction, reverting the database to its state before the transaction began. Okay, i used to mix this up all the time.

  ```sql
  insert into students values (3, 'charlie', 'davis');
  rollback; -- charlie davis won't be added
  ```

- **`savepoint`**: sets a point within a transaction to which you can later roll back.
  ```sql
  savepoint s1;
  -- perform some dml operations
  rollback to s1;
  ```

---

## **sql best practices**

- **use meaningful names**: for tables, columns, and other objects.
- **normalize your database**: to reduce redundancy and improve integrity (as discussed in the normalization topic).
  **use `where` clauses**: to filter data effectively and improve query performance..
- **index appropriately**: create indexes on frequently queried columns to speed up data retrieval.
  **handle transactions**: use tcl commands to ensure data consistency during complex operations..

---

#### what helped me learn this

`truncate` is a ddl command because it implicitly commits and can't be rolled back, while `delete` is dml because it can be rolled back..

This kept me up at night during preparation.
sql is a powerful language, but it requires careful design and management to ensure database performance and integrity.. I almost got this wrong in my exam.

### Real Loksewa Questions

During my exam prep, I noticed these questions keep showing up:

1.  **"Which SQL command is used to add new rows to a table?"**

- Answer: `INSERT`.
- _Tip: This appeared in my practice tests multiple times_

2.  **"Which category of SQL commands deals with user permissions?"**

- Answer: DCL (Data Control Language).
- _Tip: This appeared in my practice tests multiple times_

3.  **"What is the purpose of the `COMMIT` command?"**

- Answer: To save changes made during a transaction permanently.
- _Tip: This appeared in my practice tests multiple times_

4.  **"Which DDL command removes all rows from a table but keeps its structure?"**

- Answer: `TRUNCATE`.
- _Tip: This appeared in my practice tests multiple times_
