---
title: 'Types of Addressing Modes in Computer Architecture'
date: '2025-04-17'
tags: ['COA']
draft: false
summary: Comprehensive guide to different addressing modes including immediate, direct, indirect, register, and indexed addressing with examples and applications.
---

Addressing modes confused me initially during my loksewa preparation because there are so many types, and each has its specific use case. But once I understood the logic behind each mode, it became much clearer. Here's my take on it.

## **Introduction to Addressing Modes**

**Addressing well, modes** specify how the operand of an instruction is located in memory or registers. They define the method used to calculate the effective address of the operand. Honestly, this took me forever to get.

_Expect this in your loksewa - it's a common topic._

**Here's how I mean, I understand it**: Think of addressing modes like different ways to give directions to someone's house - you can give the exact address, tell them to look in a specific drawer, or give them a reference point and an offset.

## **Types of Addressing Modes**

### **1. Immediate Addressing Mode**

**Definition**: The operand is specified directly in the instruction itself.

My teacher explained this three times before I got it.

**Syntax**: `MOV AX, #5` (Move immediate value 5 to register AX)

**Characteristics**:
Operand is part of instruction.
No memory access required for operand.

- Fastest addressing mode
- Limited to small constant values

**Example**:

```assembly
MOV AX, #100    ; Load immediate value 100 into AX
ADD BX, #25     ; Add immediate value 25 to BX
```

**Advantages**:
Very fast execution.

- No additional memory access
  Simple implementation.

**Disadvantages**:

- Limited to constants
  Increases instruction size.
- Cannot modify the operand

**Use Cases**:

- Loading constants
  Initializing registers.
  Counter increments.

### **2. Direct Addressing Mode**

**Definition**: The instruction contains the actual memory address of the operand.

**Syntax**: `MOV AX, [1000]` (Move content of memory location 1000 to AX)

**Characteristics**:

- Address is specified directly in instruction
- One memory access required
- Simple address calculation

**Example**:

```assembly
MOV AX, [500]   ; Load content of memory address 500 into AX
ADD BX, [750]   ; Add content of memory address 750 to BX
```

**Effective Address**: EA = Address field of instruction

**Advantages**:
Simple and straightforward.
Direct access to memory.

- Easy to understand

**Disadvantages**:

- Limited address space (depends on instruction size)
  No flexibility in address calculation.
- Relocatability issues

### **3. Indirect Addressing Mode**

**Definition**: The instruction contains the address of a memory location that contains the actual address of the operand.

**Syntax**: `MOV AX, @[500]` (Move content from address stored at location 500)

**Characteristics**:
Two memory accesses required.

- Address is stored in memory
  More flexible than direct addressing.

**Example**:

```assembly
; If memory location 500 contains value 1000
; And memory location 1000 contains value 25
MOV AX, @[500]  ; AX gets value 25
```

**Effective Address**: EA = Content of address field

**Advantages**:

- Larger address space
- Supports dynamic addressing
  Good for pointer implementation.

**Disadvantages**:
Slower (two memory accesses).

- More complex
- Potential for address errors

**Use Cases**:

- Pointer operations
  Dynamic memory allocation.
  Subroutine calls.

### **4. Register Addressing Mode**

**Definition**: The operand is located in a processor register.

I finally understood this during revision week.

**Syntax**: `MOV AX, BX` (Move content of register BX to register AX)

**Characteristics**:

- Operand is in CPU register
- No memory access required
  Very fast execution.

**Example**:

```assembly
MOV AX, BX      ; Copy content of BX to AX
ADD CX, DX      ; Add content of DX to CX
```

**Advantages**:

- Fastest addressing mode
- No memory access overhead
- Simple implementation

**Disadvantages**:

- Limited number of registers
- Only works with register operands

### **5. Register Indirect Addressing Mode**

**Definition**: The register contains the address of the operand in memory.

I made flashcards for this - that's how I remembered it.

**Syntax**: `MOV AX, [BX]` (Move content from memory address stored in BX)

**Characteristics**:

- Register holds memory address
- One memory access required
- Flexible addressing

**Example**:

```assembly
MOV BX, #1000   ; Load address 1000 into BX
MOV AX, [BX]    ; Load content from memory address 1000 into AX
```

**Effective Address**: EA = Content of register

**Advantages**:

- Fast address calculation
- Dynamic addressing capability
- Good for array processing

This was my weak point during prep.

**Disadvantages**:

- Uses a register for addressing
- One memory access required

### **6. Indexed Addressing Mode**

**Definition**: The effective address is calculated by adding a constant offset to the content of an index register. Honestly, this took me forever to get.

**Syntax**: `MOV AX, [BX+10]` (Move content from address BX+10)

**Characteristics**:

- Combines base address with offset
- Good for array and table access
- Flexible addressing

**Example**:

```assembly
MOV BX, #1000   ; Base address
MOV AX, [BX+5]  ; Access element at offset 5 (address 1005)
```

Don't overthink this one.

**Effective Address**: EA = Content of register + Offset

**Advantages**:

- Excellent for arrays
- Flexible addressing
- Supports data structures

**Disadvantages**:

- More complex address calculation
- Limited offset range

**Use Cases**:

- Array element access
- Table lookups
- Data structure traversal

### **7. Based Addressing Mode**

**Definition**: Similar to indexed addressing, but uses a base register plus displacement.

**Syntax**: `MOV AX, [BP+8]` (Move content from address BP+8)

**Characteristics**:

- Base register + displacement
- Common in stack operations
- Good for local variable access

**Example**:

```assembly
; Accessing local variables on stack
MOV BP, SP      ; Set base pointer
MOV AX, [BP+4]  ; Access parameter
MOV BX, [BP-2]  ; Access local variable
```

I remember struggling with this part. This frustrated me so much!

### **8. Auto-increment/Auto-decrement Addressing**

**Definition**: Register content is automatically incremented or decremented after/before use.

**Types**:

- **Pre-increment**: `MOV AX, [++BX]`
- **Post-increment**: `MOV AX, [BX++]`
- **Pre-decrement**: `MOV AX, [--BX]`
- **Post-decrement**: `MOV AX, [BX--]`

This frustrated me so much!

**Use Cases**:

- Stack operations
- Array traversal
- String processing

### **9. Relative Addressing Mode**

**Definition**: The effective address is calculated relative to the current instruction address.

This was my favorite topic by the end.

**Syntax**: `JMP +10` (Jump to instruction 10 positions ahead)

**Characteristics**:

- Address relative to PC (Program Counter)
- Good for position-independent code
- Common in branch instructions

**Effective Address**: EA = PC + Offset

**Use Cases**:

- Branch instructions
- Loop control
- Position-independent code

## **Comparison of Addressing Modes**

But here's where it gets interesting.

| Mode      | Memory Accesses | Speed   | Flexibility | Use Case         |
| --------- | --------------- | ------- | ----------- | ---------------- |
| Immediate | 0               | Fastest | Low         | Constants        |
| Register  | 0               | Fastest | Low         | Register ops     |
| Direct    | 1               | Fast    | Medium      | Simple variables |
| Indirect  | 2               | Slow    | High        | Pointers         |
| Indexed   | 1               | Medium  | High        | Arrays           |
| Relative  | 1               | Medium  | Medium      | Branches         |

I was worried about this topic.

## **Effective Address Calculation Examples**

### **Example 1: Array Access**

```assembly
; Array base address = 1000, element size = 2 bytes
MOV BX, #1000   ; Base address
MOV SI, #3      ; Index (3rd element)
SHL SI, 1       ; Multiply by 2 (element size)
MOV AX, [BX+SI] ; Access array[3] at address 1006
```

### **Example 2: Stack Operations**

```assembly
; Stack pointer operations
PUSH AX         ; [--SP] = AX (pre-decrement)
POP BX          ; BX = [SP++] (post-increment)
```

## **Performance Considerations**

### **Speed Ranking** (Fastest to Slowest):

1. Register/Immediate
2. Direct/Indexed/Based
3. Register Indirect
4. Indirect

### **Memory Access Count**:

- **0 accesses**: Register, Immediate
- **1 access**: Direct, Indexed, Based, Register Indirect
- **2 accesses**: Indirect

## **My Preparation Strategy**

- **Remember the hierarchy**: Immediate/Register (fastest) → Direct/Indexed → Indirect (slowest)
- **Memory accesses matter**: More accesses = slower execution
- **Use cases**: Immediate (constants), Indexed (arrays), Indirect (pointers)
- **Effective address**: Always know how to calculate EA for each mode

### **Questions from My Experience**

During my exam prep, I noticed these questions keep showing up:

1. **"Which addressing mode is fastest and why?"**

   - Answer: Register and Immediate modes because they don't require memory access
   - _Tip: Remember 0 memory accesses = fastest_

   Don't overthink this one.

2. **"Calculate effective address for MOV AX, [BX+10] if BX contains 1000"**

   - Answer: EA = 1000 + 10 = 1010
   - _Tip: Practice these EA calculations_

3. **"Which addressing mode requires two memory accesses?"**

   - Answer: Indirect addressing mode
   - _Tip: One access to get address, another to get data_

4. **"What addressing mode is best for array processing?"**

   - Answer: Indexed addressing mode
   - _Tip: Base + offset is perfect for arrays_

   This confused me for weeks!

5. **"In immediate addressing, where is the operand located?"**
   - Answer: In the instruction itself
   - _Tip: "Immediate" means right now, in the instruction_

**Pro tip from my experience**: When solving addressing mode problems, always count the number of memory accesses required. This helps you determine the relative speed and complexity of each mode. Also, think about what each mode is optimized for - constants, variables, arrays, or pointers.
