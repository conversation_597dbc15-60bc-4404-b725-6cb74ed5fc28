---
title: Networking basic commands
date: '2025-03-21'
tags: ['Miscellaneous']
draft: false
summary: You will have idea about Networking basic commands .
---

## **1) Layer 3 information : IP / logical address**

```
ipconfig

```

## **2) Layer 2 information : MAC / physical address**

```
ipconfig/all

```

## **3) Get mac addres of wifi , ethernet , bluetooth**

```
getmac/v

```

## **4) Get new ip address : Release and renew ip address**

```
ipconfig/release (Release current ip address)
ipconfig/renew (Reach our DHCP and get fresh new ip address)

```

## **5) Get list of all the ip address of domain names in the computer**

```
ipconfig/displaydns
ipconfig/flushdns ( Remove all the dns from the computer)
```

## 6) Query the domain name and get info about IP addresses .

```
nslookup

```

## 7) Test the reachability between a device and a destination IP address or hostname

```
ping

```

## 8) To trace the route (hops) that packets take to reach a destination IP address or domain name

```
tracert

```

![traceroute ](/static/images/Miscellaneous/tracert-google.png)
