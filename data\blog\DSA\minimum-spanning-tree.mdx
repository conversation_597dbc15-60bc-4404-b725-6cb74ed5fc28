---
title: 'Minimum Spanning Tree - Kruskal and Prim Algorithms'
date: '2025-05-01'
tags: ['DSA']
draft: false
summary: Comprehensive guide to Minimum Spanning Tree algorithms including Kruskal and Prim algorithms with implementations, complexity analysis, and real-world applications.
---

Minimum Spanning Tree was initially one of those graph problems that seemed abstract to me during DSA preparation. I kept thinking, "Why do we need to connect all vertices with minimum cost?" But once I understood real-world applications like network design, circuit layout, and clustering, the importance became clear. Let me share what I've learned about these fundamental graph algorithms.

## **Introduction to Minimum Spanning Tree**

When I first encountered MST problems, I was confused about why we needed special algorithms. But then I realized that finding the minimum cost to connect all vertices in a graph is a fundamental optimization problem with countless practical applications.

**Minimum Spanning Tree (MST)** is a subset of edges in a connected, weighted graph that connects all vertices with the minimum total edge weight, without forming any cycles.

## **MST Properties and Definitions**

### **Spanning Tree Properties**

For a graph G = (V, E) with n vertices:

**Spanning Tree characteristics:**
- Contains exactly n-1 edges
- Connects all n vertices
- Contains no cycles (acyclic)
- Removing any edge disconnects the graph
- Adding any edge creates exactly one cycle

### **Minimum Spanning Tree Properties**

**MST characteristics:**
- Among all possible spanning trees, has minimum total weight
- May not be unique (multiple MSTs with same weight possible)
- Unique if all edge weights are distinct
- Always exists for connected graphs

**Example:**
```
Graph with 4 vertices:
    A ---- 1 ---- B
    |             |
    2             3
    |             |
    C ---- 4 ---- D

Possible spanning trees:
1. Edges: AB(1), AC(2), BD(3) - Total: 6
2. Edges: AB(1), AC(2), CD(4) - Total: 7
3. Edges: AB(1), BD(3), CD(4) - Total: 8

MST: AB(1), AC(2), BD(3) with total weight 6
```

I remember drawing all possible spanning trees for small graphs to understand this concept!

## **Kruskal's Algorithm**

### **Algorithm Overview**

**Kruskal's approach:** Greedy algorithm that builds MST by adding edges in order of increasing weight, avoiding cycles.

**Key insight:** Always choose the minimum weight edge that doesn't create a cycle.

### **Algorithm Steps**

1. **Sort all edges** by weight in ascending order
2. **Initialize** each vertex as a separate component
3. **For each edge** in sorted order:
   - If edge connects different components, add to MST
   - Union the components
4. **Stop** when MST has n-1 edges

### **Implementation**

```python
class UnionFind:
    """Disjoint Set Union (DSU) for cycle detection"""
    def __init__(self, n):
        self.parent = list(range(n))
        self.rank = [0] * n
        self.components = n
    
    def find(self, x):
        """Find root with path compression"""
        if self.parent[x] != x:
            self.parent[x] = self.find(self.parent[x])
        return self.parent[x]
    
    def union(self, x, y):
        """Union by rank"""
        root_x = self.find(x)
        root_y = self.find(y)
        
        if root_x == root_y:
            return False  # Already in same component
        
        # Union by rank
        if self.rank[root_x] < self.rank[root_y]:
            self.parent[root_x] = root_y
        elif self.rank[root_x] > self.rank[root_y]:
            self.parent[root_y] = root_x
        else:
            self.parent[root_y] = root_x
            self.rank[root_x] += 1
        
        self.components -= 1
        return True

def kruskal_mst(vertices, edges):
    """
    Find MST using Kruskal's algorithm
    
    Args:
        vertices: number of vertices (0 to vertices-1)
        edges: list of (weight, u, v) tuples
    
    Returns:
        list of edges in MST and total weight
    """
    # Sort edges by weight
    edges.sort()
    
    # Initialize Union-Find
    uf = UnionFind(vertices)
    mst_edges = []
    total_weight = 0
    
    for weight, u, v in edges:
        # If adding this edge doesn't create cycle
        if uf.union(u, v):
            mst_edges.append((u, v, weight))
            total_weight += weight
            
            # MST complete when we have n-1 edges
            if len(mst_edges) == vertices - 1:
                break
    
    return mst_edges, total_weight

# Example usage
vertices = 4
edges = [
    (1, 0, 1),  # A-B with weight 1
    (2, 0, 2),  # A-C with weight 2
    (3, 1, 3),  # B-D with weight 3
    (4, 2, 3),  # C-D with weight 4
]

mst_edges, total_weight = kruskal_mst(vertices, edges)
print(f"MST edges: {mst_edges}")
print(f"Total weight: {total_weight}")
```

### **Kruskal Example Walkthrough**

**Graph:**
```
    A ---- 1 ---- B
    |             |
    2             3
    |             |
    C ---- 4 ---- D
```

**Step-by-step execution:**

**Initial state:**
- Sorted edges: [(1,A,B), (2,A,C), (3,B,D), (4,C,D)]
- Components: {A}, {B}, {C}, {D}

**Step 1:** Process edge (1,A,B)
- A and B in different components ✓
- Add to MST: [(A,B,1)]
- Union A and B: {A,B}, {C}, {D}

**Step 2:** Process edge (2,A,C)
- A and C in different components ✓
- Add to MST: [(A,B,1), (A,C,2)]
- Union A and C: {A,B,C}, {D}

**Step 3:** Process edge (3,B,D)
- B and D in different components ✓
- Add to MST: [(A,B,1), (A,C,2), (B,D,3)]
- Union B and D: {A,B,C,D}

**Result:** MST with edges [(A,B,1), (A,C,2), (B,D,3)], total weight = 6

## **Prim's Algorithm**

### **Algorithm Overview**

**Prim's approach:** Greedy algorithm that grows MST one vertex at a time, always adding the minimum weight edge that connects the current tree to a new vertex.

**Key insight:** Start with any vertex and keep adding the cheapest edge to expand the tree.

### **Algorithm Steps**

1. **Start** with any vertex in the MST
2. **Repeat** until all vertices are included:
   - Find minimum weight edge connecting MST to non-MST vertex
   - Add this edge and vertex to MST
3. **Result** is the complete MST

### **Implementation**

```python
import heapq
from collections import defaultdict

def prim_mst(graph, start_vertex=0):
    """
    Find MST using Prim's algorithm
    
    Args:
        graph: adjacency list representation {vertex: [(neighbor, weight), ...]}
        start_vertex: vertex to start from
    
    Returns:
        list of edges in MST and total weight
    """
    mst_edges = []
    total_weight = 0
    visited = set()
    
    # Priority queue: (weight, from_vertex, to_vertex)
    min_heap = []
    
    # Start with the given vertex
    visited.add(start_vertex)
    
    # Add all edges from start vertex to heap
    for neighbor, weight in graph[start_vertex]:
        heapq.heappush(min_heap, (weight, start_vertex, neighbor))
    
    while min_heap and len(visited) < len(graph):
        weight, from_vertex, to_vertex = heapq.heappop(min_heap)
        
        # Skip if both vertices already in MST
        if to_vertex in visited:
            continue
        
        # Add edge to MST
        mst_edges.append((from_vertex, to_vertex, weight))
        total_weight += weight
        visited.add(to_vertex)
        
        # Add all edges from new vertex to heap
        for neighbor, edge_weight in graph[to_vertex]:
            if neighbor not in visited:
                heapq.heappush(min_heap, (edge_weight, to_vertex, neighbor))
    
    return mst_edges, total_weight

# Alternative implementation using adjacency matrix
def prim_mst_matrix(adj_matrix):
    """Prim's algorithm using adjacency matrix"""
    n = len(adj_matrix)
    INF = float('inf')
    
    # Arrays to track MST construction
    key = [INF] * n      # Minimum weight to connect vertex to MST
    parent = [-1] * n    # Parent in MST
    in_mst = [False] * n # Whether vertex is in MST
    
    # Start with vertex 0
    key[0] = 0
    
    mst_edges = []
    total_weight = 0
    
    for _ in range(n):
        # Find minimum key vertex not yet in MST
        min_key = INF
        min_vertex = -1
        
        for v in range(n):
            if not in_mst[v] and key[v] < min_key:
                min_key = key[v]
                min_vertex = v
        
        # Add vertex to MST
        in_mst[min_vertex] = True
        
        if parent[min_vertex] != -1:
            mst_edges.append((parent[min_vertex], min_vertex, key[min_vertex]))
            total_weight += key[min_vertex]
        
        # Update keys of adjacent vertices
        for v in range(n):
            if (adj_matrix[min_vertex][v] != 0 and 
                not in_mst[v] and 
                adj_matrix[min_vertex][v] < key[v]):
                key[v] = adj_matrix[min_vertex][v]
                parent[v] = min_vertex
    
    return mst_edges, total_weight

# Example usage
graph = {
    0: [(1, 1), (2, 2)],        # A connects to B(1), C(2)
    1: [(0, 1), (3, 3)],        # B connects to A(1), D(3)
    2: [(0, 2), (3, 4)],        # C connects to A(2), D(4)
    3: [(1, 3), (2, 4)]         # D connects to B(3), C(4)
}

mst_edges, total_weight = prim_mst(graph, 0)
print(f"MST edges: {mst_edges}")
print(f"Total weight: {total_weight}")
```

### **Prim Example Walkthrough**

**Graph:** Same as Kruskal example

**Step-by-step execution starting from A:**

**Initial state:**
- MST: {A}
- Available edges: [(A,B,1), (A,C,2)]
- Min heap: [(1,A,B), (2,A,C)]

**Step 1:** Add edge (A,B,1)
- MST: {A,B}
- New available edges: [(B,D,3)]
- Min heap: [(2,A,C), (3,B,D)]

**Step 2:** Add edge (A,C,2)
- MST: {A,B,C}
- New available edges: [(C,D,4)]
- Min heap: [(3,B,D), (4,C,D)]

**Step 3:** Add edge (B,D,3)
- MST: {A,B,C,D}
- All vertices included

**Result:** Same MST as Kruskal with total weight = 6

## **Algorithm Comparison**

### **Time Complexity**

| Algorithm | Time Complexity | Space Complexity |
|-----------|----------------|------------------|
| Kruskal | O(E log E) | O(V) |
| Prim (Binary Heap) | O(E log V) | O(V) |
| Prim (Fibonacci Heap) | O(E + V log V) | O(V) |
| Prim (Adjacency Matrix) | O(V²) | O(V) |

Where V = vertices, E = edges

### **When to Use Each Algorithm**

**Use Kruskal when:**
- Graph is sparse (E << V²)
- Edges are already sorted
- Working with edge list representation
- Need to find MST incrementally

**Use Prim when:**
- Graph is dense (E ≈ V²)
- Working with adjacency matrix/list
- Need to start from specific vertex
- Memory is limited

I found that understanding these trade-offs was crucial for choosing the right algorithm!

## **Applications of MST**

### **Network Design**

**Telecommunications:**
- Design minimum cost network to connect all cities
- Minimize cable laying costs
- Ensure connectivity with least infrastructure

**Computer Networks:**
- Design LAN topology
- Minimize network latency
- Reduce infrastructure costs

### **Circuit Design**

**VLSI Design:**
- Connect components with minimum wire length
- Reduce signal delay
- Minimize manufacturing cost

**Printed Circuit Boards:**
- Route connections efficiently
- Minimize board size
- Reduce electromagnetic interference

### **Clustering and Approximation**

**Data Clustering:**
- Build hierarchical clusters
- Find natural groupings in data
- Approximate solutions to TSP

**Image Segmentation:**
- Segment images based on pixel similarity
- Computer vision applications
- Medical image analysis

### **Real-world Example**

**Problem:** Connect 5 cities with minimum road construction cost

```python
# Cities: A, B, C, D, E
# Construction costs (in millions):
cities = ['A', 'B', 'C', 'D', 'E']
costs = [
    (10, 'A', 'B'),  # A-B: $10M
    (15, 'A', 'C'),  # A-C: $15M
    (20, 'A', 'D'),  # A-D: $20M
    (25, 'B', 'C'),  # B-C: $25M
    (30, 'B', 'E'),  # B-E: $30M
    (35, 'C', 'D'),  # C-D: $35M
    (40, 'C', 'E'),  # C-E: $40M
    (45, 'D', 'E'),  # D-E: $45M
]

# Convert to vertex indices
city_to_index = {city: i for i, city in enumerate(cities)}
edges = [(cost, city_to_index[u], city_to_index[v]) for cost, u, v in costs]

mst_edges, total_cost = kruskal_mst(5, edges)

print("Minimum cost road network:")
for u, v, cost in mst_edges:
    print(f"  {cities[u]} - {cities[v]}: ${cost}M")
print(f"Total cost: ${total_cost}M")
```

## **Advanced Topics**

### **MST Variants**

**Minimum Bottleneck Spanning Tree:**
- Minimize the maximum edge weight
- Different from MST but related
- Applications in network reliability

**Degree-Constrained MST:**
- Limit maximum degree of vertices
- NP-hard problem
- Approximation algorithms exist

**Dynamic MST:**
- Handle edge insertions/deletions
- Maintain MST efficiently
- Advanced data structures required

### **MST Properties and Theorems**

**Cut Property:**
- For any cut, minimum weight crossing edge is in some MST
- Foundation for correctness proofs

**Cycle Property:**
- For any cycle, maximum weight edge is not in MST
- Used in optimization algorithms

**Uniqueness:**
- MST is unique if all edge weights are distinct
- Multiple MSTs possible with equal weights

## **Implementation Optimizations**

### **Kruskal Optimizations**

```python
def optimized_kruskal(vertices, edges):
    """Optimized Kruskal with early termination"""
    edges.sort()  # Can use counting sort if weights are small
    
    uf = UnionFind(vertices)
    mst_edges = []
    total_weight = 0
    
    for weight, u, v in edges:
        if uf.union(u, v):
            mst_edges.append((u, v, weight))
            total_weight += weight
            
            # Early termination
            if len(mst_edges) == vertices - 1:
                break
    
    return mst_edges, total_weight
```

### **Prim Optimizations**

```python
def optimized_prim(graph, start=0):
    """Prim with efficient priority queue operations"""
    import heapq
    
    n = len(graph)
    visited = [False] * n
    min_heap = [(0, start, -1)]  # (weight, vertex, parent)
    
    mst_edges = []
    total_weight = 0
    
    while min_heap:
        weight, u, parent = heapq.heappop(min_heap)
        
        if visited[u]:
            continue
        
        visited[u] = True
        if parent != -1:
            mst_edges.append((parent, u, weight))
            total_weight += weight
        
        for v, edge_weight in graph[u]:
            if not visited[v]:
                heapq.heappush(min_heap, (edge_weight, v, u))
    
    return mst_edges, total_weight
```

## **Common Pitfalls and Tips**

### **1. Graph Connectivity**
```python
def check_connectivity(vertices, edges):
    """Ensure graph is connected before finding MST"""
    if len(edges) < vertices - 1:
        return False
    
    # Use DFS/BFS to check connectivity
    # Implementation depends on graph representation
    return True
```

### **2. Handling Duplicate Edges**
```python
def remove_duplicates(edges):
    """Remove duplicate edges, keep minimum weight"""
    edge_dict = {}
    
    for weight, u, v in edges:
        # Ensure consistent edge representation
        if u > v:
            u, v = v, u
        
        key = (u, v)
        if key not in edge_dict or weight < edge_dict[key]:
            edge_dict[key] = weight
    
    return [(weight, u, v) for (u, v), weight in edge_dict.items()]
```

### **3. Integer Overflow**
```python
def safe_mst(vertices, edges):
    """Handle large weights safely"""
    # Use appropriate data types
    # Check for overflow in total weight calculation
    total_weight = 0
    mst_edges = []
    
    for edge in mst_edges:
        if total_weight > float('inf') - edge[2]:
            raise ValueError("Weight overflow")
        total_weight += edge[2]
    
    return mst_edges, total_weight
```

## **Practice Problems**

Here are some problems I found helpful:

1. **Connecting Cities** - Basic MST application
2. **Network Delay Time** - Modified MST problem
3. **Min Cost to Connect All Points** - Coordinate-based MST
4. **Optimize Water Distribution** - Real-world MST application
5. **Critical Connections** - Bridge finding (related to MST)

## **Conclusion**

Minimum Spanning Tree algorithms are fundamental tools for optimization problems involving connectivity. Key takeaways:

**Core Concepts:**
- MST connects all vertices with minimum total weight
- Greedy algorithms work due to optimal substructure
- Two main approaches: edge-based (Kruskal) and vertex-based (Prim)

**Algorithm Choice:**
- Kruskal: Better for sparse graphs, edge-list representation
- Prim: Better for dense graphs, adjacency representation
- Both achieve same result with different approaches

**Applications:**
- Network design and optimization
- Circuit layout and VLSI design
- Clustering and approximation algorithms
- Infrastructure planning

**For loksewa preparation, focus on:**
- Understanding both Kruskal and Prim algorithms
- Implementing Union-Find for cycle detection
- Analyzing time and space complexity
- Recognizing MST applications in real problems
- Comparing algorithm trade-offs

**Remember:** MST algorithms demonstrate the power of greedy approaches when optimal substructure exists. The key insight is that locally optimal choices (minimum weight edges) lead to globally optimal solutions when cycles are avoided.

Understanding MST algorithms provides a foundation for more advanced graph algorithms and optimization techniques!
