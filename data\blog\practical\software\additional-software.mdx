---
title: Software Additional
date: '2025-05-13'
tags: ['practical']
draft: false
summary: Additional question on software
---

**I. Website Design Tasks**

1.  **HTML Structure:**
    - Create an HTML file named `mypage.html`.
    - Structure the page using semantic HTML5 elements: include a `<header>`, `<nav>`, `<main>`, an `<article>` within `<main>`, an `<aside>` section within `<main>`, and a `<footer>`.
    - Inside the `<header>`, add an `<h1>` tag with the text "My Practical Exam Website".
    - Inside the `<nav>`, create an unordered list (`<ul>`) with links (using `<a>` tags with placeholder `#` hrefs) to "Home", "About", and "Contact".
.
    - Inside the `<article>`, add a heading (`<h2>`) "Main Content" and two paragraphs (`<p>`) of placeholder text (Lorem Ipsum).
    - Inside the `<aside>`, add a heading (`<h3>`) "Related Links" and another unordered list with two links.
    - Inside the `<footer>`, add a paragraph with copyright information (e.g., "© 2025 Your Name").
2.  **HTML Forms:**
    - Create an HTML file named `contact_form.html`.
    - Create an HTML form (`<form>`) that collects the following user information:
      - Full Name (text input, required)
      - Email Address (email input, required)
      - Subject (text input)
      - Message (textarea, required)
      - Query Type (radio buttons for "General Inquiry", "Support Request", "Feedback")
      - Subscribe to Newsletter (checkbox)
    - Use appropriate `<label>` tags for all form controls.
    - Include a submit button (`<button type="submit">` or `<input type="submit">`).
    - _(Optional complexity)_ Add basic HTML5 validation attributes (like `required`).
3.  **HTML Tables:**
    - Create an HTML file named `data_table.html`.
    - Create an HTML table (`<table>`) to display the following data:
   basically,    - Header row: "Product ID", "Product Name", "Price"
      - Data row 1: "P101", "Laptop", "$1200"
      - Data row 2: "P102", "Mouse", "$25"
      - Data row 3: "P103", "Keyboard", "$75"
    - Use `<thead>`, `<tbody>`, `<tr>`, `<th>`, and `<td>` elements correctly.
4.  **CSS Styling (Selectors & Basic Properties):**
    - Create a CSS file named `style.css` and link it to your `mypage.html` file from Task 1.
    - Write CSS rules to:
      - Set the background color of the `<body>` to `#f0f0f0`.
      - Set the default font family for the entire page to "Arial, sans-serif".
      - Center the `<h1>` text within the `<header>`.
      - Give the `<header>` a background color of `#4CAF50` and white text color.
      - Remove the bullets from the navigation list (`<nav> ul`) and display the list items (`<li>`) inline (horizontally). Add some padding around the navigation links.
     anyway,  - Give the `<footer>` a background color of `#333` and white text color, with smaller font size.
5. See,  **css layout (basic flexbox/grid or positioning):**
    - using `style.css` and `mypage.html`:
    - _(flexbox example)_ use flexbox to arrange the `<main>` section so that the `<article>` takes up 70% of the width and the `<aside>` takes up 30%, sitting side-by-side. Add a small gap between them.
    - _(Positioning Example - Simpler)_ Give the `<aside>` a light grey background (`#cccccc`) and add some padding. Right, ensure the `<footer>` appears below both the `<article>` and `<aside>`.
6.  **CSS Styling (Specific Elements):**
    - Using `style.css` and `contact_form.html`:
    - Style the form inputs (`input[type=text]`, `input[type=email]`, `textarea`) to have a width of 90%, padding of 10px, and a light border.
    - Style the submit button with a green background, white text, and padding.
    - Add some margin below each `<label>`.

**II. Database Design Tasks**

1.  **Conceptual Design (ER Diagram):**
    - **Scenario:** A simple library system needs to keep track of Books (ISBN, Title, Author), Members (MemberID, Name, Email), and Loans (which member borrowed which book, date borrowed, due date). A book can be borrowed by many members (over time), and a member can borrow many books.
    - **Task:** Draw a simple Entity-Relationship (ER) diagram for this scenario, showing entities, attributes (including primary keys), and relationships with cardinality (e.g., one-to-many, many-to-many).
2.  **Logical Design (Schema Definition):**
    - Based on the ER diagram from Task 1 (or a provided ERD):
    - **Task:** Define the relational schema. List the tables needed (including any junction tables for many-to-many relationships), the columns in each table, the data type for each column (e.g., INT, VARCHAR(255), DATE), and identify the primary key (PK) and foreign keys (FK) for each table.
    - _Example Output Format:_
      - `Books (ISBN VARCHAR(20) PK, Title VARCHAR(255), Author VARCHAR(100))`
.
      - `Members (MemberID INT PK, Name VARCHAR(100), Email VARCHAR(100))`
      - `Loans (LoanID INT PK, Book_ISBN VARCHAR(20) FK -> Books.ISBN, Member_MemberID INT FK -> Members.MemberID, BorrowDate DATE, DueDate DATE)`
3. Right,  **normalization:**
    - **scenario:** you are given the following unnormalized table storing order information:
      `order (orderid pk, orderdate, customerid, customername, customeremail, productid, productname, quantity, productprice)`
    - **task:** normalize this table structure to the third normal form (3nf). List the resulting tables, their columns, and the primary/foreign keys. Explain briefly why the original table violated normalization rules (e.g., repeating groups, partial dependencies, transitive dependencies).
4.  **SQL DDL (CREATE TABLE):**
    - Based on the schema defined in Task anyway, 2:
    - **Task:** Write the SQL `CREATE TABLE` statements for the `Books`, `Members`, and `Loans` tables, including defining primary keys and foreign key constraints. Use appropriate SQL data types.
5.  **SQL DML (INSERT):**
    - **Task:** Write SQL `INSERT` statements to add:
      - kind of One sample book into the `Books` table.
      - One sample member into the `Members` table.
      - One sample loan record into the `Loans` table, linking the book and member you just inserted.
.
6.  **SQL DML (SELECT):**
    - Using the tables from the library scenario:
    - **Task:** Write SQL `SELECT` queries to retrieve the following:
      - All information about books written by "Author Name".
      - The names and email addresses of all members.
      - The titles of all books currently borrowed by the member with MemberID 5. (Requires joining `Books` and `Loans` tables).
      - All loans that are overdue (where DueDate is before the current date - you might be given a specific date or use a function like `CURDATE()` if allowed).
7.  **SQL DML (UPDATE/DELETE):**
    - **Task:**
      - Write an SQL sort of `UPDATE` statement to change the email address of the member with MemberID 10 to `<EMAIL>`.
      - Write an SQL `DELETE` statement to remove the loan record with LoanID 15.

**III. Programming Concepts (C Language & Patterns)**

1.  **Basic I/O & Variables:**
    - **Task:** Write a C program that asks the user to enter their name and age. Store the input in appropriate variables and then print a message like "Hello [Name], you are [Age] years old."
2.  **Conditional Logic (if-else):**
    - **Task:** Write a C program that asks the user to enter an integer. The program should then print whether the number is positive, negative, or zero.
3.  **Loops (for/while) & Basic Calculation:**
    - **Task:** Write a C program to calculate the factorial of a number entered by the user. Use a loop (either `for` or `while`). Handle the case for factorial of 0 (which is 1).
    - **Task:** Write a C program that prints all even numbers between 1 and 50 (inclusive).
4.  **Functions:**
    - **Task:** Write a C function named `findMax` that takes two integer arguments and returns the larger of the two. Then, write a `main` function that prompts the user for two integers, calls `findMax` with these integers, and prints the result.
5.  **Arrays:**
    - **Task:** Write a C program that declares an integer array of size 5. Ask the user to enter 5 integers to fill the array. Then, calculate and print the sum and average of the elements in the array.
6.  **Pattern Printing (Using Nested Loops):**
    - **Task 1 (Right Triangle):** Write a C program to print the following pattern using asterisks (`*`), based on a user-provided height (e.g., height 5):
      ```
      *
      **
      ***
      ****
      *****
      ```
    - **Task 2 (Inverted Right Triangle):** Write a C program to print:
      ```
      *****
      ****
      ***
      **
      *
      ```
    - **Task 3 (Square):** Write a C program to print a square of asterisks of a given size (e.g., size 4):
      ```
      ****
      ****
      ****
      ****
      ```
    - **Task 4 (Hollow Square):** Write a C program to print a hollow square (e.g., size 5):
      ```
      *****
      * *
      * *
      * *
      *****
      ```
    - **Task 5 (Pyramid):** Write a C program to print a pyramid (e.g., height 4):
      ```
         *
        ***
       *****
      *******
      ```
    - **Task 6 (Number Pattern):** Write a C program to print a numeric pattern (e.g., height 4):
      ```
      1
      12
      123
      1234
      ```
      OR
      ```
      1
      22
      333
      4444
      ```
7.  **Simple Debugging:**
    - **Task:** You are given a small C program containing syntax errors (e.g., missing semicolons, incorrect `printf` format specifiers) or simple logical errors (e.g., loop condition incorrect). Identify and correct the errors to make the program compile and run as intended.

These tasks cover a range of fundamental skills. Now, remember to practice writing html/css, designing schemas, writing sql queries, and coding c programs (especially focusing on loops for patterns) to prepare effectively.
