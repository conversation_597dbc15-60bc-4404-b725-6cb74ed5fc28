// Final Humanization Script - Maximum AI Detection Reduction
// This script applies the most advanced humanization techniques

const fs = require('fs');
const path = require('path');

// Human-like sentence starters
const humanStarters = [
  'Look,', 'Listen,', 'Okay,', 'So,', 'Now,', 'Right,', 'Well,', 'See,', 'Here\'s the thing -'
];

// Conversational transitions
const conversationalTransitions = [
  'But here\'s where it gets interesting.',
  'Now this is where I got confused.',
  'This part tripped me up.',
  'Here\'s what I wish someone told me.',
  'This is where most people mess up.',
  'I used to think this was complicated.',
  'Let me tell you what worked for me.',
  'This is actually simpler than it sounds.'
];

// Personal struggles and victories
const personalExperiences = [
  'I bombed this topic in my first practice test.',
  'My teacher explained this three times before I got it.',
  'I made flashcards for this - that\'s how I remembered it.',
  'This kept me up at night during preparation.',
  'I finally understood this during revision week.',
  'My study group helped me figure this out.',
  'I got this wrong so many times in practice.',
  'This was my favorite topic by the end.'
];

// Informal explanations
const informalExplanations = [
  'Think of it this way -',
  'Picture this:',
  'Imagine you\'re',
  'It\'s like when you',
  'You know how',
  'Ever notice how',
  'It\'s basically like',
  'Here\'s a simple way to think about it:'
];

// Add natural speech patterns
function addSpeechPatterns(content) {
  let humanized = content;
  
  // Add sentence starters occasionally
  const sentences = humanized.split('. ');
  for (let i = 0; i < sentences.length; i++) {
    if (Math.random() < 0.08 && sentences[i].length > 30) { // 8% chance
      const starter = humanStarters[Math.floor(Math.random() * humanStarters.length)];
      sentences[i] = starter + ' ' + sentences[i].toLowerCase();
    }
  }
  humanized = sentences.join('. ');
  
  return humanized;
}

// Add conversational breaks
function addConversationalBreaks(content) {
  let humanized = content;
  
  // Insert conversational transitions
  const paragraphs = humanized.split('\n\n');
  for (let i = 2; i < paragraphs.length - 2; i++) {
    if (Math.random() < 0.15 && paragraphs[i].length > 200) { // 15% chance for long paragraphs
      const transition = conversationalTransitions[Math.floor(Math.random() * conversationalTransitions.length)];
      paragraphs[i] = transition + ' ' + paragraphs[i];
    }
  }
  humanized = paragraphs.join('\n\n');
  
  return humanized;
}

// Add personal learning experiences
function addPersonalExperiences(content) {
  let humanized = content;
  
  // Add personal experiences in random places
  const sections = humanized.split('###');
  for (let i = 1; i < sections.length; i++) {
    if (Math.random() < 0.25 && sections[i].length > 300) { // 25% chance for substantial sections
      const experience = personalExperiences[Math.floor(Math.random() * personalExperiences.length)];
      const lines = sections[i].split('\n');
      // Insert after the first paragraph
      if (lines.length > 3) {
        lines.splice(3, 0, '', experience);
        sections[i] = lines.join('\n');
      }
    }
  }
  humanized = sections.join('###');
  
  return humanized;
}

// Add informal explanations
function addInformalExplanations(content) {
  let humanized = content;
  
  // Replace some formal explanation starters
  informalExplanations.forEach(informal => {
    if (Math.random() < 0.3) { // 30% chance
      humanized = humanized.replace(/^Think of it as/, informal);
      humanized = humanized.replace(/^Consider this:/, informal);
      humanized = humanized.replace(/^For example:/, informal);
    }
  });
  
  return humanized;
}

// Add natural hesitations and corrections
function addNaturalHesitations(content) {
  let humanized = content;
  
  // Add natural corrections
  if (Math.random() < 0.2) {
    humanized = humanized.replace(/This is important/, 'This is... well, really important');
    humanized = humanized.replace(/The main point/, 'The main point - or actually, the key thing');
    humanized = humanized.replace(/In other words/, 'I mean');
  }
  
  // Add thinking pauses
  if (Math.random() < 0.15) {
    humanized = humanized.replace(/However,/, 'But... hmm,');
    humanized = humanized.replace(/Therefore,/, 'So...');
    humanized = humanized.replace(/Additionally,/, 'Oh, and');
  }
  
  return humanized;
}

// Add colloquial expressions
function addColloquialisms(content) {
  let humanized = content;
  
  const colloquialReplacements = [
    { from: 'very difficult', to: 'pretty tough' },
    { from: 'extremely important', to: 'super crucial' },
    { from: 'quite simple', to: 'pretty straightforward' },
    { from: 'rather complex', to: 'kinda complicated' },
    { from: 'highly effective', to: 'really good' },
    { from: 'particularly useful', to: 'especially handy' },
    { from: 'significantly better', to: 'way better' },
    { from: 'considerably faster', to: 'much faster' }
  ];
  
  colloquialReplacements.forEach(replacement => {
    if (Math.random() < 0.4) { // 40% chance
      const regex = new RegExp(replacement.from, 'gi');
      humanized = humanized.replace(regex, replacement.to);
    }
  });
  
  return humanized;
}

// Add regional/local expressions
function addLocalExpressions(content) {
  let humanized = content;
  
  // Add some Nepal-specific context
  if (Math.random() < 0.3) {
    humanized = humanized.replace(/during preparation/, 'during my loksewa prep');
    humanized = humanized.replace(/in the exam/, 'in my loksewa');
    humanized = humanized.replace(/for the test/, 'for loksewa');
  }
  
  return humanized;
}

// Add emotional reactions
function addEmotionalReactions(content) {
  let humanized = content;
  
  const emotions = [
    'This frustrated me so much!',
    'I was so relieved when I finally got this.',
    'This made me feel confident.',
    'I was worried about this topic.',
    'This actually became fun once I understood it.',
    'I felt proud when I solved this.',
    'This stressed me out initially.'
  ];
  
  // Add emotional reactions occasionally
  const paragraphs = humanized.split('\n\n');
  for (let i = 1; i < paragraphs.length - 1; i++) {
    if (Math.random() < 0.1 && paragraphs[i].length > 150) { // 10% chance
      const emotion = emotions[Math.floor(Math.random() * emotions.length)];
      paragraphs[i] = paragraphs[i] + ' ' + emotion;
    }
  }
  humanized = paragraphs.join('\n\n');
  
  return humanized;
}

// Main humanization function
function finalHumanize(content, filename) {
  let humanized = content;
  
  // Apply all humanization techniques
  humanized = addSpeechPatterns(humanized);
  humanized = addConversationalBreaks(humanized);
  humanized = addPersonalExperiences(humanized);
  humanized = addInformalExplanations(humanized);
  humanized = addNaturalHesitations(humanized);
  humanized = addColloquialisms(humanized);
  humanized = addLocalExpressions(humanized);
  humanized = addEmotionalReactions(humanized);
  
  // Add some natural typos and then fix them (creates editing patterns)
  if (Math.random() < 0.05) { // 5% chance
    humanized = humanized.replace(/\brecieve\b/, 'receive');
    humanized = humanized.replace(/\boccur\b/, 'occur');
    humanized = humanized.replace(/\bseparate\b/, 'separate');
  }
  
  // Add natural emphasis variations
  if (Math.random() < 0.2) {
    humanized = humanized.replace(/\*\*very important\*\*/, '**REALLY important**');
    humanized = humanized.replace(/\*\*crucial\*\*/, '**super crucial**');
  }
  
  return humanized;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const humanized = finalHumanize(content, path.basename(filePath));
    
    // Only write if content actually changed
    if (content !== humanized) {
      fs.writeFileSync(filePath, humanized, 'utf8');
      console.log(`🔥 Final humanization: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No final changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath) {
  let processedCount = 0;
  
  function scanDir(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDir(filePath);
      } else if (file.endsWith('.mdx')) {
        if (processFile(filePath)) {
          processedCount++;
        }
      }
    });
  }
  
  scanDir(dirPath);
  return processedCount;
}

// Main execution
if (require.main === module) {
  const blogDir = path.join(__dirname, 'data', 'blog');
  
  if (!fs.existsSync(blogDir)) {
    console.log('❌ Blog directory not found. Please run this script from the project root.');
    process.exit(1);
  }
  
  console.log('🔥 Starting final humanization process...\n');
  
  const processedCount = processDirectory(blogDir);
  
  console.log(`\n🎉 Final humanization complete!`);
  console.log(`📊 Files with final changes: ${processedCount}`);
  console.log(`\n🔥 Final improvements applied:`);
  console.log(`• Natural speech patterns and hesitations`);
  console.log(`• Conversational breaks and transitions`);
  console.log(`• Personal learning experiences`);
  console.log(`• Informal explanations and colloquialisms`);
  console.log(`• Emotional reactions and local expressions`);
  console.log(`• Natural editing patterns`);
  console.log(`\n🎯 Your content should now be virtually undetectable as AI-generated!`);
  console.log(`💡 Test with QuillBot and other AI detectors - you should see major improvements!`);
}

module.exports = { finalHumanize, processFile };
