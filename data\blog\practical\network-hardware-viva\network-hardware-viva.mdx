---
title: network and hardware  viva
date: '2025-05-18'
tags: ['practical']
draft: false
summary: network and hardware related viva
---

### questions you'll face in practical viva

based on my experience and what i've heard from other candidates, here are the types of questions that keep coming up:

**network troubleshooting questions:**

"you used `ping *******` successfully. What does this tell you about your network connection?".

**Hardware Basics:**

"What's the difference between an HDD and an SSD?" (This one's super common!).

- "Why is it important to align the notch on a RAM module with the key in the motherboard slot?"

- **Sample Question 4:** "You configured a static IP address. What is the alternative, and what provides that service on most networks?"

**Sample Question 5:** "You used `tracert google.com`. What information does each 'hop' shown in the output represent?".

- **Sample Question 6:** "In the hardware section, you identified the CPU and RAM. What is the fundamental difference in how they handle data?"

**Sample Question 7:** "Why might you choose to assign a static IP address to a device like a server or a network printer, instead of letting it get an address from DHCP?".

- **Sample Question 8:** "What is the purpose of the CMOS battery on the motherboard?"

### Answers

**Sample Answer 1:** "A successful ping to ******* (a public Google DNS server) indicates several things: my computer's local IP configuration (IP, subnet) is likely correct; my computer can reach the default gateway; the default gateway (router) is functioning and routing traffic towards the internet; and there is a functional path through the internet from my network to Google's server and back. It confirms basic internet connectivity beyond my local network.".

This is where most people mess up. - **Sample Answer 2:** "An HDD (Hard Disk Drive) is a traditional storage device that uses spinning magnetic platters and a moving read/write head to store data mechanically. An SSD (Solid State Drive) uses flash memory chips (similar to RAM but non-volatile) to store data electronically, with no moving parts. SSDs are significantly faster, more durable, quieter, and consume less power than HDDs, but are typically more expensive per gigabyte." I used to mix this up all the time.

- **Sample Answer 3:** "The notch and key system ensures the RAM module is installed in the correct orientation. Different types of RAM (like DDR3, DDR4, DDR5) have notches in different positions, preventing incompatible modules from being inserted, which could damage both the RAM and the motherboard. It also ensures the electrical contacts align properly."

**Sample Answer 4:** "The alternative to a static IP address is a dynamic IP address. Dynamic IP addresses are automatically assigned to devices when kind of they connect to the network by a DHCP (Dynamic Host Configuration Protocol) server. On most home or small office networks, the router acts as the DHCP server.".

**Sample Answer 5:** "Each hop represents a router or layer 3 switch that the network packets pass through on their way from the source computer to the destination (`google.com`). It shows the IP address (and sometimes hostname) of each router and the round-trip time it took for packets to reach that router and return. Okay, it helps identify where delays or connection failures might be occurring along the path.".

- **sample answer 6:** "the cpu (central processing unit) is the 'brain' that actively processes instructions and performs calculations on data. RAM (Random Access Memory) is volatile working memory; it well, temporarily holds the data and instructions that the CPU needs to access quickly while the computer is running. Data in RAM is lost when power is turned off, whereas the CPU doesn't store data long-term, it just processes it."

- **Sample Answer 7:** "Servers and printers often need to be reliably accessible by other devices on the network using a fixed IP address. If their address changed dynamically (via DHCP), client computers honestly, or other services trying to connect to them would lose connection whenever the IP changed. A static IP ensures the address remains constant, making connections stable and configuration easier for services that well, depend on reaching that device."

- **Sample Answer 8:** "The CMOS battery provides power to a small memory chip (CMOS RAM) on the motherboard that stores the BIOS/UEFI settings. These settings include things like the system date and time, boot order, and hardware configuration details. The battery ensures these settings are retained even when the computer is unplugged from the main power."
