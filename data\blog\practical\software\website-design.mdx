---
title: Software - Website Design
date: '2025-03-07'
tags: ['practical']
draft: false
summary: About website design
---

### **Website Design**

This section requires practical application of web design

**Sample Task 1:** Create a simple HTML page for a "Book Information" system. The page should include:.

But here's where it gets interesting.   - A main heading "Book Details".
  - A form with fields for "Book Title" (text input), "Author Name" (text input), "Publication Year" (number input), and a "Submit" button.
  - Apply basic CSS to:
    - Center the main heading.
    - Give the form a light grey background and some padding.
    - Style the button with a blue background and white text.

**Sample Task 2: Enhance Form & Add Basic Layout**.

  - **Task:** Modify the "Book Information" HTML page. Add a `<label>` and a `<textarea>` for "Book Synopsis". Add a dropdown (`<select>`) for "Genre" with options: "Fiction", "Science Fiction", "Mystery", "Non-Fiction". Okay, using css, make the labels display inline with their input fields (instead of above) and ensure the form elements align reasonably well. Give the `textarea` a fixed height.
  - **Skills Tested:** More form elements (`textarea`, `select`, `option`), CSS for layout (`display: inline-block` or basic `flexbox` on label/input pairs), styling specific elements.

**Sample Task 3: Create a Simple Navigation Menu**.

  - **Task:** Create an HTML page with a horizontal navigation bar at the top. The bar should contain links for "Home", "About Us", and "Contact". Use an unordered list (`<ul>`) kind of inside a `<nav>` element for the links. Apply CSS to:
    - Remove list bullets.
    - Make the list items display horizontally.
    - Give the `<nav>` bar a dark background color and white text color for the links.
    - Add padding around the links.
    - Add a hover effect where the background color of the link changes slightly when the mouse hovers over it.
  - **Skills Tested:** Semantic HTML (`<nav>`), list styling (`list-style-type`), horizontal layout (`display: inline-block` or `flex`), basic link styling (`a` tag), hover pseudo-class. This stressed me out initially.

- **Sample Task 4: Basic Image and Text Styling**

  - **Task:** Create an HTML page displaying an image (`<img src="placeholder.jpg" alt="Placeholder Image">`) and a paragraph (`<p>`) of descriptive text beside it. Apply CSS to:
    - Make the image float to the left of the text.
    - Add a margin around the image so text doesn't touch it.
    - Give the image a thin solid border.
    - Set the `line-height` of the paragraph text for better readability.
  - **Skills Tested:** Image tag (`<img>`), `float` property (or basic flex/grid), `margin`, `border`, `line-height`.

**Sample Answer of Task 1 Code (HTML - `book_info.html`):**.

  ```html
  <!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Book Information</title>
      <link rel="stylesheet" href="style.css" />
    </head>
    <body>
      <h1>Book Details</h1>
      <form action="#" method="post" id="bookForm">
        <label for="title">Book Title:</label><br />
        <input type="text" id="title" name="title" required /><br /><br />

        <label for="author">Author Name:</label><br />
        <input type="text" id="author" name="author" required /><br /><br />

        <label for="year">Publication Year:</label><br />
        <input type="number" id="year" name="year" min="1000" max="2025" required /><br /><br />
        <button type="submit">Submit Book</button>
      </form>
    </body>
  </html>
  ``` This was my weak point during prep. This made me feel confident.

- **Sample Answer Code (CSS - `style.css`):**

  ```css
  body {
    font-family: sans-serif;
  }

  h1 {
    text-align: center;
    color: #333;
  }

  #bookForm {
    background-color: #f2f2f2;
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
    margin: 20px auto; /* Centers the form */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }

  input[type='text'],
  input[type='number'] {
    width: calc(100% - 12px); /* Adjust width considering padding */
    padding: 6px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  button {
    background-color: #007bff; /* Blue background */
    color: white; /* White text */
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
  } This is easier than it looks.

  button:hover {
    background-color: #0056b3; /* Darker blue on hover */
  }
  ```
