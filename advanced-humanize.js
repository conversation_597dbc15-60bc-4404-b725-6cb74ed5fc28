// Advanced Content Humanization Script
// This script applies deeper humanization to reduce AI detection scores

const fs = require('fs');
const path = require('path');

// Advanced AI patterns to replace
const advancedAIPatterns = {
  // Overly structured introductions
  'This topic frequently appears in loksewa computer engineering exams.': [
    'I\'ve seen this come up in multiple loksewa exams.',
    'This showed up in my loksewa exam too.',
    'Expect this in your loksewa - it\'s a common topic.',
    'This topic keeps appearing in loksewa papers.',
    'I remember this being tested in my loksewa exam.'
  ],
  
  // Perfect transitions
  'Let me share what I\'ve learned.': [
    'Here\'s what I figured out.',
    'Let me break this down for you.',
    'Here\'s my take on it.',
    'This is how I finally understood it.',
    'Let me explain it the way that worked for me.'
  ],
  
  // Overly formal explanations
  'Here\'s how I understand it:': [
    'The way I see it:',
    'My understanding is:',
    'I think of it like this:',
    'From what I learned:',
    'The way it clicked for me:'
  ],
  
  // Perfect study tips
  'Study Tips from My Experience': [
    'What Helped Me Learn This',
    'My Study Notes',
    'Tips That Worked for Me',
    'How I Remembered This Stuff',
    'My Preparation Strategy'
  ],
  
  // Repetitive exam sections
  'Loksewa Exam Focus: Questions I\'ve Encountered': [
    'Common Loksewa Questions',
    'What They Actually Ask in Exams',
    'Questions from My Experience',
    'Exam Patterns I Noticed',
    'Real Loksewa Questions'
  ],
  
  // Perfect conclusions
  'Pro tip from my experience:': [
    'One thing I learned the hard way:',
    'Something that really helped me:',
    'Here\'s what I wish I knew earlier:',
    'My biggest takeaway:',
    'What finally made it click:'
  ]
};

// Casual filler words and phrases to add natural flow
const casualFillers = [
  'you know,', 'I mean,', 'basically,', 'honestly,', 'actually,', 
  'pretty much', 'kind of', 'sort of', 'anyway,', 'well,'
];

// Imperfect language patterns
const imperfections = [
  { from: 'very important', to: 'super important' },
  { from: 'extremely', to: 'really' },
  { from: 'significantly', to: 'way' },
  { from: 'particularly', to: 'especially' },
  { from: 'subsequently', to: 'then' },
  { from: 'consequently', to: 'so' },
  { from: 'therefore', to: 'that\'s why' },
  { from: 'however', to: 'but' },
  { from: 'nevertheless', to: 'still' },
  { from: 'furthermore', to: 'also' }
];

// Personal interjections to add throughout content
const personalInterjections = [
  'I remember struggling with this part.',
  'This confused me for weeks!',
  'Honestly, this took me forever to get.',
  'I used to mix this up all the time.',
  'This was my weak point during prep.',
  'I almost got this wrong in my exam.',
  'My friend helped me understand this.',
  'I found a trick that really works.',
  'This is easier than it looks.',
  'Don\'t overthink this one.'
];

// Specific loksewa years and contexts
const loksewaContexts = [
  'loksewa 2078',
  'loksewa 2079', 
  'loksewa 2080',
  'my loksewa exam',
  'during my preparation',
  'when I was studying',
  'in my practice tests',
  'from past papers'
];

function addCasualLanguage(content) {
  let humanized = content;
  
  // Add casual fillers randomly
  const sentences = humanized.split('. ');
  for (let i = 0; i < sentences.length; i++) {
    if (Math.random() < 0.15 && sentences[i].length > 50) { // 15% chance for longer sentences
      const filler = casualFillers[Math.floor(Math.random() * casualFillers.length)];
      const words = sentences[i].split(' ');
      const insertPos = Math.floor(words.length / 3) + Math.floor(Math.random() * 3);
      words.splice(insertPos, 0, filler);
      sentences[i] = words.join(' ');
    }
  }
  humanized = sentences.join('. ');
  
  return humanized;
}

function addPersonalTouches(content) {
  let humanized = content;
  
  // Add personal interjections in random places
  const paragraphs = humanized.split('\n\n');
  for (let i = 1; i < paragraphs.length - 1; i++) {
    if (Math.random() < 0.2 && paragraphs[i].length > 100) { // 20% chance for substantial paragraphs
      const interjection = personalInterjections[Math.floor(Math.random() * personalInterjections.length)];
      paragraphs[i] = paragraphs[i] + ' ' + interjection;
    }
  }
  humanized = paragraphs.join('\n\n');
  
  return humanized;
}

function addSpecificLoksewaReferences(content) {
  let humanized = content;
  
  // Replace generic "loksewa" with specific years occasionally
  const loksewaMatches = humanized.match(/\bloksewa\b/gi);
  if (loksewaMatches && loksewaMatches.length > 2) {
    // Replace 30% of generic "loksewa" with specific contexts
    let replacementCount = Math.floor(loksewaMatches.length * 0.3);
    for (let i = 0; i < replacementCount; i++) {
      const specificContext = loksewaContexts[Math.floor(Math.random() * loksewaContexts.length)];
      humanized = humanized.replace(/\bloksewa\b/, specificContext);
    }
  }
  
  return humanized;
}

function addImperfections(content) {
  let humanized = content;
  
  // Apply imperfect language patterns
  imperfections.forEach(pattern => {
    if (Math.random() < 0.4) { // 40% chance to apply each pattern
      const regex = new RegExp(`\\b${pattern.from}\\b`, 'gi');
      humanized = humanized.replace(regex, pattern.to);
    }
  });
  
  return humanized;
}

function varyStructure(content) {
  let humanized = content;
  
  // Vary bullet point structure
  const lines = humanized.split('\n');
  let consecutiveBullets = 0;
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('- ')) {
      consecutiveBullets++;
      
      // Every 4th bullet point, consider converting to paragraph
      if (consecutiveBullets % 4 === 0 && Math.random() < 0.3) {
        lines[i] = lines[i].replace(/^- /, '') + '.';
      }
    } else {
      consecutiveBullets = 0;
    }
  }
  
  humanized = lines.join('\n');
  return humanized;
}

function advancedHumanize(content, filename) {
  let humanized = content;
  
  // Apply advanced pattern replacements
  Object.keys(advancedAIPatterns).forEach(pattern => {
    if (humanized.includes(pattern)) {
      const replacements = advancedAIPatterns[pattern];
      const randomReplacement = replacements[Math.floor(Math.random() * replacements.length)];
      humanized = humanized.replace(pattern, randomReplacement);
    }
  });
  
  // Apply various humanization techniques
  humanized = addCasualLanguage(humanized);
  humanized = addPersonalTouches(humanized);
  humanized = addSpecificLoksewaReferences(humanized);
  humanized = addImperfections(humanized);
  humanized = varyStructure(humanized);
  
  // Add some typos and then fix them (creates natural editing patterns)
  if (Math.random() < 0.1) { // 10% chance
    humanized = humanized.replace(/\bthe the\b/, 'the');
    humanized = humanized.replace(/\band and\b/, 'and');
  }
  
  // Add occasional informal punctuation
  humanized = humanized.replace(/\. This is/g, '. This is');
  if (Math.random() < 0.2) {
    humanized = humanized.replace(/\. This is/, '... this is');
  }
  
  // Add occasional emphasis
  if (Math.random() < 0.3) {
    humanized = humanized.replace(/really important/, '*really* important');
    humanized = humanized.replace(/very crucial/, '*very* crucial');
  }
  
  return humanized;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const humanized = advancedHumanize(content, path.basename(filePath));
    
    // Only write if content actually changed
    if (content !== humanized) {
      fs.writeFileSync(filePath, humanized, 'utf8');
      console.log(`🎯 Advanced humanization: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No advanced changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath) {
  let processedCount = 0;
  
  function scanDir(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDir(filePath);
      } else if (file.endsWith('.mdx')) {
        if (processFile(filePath)) {
          processedCount++;
        }
      }
    });
  }
  
  scanDir(dirPath);
  return processedCount;
}

// Main execution
if (require.main === module) {
  const blogDir = path.join(__dirname, 'data', 'blog');
  
  if (!fs.existsSync(blogDir)) {
    console.log('❌ Blog directory not found. Please run this script from the project root.');
    process.exit(1);
  }
  
  console.log('🎯 Starting advanced humanization process...\n');
  
  const processedCount = processDirectory(blogDir);
  
  console.log(`\n🎉 Advanced humanization complete!`);
  console.log(`📊 Files with advanced changes: ${processedCount}`);
  console.log(`\n🎯 Advanced improvements applied:`);
  console.log(`• Varied language patterns and transitions`);
  console.log(`• Added casual language and fillers`);
  console.log(`• Inserted personal interjections`);
  console.log(`• Specific loksewa year references`);
  console.log(`• Structural variations`);
  console.log(`• Imperfect language patterns`);
  console.log(`\n💡 Your content should now score much lower on AI detection tools!`);
}

module.exports = { advancedHumanize, processFile };
