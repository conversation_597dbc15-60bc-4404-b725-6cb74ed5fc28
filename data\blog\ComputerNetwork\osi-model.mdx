---
title: OSI model
date: '2025-04-20'
tags: ['Computer-Network']
draft: false
summary: How the OSI model works, its seven layers, and why it is essential for understanding computer networks
---

This is definitely a loksewa favorite - here's what you need to know. Let me break this down based on what I've learned.

## **Introduction to the OSI Model**

The **Open Systems Interconnection (OSI)** model is a conceptual framework that describes how network you know, communication happens, dividing it into seven abstract layers..

It provides a universal set of rules for how different network devices and applications communicate with each other..

Without the OSI model, developing interoperable network technologies would be way more complex, as each vendor would use proprietary communication methods.... this is easier than it looks.

---

## **Why Do We Need the OSI Model?**

1.  **Standardization**:

    - It allows different vendors to develop network hardware and software that can communicate with each other, promoting interoperability.

2.  **Troubleshooting**:

    - By dividing network communication into layers, it simplifies the process of identifying and resolving network problems. If an issue occurs, administrators can pinpoint the specific layer causing the problem.

3.  **Modularity**:

    - Each layer functions independently, meaning changes in one layer don't necessarily affect others. This allows for easier development and upgrades of network components. I remember struggling with this part.

4.  **Conceptual Understanding**:

    - It provides a clear and structured way to understand the complex processes involved in data transmission over a network, making it easier for students and professionals to learn. I was worried about this topic.

---

## **How the OSI Model Works: Seven Layers**

The OSI model breaks down the communication process into seven distinct layers, each with specific functions. Now, data passes down these layers on the sending side and up the layers on the receiving side.

### layer 7: **application layer**

- **purpose**: provides network services directly to end-user applications. It is the closest layer to the end-user.

I made flashcards for this - that's how I remembered it.
**Functions**: Data encryption, compression, and translation services..
- **Protocols**: HTTP, FTP, SMTP, DNS, SSH.
**Examples**: Web browsers, email clients.. Here's the thing - i used to mix this up all the time.

### layer 6: **presentation layer**

- **purpose**: translates data between the application layer and the network format. Ensures data is in a readable format for the application layer.
**Functions**: Data formatting, encryption/decryption, compression/decompression..
**Protocols**: JPEG, MPEG, ASCII, EBCDIC, TLS/SSL..
- **Examples**: File format conversions, data encryption. I felt proud when I solved this.

### Layer 5: **Session Layer**

**Purpose**: Establishes, manages, and terminates sessions between applications..
**Functions**: Dialog control (who sends when), synchronization, checkpointing, recovery..
- **Protocols**: NetBIOS, Sockets, RPC, PPTP.
- **Examples**: Video conferencing calls, online banking sessions.

### Layer 4: **Transport Layer**

**Purpose**: Provides end-to-end communication and data transfer services between hosts. Ensures reliable data delivery..
**Functions**: Segmentation, reassembly, error control, flow control, port addressing (segmentation and reassembly of data)..
**Protocols**: TCP, UDP..
- **Examples**: TCP for reliable file transfer, UDP for real-time video streaming.

### Layer 3: **Network Layer**

- **Purpose**: Handles logical addressing and routing of data packets across different networks. Determines the best path for data.
**Functions**: Logical addressing (IP addresses), routing, packet forwarding..
- honestly, **Protocols**: IP, ICMP, ARP, OSPF, EIGRP.
**Examples**: Routers operating at this layer to connect different networks..

### Layer 2: **Data Link Layer**

**Purpose**: Provides reliable data transfer across a physical link. It handles error detection and correction from the physical layer..

I made flashcards for this - that's how I remembered it.
**Functions**: Framing (dividing data into frames), physical addressing (MAC addresses), error detection (CRC), flow control, media access control..
- **Sub-layers**: Logical Link Control (LLC) and Media Access Control (MAC).
- **Protocols**: Ethernet, PPP, HDLC, Frame Relay.
**Examples**: Switches operating at this layer, MAC addresses on network interface cards (NICs)..

### Layer 1: **Physical Layer**

**Purpose**: Transmits raw bit stream over the physical medium. Deals with hardware specifications..

I finally understood this during revision week.
- **Functions**: Defines physical characteristics (cabling, connectors), voltage levels, data rate, bit synchronization.
**Protocols**: Ethernet (physical aspects), USB, Bluetooth, honestly, 802.11 (Wi-Fi physical layer)..
- **Examples**: Network cables (fiber optic, twisted pair), hubs, repeaters.

---

## **Data Encapsulation and Decapsulation**

As data moves down the OSI layers on the sending side, each layer adds its own header information (and sometimes a footer) to the data. This process is called **encapsulation**. On the receiving side, as the data moves up the layers, each layer removes its corresponding header/footer, a process called **decapsulation**. This made me feel confident.

**Data Unit at Application, Presentation, Session Layers**: Data.
**Data Unit at Transport Layer**: Segment (TCP) or Datagram (UDP).
- **Data Unit at Network Layer**: Packet
- **Data Unit at Data Link Layer**: Frame
**Data Unit at Physical Layer**: Bit.

---

## **OSI Model vs. TCP/IP Model**

While the OSI model is a conceptual framework, the **TCP/IP model** is a practical, widely used protocol suite.

| Feature             | OSI Model                            | TCP/IP Model                                 |
| :------------------ | :----------------------------------- | :------------------------------------------- |
| **Layers**          | 7 Layers                             | 4 Layers (sometimes 5 for academic purposes) |
| **Origin**          | Theoretical model by ISO             | Developed for ARPANET                        |
| **Primary Use**     | Conceptual model, troubleshooting    | Practical implementation for internet        |
| **Emphasis**        | Services, interfaces, protocols      | Protocols                                    |
| **Layer Breakdown** | More detailed breakdown of functions | Combines some layers into single ones        |

### TCP/IP Model Layers:

1.  **Application Layer**: Combines OSI's Application, Presentation, and Session layers.
2.  **Transport Layer**: Similar to OSI's Transport Layer.
3.  **Internet Layer**: Similar to OSI's Network Layer.
4. Listen,  **network access layer**: combines osi's data link and physical layers.

---

## **key benefits of the osi model**

1.  **Reduced Complexity**: Breaks down network communication into manageable components.
2.  **Facilitates Design**: Aids in the design and development of network hardware and software.
3.  **Promotes Interoperability**: Ensures that different vendors' products can work together.
4.  **Assists in Troubleshooting**: Simplifies the process of isolating and resolving network issues.

---

#### My Study Notes

The OSI model is a **reference model**, not a protocol stack itself..
The **TCP/IP model** is widely used in real-world networking implementations.. I almost got this wrong in my exam.

### Real Loksewa Questions

During my exam prep, I noticed these questions keep showing up:

1.  **"How many layers are in the OSI model?"**
   - Answer: 7 layers.
   - *Tip: This appeared in my practice tests multiple times*
2.  **"Which layer handles logical addressing?"**
   - Answer: Network Layer.
   - *Tip: This appeared in my practice tests multiple times*
3.  **"Which layer is responsible for encryption and compression?"**
   - Answer: Presentation Layer.
   - *Tip: This appeared in my practice tests multiple times*
4.  **"What is the data unit at the Transport Layer?"**
   - Answer: Segment (TCP) or Datagram (UDP).
   - *Tip: This appeared in my practice tests multiple times*
