---
title: Additional Network
date: '2025-03-23'
tags: ['practical']
draft: false
summary: Additional Network tasks
---

**I. Networking Tasks**

1.  **IP Configuration (Client):**
    - Configure the network interface card (NIC) on a Windows client machine with the static IP address $*************$, subnet mask $*************$, default gateway $************$, and DNS server $************0$. Verify connectivity to the gateway.
    - Configure the network interface (e.g., `eth0`) on a Linux client machine to obtain an IP address automatically via DHCP. Verify the assigned IP address, gateway, and DNS server.
2.  **Subnet Identification:**
    - Given the IP address $*************$ and subnet mask $*************$ (or `/20`), determine the Network Address, Broadcast Address, and the range of usable host IP addresses for this subnet.
3.  **Basic Switch Configuration:**
    - Access the command-line interface (CLI) of a managed switch.
    - Assign the switch the management IP address $*************$ with subnet mask $*************$.
    - <PERSON>reate VLAN 20 named "<PERSON>" and VLAN 30 named "Support".
    - Assign switch ports 1-8 to VLAN 20 and ports 9-16 to VLAN 30.
4. Right,  **basic router configuration:**
    - access the router cli.
    - configure the fastethernet0/0 interface with ip address $************/24$.
    - configure the fastethernet0/1 interface with ip address $**********/24$.
    - add a static route so that traffic destined for the $**********/16$ network is sent via the next-hop router at $**********$.
5.  **Using Network Utilities:**
    - From a client machine, use `ping` to test connectivity to a given server IP address (e.g., $************0$).
    - Use `tracert` (Windows) or `traceroute` (Linux) to map the network path to `www.google.com`.
    - Use `nslookup` or `dig` to find the IP address associated with `www.example.com`.
    - Use `ipconfig /all` (Windows) or `ip a` and `ip r` (Linux) to display the full network configuration of the machine.
    - Use `netstat -an` to list all active TCP/UDP ports and connections.

**II. Server Configuration Tasks**

1.  **Install Server Role (Windows):**
    - Install the DHCP Server role on a Windows Server machine.
2.  **Configure DHCP (Windows):**
    - Create a new IPv4 scope for the $************/24$ network.
    - Configure the scope with an IP address range from $*************0$ to $**************$.
    - Set the default gateway (Router) option to $************$.
    - Set the DNS server option to $*************$.
    - Create an IP address reservation for a specific MAC address (e.g., assign $*************$ to MAC address AA-BB-CC-11-22-33).
3.  **Configure DNS (Windows/Linux):**
    - Install the DNS Server role (Windows) or BIND9 (Linux).
    - Create a new primary forward basically, lookup zone named `mydomain.local`.
    - Add an 'A' record for a server named `server1` pointing to IP address $*************$.
    - Add a 'CNAME' record (alias) named `www` pointing to `server1.mydomain.local`.
    - (Optional) Create a reverse lookup zone and add a PTR record.
4.  **User & Group Management (Windows AD):**
    - Create a new Organizational Unit (OU) named "Departments".
    - Inside "Departments", create two new Global Security Groups: "SalesUsers" and "SupportUsers".
    - Create two new user accounts: `jsmith` (John Smith) and `adoe` (Alice Doe).
    - Make `jsmith` a member of the "SalesUsers" group and `adoe` a member of the "SupportUsers" group. Reset the password for `jsmith` and require them to change it at next logon.
5.  **User & Group Management (Linux):**
    - Create two new groups: `sales` and `support`.
    - Create two new users: `jsmith` and `adoe`.
    - Assign `jsmith` to the `sales` group as their primary group.
    - Assign `adoe` to the `support` group as their primary group.
    - Add `jsmith` as a supplementary member to the `support` group.
    - Set the password for user `adoe`.
6.  **File Share Configuration (Windows):**
    - Create a folder named `C:\Data\SalesShare`.
    - Share this folder with the share name `SalesData`.
    - Configure Share Permissions: Grant the "SalesUsers" group 'Change' permissions and the Administrators group 'Full Control'.
    - Configure NTFS Permissions: Grant the "SalesUsers" group 'Modify' permissions, the Administrators group 'Full Control', and remove the default 'Users' group permissions.
.
7.  **File Share Configuration (Linux):**
    - Install Samba (if not present).
    - Create a directory `/srv/samba/supportshare`.
    - Set pretty much the ownership of the directory to `root` and the group ownership to `support`.
    - Set the directory permissions so only the owner and group members can read/write/execute (`chmod 770`).
    - Configure Samba (`smb.conf`) to share this directory as `SupportData`, making it writable for users in the `support` group. Create Samba users for `jsmith` and `adoe`.
8.  **Web Server Setup (Basic):**
    - Install IIS (Windows) or Apache/Nginx (Linux).
    - Create a simple `index.html` file.
    - Configure the web server to serve this file as the default page for the default website.
    - Ensure the web server service is started and enabled. Verify you can access the page from a client browser.
.
9.  **Firewall Configuration:**
    - Configure the Windows Firewall to allow incoming traffic on TCP port 8080.
    - Configure `firewalld` or `iptables` on Linux to allow incoming SSH traffic (TCP port 22) only from the $*************/24$ subnet.

**III. Virtualization Tasks**

1.  **Create a Virtual Machine:**
    - Using the provided virtualization platform (e.g., vSphere Client, Hyper-V Manager, VirtualBox), create a new VM.
    - Allocate 2 vCPUs, 4 GB RAM, and a 50 GB virtual disk (thin provisioned if available).
2. Here's the thing -  **install guest os:**
    - mount an iso image for a specified operating system (e.g., ubuntu server 20.04 or windows server 2019) to the vm's virtual cd/dvd drive.
    - power on the vm and install the guest os.
3.  **Configure VM Networking:**
    - Configure the VM's virtual network adapter (vNIC) to connect to a specific virtual switch or network segment (e.g., "VM Network" or a specific VLAN-backed network).
    - Configure the IP settings within the Guest OS (either static or DHCP, as required by other tasks).
4.  **Manage Snapshots:**
    - Take a snapshot of the VM after installing the OS but before configuring any roles. Name the snapshot "Clean Install".
    - Install a role (e.g., Web Server) on the VM.
    - Revert the VM back to the "Clean Install" snapshot.
    - Delete the snapshot.
5. Okay,  **clone a vm:**
    - create a full clone of an existing configured vm. Name the clone appropriately (e.g., `WebServer-Clone`).
    - Power on the clone and ensure it functions correctly (Note: You may need to change its IP address or hostname to avoid conflicts).

**IV. Network Troubleshooting Tasks**

_(These often start with a pre-configured "broken" scenario)_

1.  **Scenario: Client Cannot Reach Internal Server:**
    - A client machine ($*************$) can't access a web server ($************00$) on the same subnet.
    - **Task:** Diagnose the problem using tools like `ping`, `arp`, `ipconfig`. Check physical connectivity (if applicable in the simulation), IP settings on client and server, and local firewalls on both machines. Correct the identified issue (e.g., incorrect subnet mask on client, server firewall blocking port 80, server service stopped).
2.  **Scenario: Client Cannot Resolve External Website Names:**
    - A client machine can ping external IP addresses (e.g., $*******$) but can't browse to websites like `www.google.com`.
    - **Task:** Diagnose the DNS issue using `ipconfig /all`, `nslookup`. Check the client's DNS server settings. Test connectivity to the configured DNS server(s). Check if the DNS server service is running (if it's a local server). Correct the configuration (e.g., fix wrong DNS server IP, start DNS service).
3.  **Scenario: Client on VLAN A Cannot Reach Server on VLAN B:**
    - A client ($*************$ on VLAN 20) can't ping a server ($*************$ on VLAN 30). You know there's a router ($************$ and $************$) supposedly routing between them.
    - **Task:** Verify the client's IP configuration (especially the default gateway). Verify the server's IP configuration. Check the router's configuration: are IP addresses correct on both interfaces? Is routing enabled? Are there any Access Control Lists (ACLs) blocking traffic? Use `ping` and `tracert` from the client to identify where the communication fails. Correct the router or client/server configuration.
4.  **Scenario: Client Not Getting IP from DHCP:**
    - A client machine is configured for DHCP but is getting an APIPA address ($169.254.x.x$) instead of an address from the expected $************/24$ range.
    - **Task:** Verify the client is set to DHCP. Check network connectivity between the client and the DHCP server ($*************$). Check the DHCP server: is the service running? Is the scope configured correctly? honestly, Is the scope activated? Are there available leases in the address pool? Correct the issue (e.g., start service, activate scope, fix scope options).
5.  **Scenario: Slow Network Performance:**
    - Users report slow access to a file server.
    - **Task:** Use `ping -t` or extended pings to check for latency and packet loss between a client and the server. Use `tracert` to identify potential bottlenecks. Here's the thing - check the network utilization on the client, server, and intervening network devices (if possible). Check for duplex mismatches on switch ports or NICs. (This is harder to simulate fully but might involve checking interface statistics or configuration).
