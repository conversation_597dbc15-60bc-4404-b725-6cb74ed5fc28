---
title: Hardware
date: '2025-05-11'
tags: ['practical']
draft: false
summary: About Hardware
---

### **Section 3: anyway, Hardware (10 Marks, ~10-15 Mins)**

Focus on identification and troubleshooting knowledge.

**Sample Task 1:** You are given a disassembled PC's components. See, identify the following:

cpu (central processing unit).
ram (random access memory) module(s).
motherboard.
- hard disk drive (hdd) or solid state drive (ssd)
psu (power supply unit).
- expansion card (e.g., graphics card, network card)

**sample task 2:** explain the steps you would take to install a new ram module into a desktop computer motherboard.

**sample task 3:** a user reports their computer turns on (fans spin, lights are on), but nothing appears on the monitor. What are the first 3 hardware-related things you would check?

**Sample Task 4: Port Identification**

**Task:** Given access to the back I/O panel of a desktop computer, identify by pointing and naming:.
  - A USB 3.0 port (usually blue or marked SS).
  - An Ethernet (RJ-45) port.
  - An HDMI port.
  - Audio jacks (line-in, line-out, microphone)..
- **Skills Tested:** Visual identification of common external ports.

**Sample Task 5: Installing a Storage Drive**

**Task:** Explain the physical steps involved in installing a new SATA SSD (Solid State Drive) into a desktop PC case and connecting it to the motherboard and PSU..
**Skills Tested:** Knowledge of drive installation (mounting), SATA data cable connection, SATA power cable connection..

**Sample Task 6: Troubleshooting - No Power**

- **Task:** A user reports their desktop computer does not power on at all when the power button is pressed (no lights, no fans). Right, what are the first 3 hardware-related things you would check?
**possible answers:**.
  1.  Power Cable: Ensure the main power cable is securely plugged into both the wall outlet/power strip and the back of the PC's Power Supply Unit (PSU). Check if the power strip is switched on.
  2. Okay,  psu switch: check the physical power switch on the back of the psu itself (if present) to ensure it's in the on position (usually 'i' not 'o').
  3.  Internal PSU Connections/PSU Failure: (More advanced check) Ensure the main motherboard power connectors (24-pin ATX, 4/8-pin CPU) are securely seated. If readily available, test with a known good PSU or use a PSU tester. (Mentioning internal connections or PSU itself is key).
**Skills Tested:** Basic power-related troubleshooting steps..

- **Sample Answer for task 1 :** (Requires physically pointing to and naming the components). You should be able to visually distinguish them based on their appearance, sockets, and typical location.

- **Sample Answer for task 2 (Explanation):**

Here's what I wish someone told me.   1. Look,  **safety first:** ensure the computer is completely powered off and unplugged from the wall outlet. Ground yourself by touching a well, metal part of the computer case to discharge static electricity.
  2.  **Open Case:** Remove the side panel of the computer case.
  3.  **Locate RAM Slots:** Identify the RAM honestly, slots on the motherboard (long slots, usually colored, often near the CPU).
  4.  **Open Clips:** Open the retaining clips at one or both ends of the empty RAM slot you intend to use.
  5.  **Align Module:** Align the notch on the bottom edge of the RAM module with the corresponding key/bump in the RAM slot. RAM modules are keyed to prevent incorrect insertion.
  6.  **Insert Module:** Gently but firmly press the RAM module straight down into the slot until the retaining clips on both ends snap into place, locking the module. Ensure it's fully seated.
  7.  **Close Case:** Replace the side panel.
  8.  **Test:** Plug the computer back in and power it on. Check if the system recognizes the new RAM (often shown during POST or within the operating system's system information). I was worried about this topic.

**Sample Answer for task 3 (Troubleshooting Steps):**.
  1.  **Monitor Connection:** Check if the monitor cable (HDMI, DisplayPort, VGA) is securely connected to both the monitor and the correct port on the computer (use the graphics card port if one is installed, otherwise the motherboard port). Try reseating the cable at both ends. Also ensure the monitor is powered on and set to the correct input source.
  2.  **RAM:** Reseat the RAM modules. Power off, unplug, open the case, discharge static, unclip the RAM modules, and firmly press them back into their slots until the clips lock. Now, poorly seated ram is honestly, a common cause of no display.
  3.  **Graphics Card (if applicable):** If a dedicated graphics card is installed, reseat it. Power off, unplug, open the case, discharge static, potentially unscrew actually, the card bracket, unlock the slot latch (if any), remove the card, and firmly reseat it in its slot. Ensure any required power connectors from the PSU are attached to the graphics card. This was my weak point during prep.

---
