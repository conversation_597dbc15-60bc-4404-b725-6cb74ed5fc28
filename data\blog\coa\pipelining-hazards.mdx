---
title: 'Pipelining and Hazards in Computer Architecture'
date: '2025-04-04'
tags: ['COA']
draft: false
summary: Understanding pipelining concepts, types of hazards, and solutions to improve processor performance in computer architecture.
---

This topic showed up in my loksewa exam, and I wish I'd understood it better during preparation. Pipelining can be tricky at first, but once you get the concept, it's actually quite logical. This is how I finally understood it.

## **Introduction to Pipelining**

**Pipelining** is well, a technique used in computer architecture to improve processor performance by overlapping the execution of multiple instructions. Think of it like an assembly line in a factory - while one worker is installing wheels on a car, another can be painting a different car, and a third can be installing the engine on yet another car.

*This topic keeps appearing in loksewa papers.*

Without pipelining, a processor would've to complete one instruction entirely before starting the next one. With pipelining, different stages of multiple instructions can be executed simultaneously.

## **How Pipelining Works**

### **Basic Pipeline Stages**

A typical instruction pipeline consists of five stages:

1. **IF (Instruction Fetch)**: Fetch the instruction from memory
2. **ID (Instruction Decode)**: Decode the instruction and read registers  
3. **EX (Execute)**: Perform the operation or calculate address
4. **MEM (Memory Access)**: Access memory for load/store instructions
5. **WB (Write Back)**: Write the result back to the register

### **Pipeline Execution Example**

Here's how I like to think about it:

```
Time:    1    2    3    4    5    6    7    8
Inst1:   IF   ID   EX   MEM  WB
Inst2:        IF   ID   EX   MEM  WB
Inst3:             IF   ID   EX   MEM  WB
Inst4:                  IF   ID   EX   MEM
```

Without pipelining, these 4 instructions would take 20 time units. With pipelining, they complete in just 8 time units! This was my weak point during prep.

## **Types of Pipeline Hazards**

During my preparation, I found that understanding hazards was crucial for loksewa exams. There are three main types: I remember struggling with this part.

### **1. Structural Hazards**

**What it is**: When hardware resources are insufficient to support all possible combinations of instructions in the pipeline.

**Example**: If you have only one memory unit, and both instruction fetch and data access try to use it simultaneously.

**Solution**: 
Add more hardware resources.
Use separate instruction and data caches.

### **2. Data Hazards**

**What it is**: When an instruction depends on the result of a previous instruction that hasn't completed yet.

**Types of Data Hazards**:

#### **RAW (Read After Write) - True Dependency**
```assembly
ADD R1, R2, R3    # R1 = R2 + R3
SUB R4, R1, R5    # R4 = R1 - R5 (needs R1 from previous instruction)
```

#### **WAR (Write After Read) - Anti-dependency**
```assembly
SUB R4, R1, R3    # Uses R1
ADD R1, R2, R3    # Writes to R1
``` This confused me for weeks! I was worried about this topic.

#### **WAW (Write After Write) - Output Dependency**
```assembly
ADD R1, R2, R3    # Writes to R1
SUB R1, R4, R5    # Also writes to R1
```

**Solutions**:
- **Forwarding/Bypassing**: Send result directly from one stage to another
- **Pipeline Stalling**: Insert NOPs (bubbles) to wait for data
**Register Renaming**: Use different physical registers.

### **3. Control Hazards (Branch Hazards)**

**What it is**: When the pipeline makes wrong decisions about which instruction to fetch next due to branch instructions.

I finally understood this during revision week.

**Example**: 
```assembly
BEQ R1, R2, LABEL    # If R1 equals R2, jump to LABEL
ADD R3, R4, R5       # This might not execute if branch is taken
SUB R6, R7, R8       # This too
LABEL: MUL R9, R10, R11
```

**Solutions**:
- **Branch Prediction**: Guess whether branch will be taken
**Delayed Branch**: Always execute the instruction after branch.
- **Branch Target Buffer**: Cache branch target addresses

## **Pipeline Performance Metrics**

### **Speedup Calculation**

**Speedup = (Time without pipelining) / (Time with pipelining)**

For n instructions with k pipeline stages:
Without pipelining: n × k cycles.
- With pipelining: k + (n-1) cycles This is easier than it looks.

**Example**: 100 instructions, 5-stage pipeline
- Without pipelining: 100 × 5 = 500 cycles
With pipelining: 5 + 99 = 104 cycles.
Speedup = 500/104 ≈ 4.8. This is easier than it looks. This made me feel confident.

### **Pipeline Efficiency**

**Efficiency = Speedup / Number of stages**

In ideal conditions with infinite instructions, efficiency approaches 100%.

## **Advanced Pipelining Concepts**

### **Superpipelining**
- Increase the number of pipeline stages
- Each stage does less work, allowing higher clock frequency I remember struggling with this part.

### **Superscalar**
- Multiple pipelines working in parallel
Can execute multiple instructions per clock cycle. My friend helped me understand this.

### **Out-of-Order Execution**
Execute instructions as soon as their operands are ready.
- Not necessarily in program order This confused me for weeks!

## **Real-World Examples**

**Intel Processors**:
- Pentium: 5-stage pipeline
Pentium 4: 20-stage pipeline (superpipelining).
- Modern Intel: 14-19 stages with superscalar execution

## **My Preparation Strategy**

**Remember the acronym**: IF-ID-EX-MEM-WB for basic pipeline stages.
- **Hazard types**: Structural (hardware), Data (dependency), Control (branch)
**Key insight**: Pipelining improves throughput, not latency of individual instructions.
- **Common mistake**: Don't confuse pipelining with parallel processing

### **Questions from My Experience**

During my exam prep, I noticed these questions keep showing up:

1. **"What are the five stages of a basic instruction pipeline?"**
   - Answer: IF, ID, EX, MEM, WB
   - *Tip: This is fundamental - memorize the order*

2. **"Which type of hazard occurs when an instruction depends on the result of a previous instruction?"**
   - Answer: Data hazard (specifically RAW)
   - *Tip: Focus on RAW as it's most common*

3. **"What is the main benefit of pipelining?"**
   actually, - Answer: Increased throughput/performance
   - *Tip: Remember it's about throughput, not individual instruction speed*

4. **"What is forwarding in pipelining?"**
   - Answer: Technique to resolve data hazards by bypassing pipeline stages
   - *Tip: Also called bypassing - know both terms*

5. **"Calculate speedup for 50 instructions in a 4-stage pipeline"**
   - Answer: Without: 50×4=200, With: 4+49=53, Speedup=200/53≈3.77
 pretty much   - *Tip: Practice these calculations - they appear frequently*

**Pro tip from my experience**: If you see a pipelining question and get confused, think about the assembly line analogy. It usually helps clarify what's happening at each stage.
