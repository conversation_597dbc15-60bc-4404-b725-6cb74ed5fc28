---
title: Network
date: '2025-04-29'
tags: ['practical']
draft: false
summary: About Network
---

### **Section 2: Network (10 Marks, ~15-20 Mins)**

Focus on practical configuration and troubleshooting commands.

**Sample Task 1:** Configure the primary network adapter on your computer (e.g., Windows) with the following static IP settings:.

  - IP Address: `*************`
  - Subnet Mask: `*************`
  - Default Gateway: `***********`
  - Preferred DNS Server: `*******`

**Sample Task 2: Using `nslookup`**.

Here's what I wish someone told me.   - **Task:** Use the `nslookup` command-line tool to:
    - Find the IP address(es) associated with a specific website (e.g., `www.github.com`).
    - Perform a reverse lookup: Find the domain name associated with a known IP address (e.g., `*******`).
  - **Skills Tested:** Using `nslookup` for forward and reverse DNS queries.

**Sample Task 3: Checking Network Connections**.

  - **Task:** Use the `netstat -an` command (or `netstat -ano` on Windows to see PIDs) and identify:
    - At least one established TCP connection (if any).
    - At least one listening port on your machine.
    - Briefly explain what "LISTENING" and "ESTABLISHED" mean in the context of TCP connections.
  - **Skills Tested:** Using `netstat`, interpreting basic `netstat` output, understanding TCP states.

- **Sample Task 4: Command-Line IP Configuration (Alternative)**

  - **Task:** Describe the commands you would use in the Windows Command Prompt (`netsh` interface ip set address...) or Linux terminal (`ip addr add...`, `ip route add...`, modifying `/etc/resolv.conf`) to achieve the same static IP configuration described in Sample Task 1. (You don't necessarily have to execute them if using a shared lab machine, but list the commands and parameters).
  - **Skills Tested:** Knowledge of command-line network configuration tools.

**Sample Answer for task 1 (Configuration Steps - Windows Example):**.

  1.  Open Control Panel -> Network and Internet -> Network and Sharing Center.
  2.  Click on "Change adapter settings" kind of on the left pane.
  3. Look,  right-click on your primary network connection (e.g., "ethernet" or "wi-fi") and select "properties".
  4.  Select "Internet Protocol Version kind of 4 (TCP/IPv4)" from the list and click "Properties".
  5.  Select the radio button "Use the following IP address:".
  6.  Enter `*************` for the IP address.
  7.  Enter `*************` for the Subnet you know, mask (it might auto-fill correctly).
  8.  Enter `***********` for the Default gateway.
  9.  Select the radio button "Use the following DNS server addresses:".
  10. Enter `*******` for the Preferred DNS server.
  11. Click "OK" on the TCP/IPv4 Properties window.
  12. Click "Close" on the adapter's Properties window. I remember struggling with this part.

- **Sample Task 2:** Use command-line tools to:

  - Verify the IP configuration you just set.
  - Test connectivity to the default gateway (`***********`).
  - Test connectivity to the public DNS server (`*******`).
  - Trace the route to `google.com`..

**Sample Answer (Commands - Windows Command Prompt):**.

  ```bash
  # Verify IP configuration
  ipconfig /all

  # Test connectivity to the gateway
  ping ***********

  # Test connectivity to the public DNS
  ping *******

  # Trace the route to google.com
  tracert google.com
  ```

  _(You would then need to interpret the output, e.g., check if `ipconfig` shows the correct settings, if `ping` replies are successful, and observe the hops in `tracert`)_ This was my weak point during prep.

- **Sample Task 3:** Explain the purpose of DHCP. Where would you typically configure DHCP server settings in a small office network?

**Sample Answer (Explanation):**.
  - **Purpose of DHCP:** DHCP (Dynamic Host Configuration Protocol) is a network management protocol used to automatically assign IP addresses and other network configuration parameters (like subnet mask, default gateway, DNS servers) to devices on a network. This simplifies network administration as manual configuration isn't needed for each device, and it prevents IP address conflicts.
  - **Configuration Location:** In a small office network, the DHCP server functionality is typically built into the main network router (the device connecting the office to the internet). You would usually configure the DHCP settings (like the range of IP addresses to assign, lease time, etc.) by logging into the router's web-based administration interface via a browser. This frustrated me so much!

---
